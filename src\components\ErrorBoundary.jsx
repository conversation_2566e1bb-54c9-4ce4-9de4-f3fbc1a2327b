'use client'

import React from 'react'
import { But<PERSON> } from './ui'

export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)

    // Define a state variable to track whether is an error or not
    this.state = { hasError: false }
  }
  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI

    return { hasError: true }
  }
  componentDidCatch(error, errorInfo) {
    // You can use your own error logging service here
    console.log({ error, errorInfo })
  }
  render() {
    // Check if the error is thrown
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className='inner-container w-full'>
          <div className='flex flex-col items-center justify-center py-16 border border-red-400 rounded-lg'>
            <h2>Oops, there is an error!</h2>
            <Button onClick={() => this.setState({ hasError: false })}>Try again?</Button>
          </div>
        </div>
      )
    }

    // Return children components in case of no error

    return this.props.children
  }
}
