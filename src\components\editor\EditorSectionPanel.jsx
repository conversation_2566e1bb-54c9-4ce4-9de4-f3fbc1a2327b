'use client'

import { ChevronR<PERSON>, Eye, EyeOff, GripVertical } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button, Text } from '@components'
import { useEditor } from '@hooks'
import { cn } from '@utils'

export const EditorSectionPanel = ({ children, isVisible }) => {
  return (
    <div
      className={cn(
        'relative flex items-center gap-3  border-b border-slate-200 transition-all select-none bg-white pl-4 lg:pl-5',
        isVisible ? 'hover:bg-slate-50' : ''
      )}>
      {children}
    </div>
  )
}

export const EditorSectionDetails = ({ onClick, section, isVisible }) => {
  const { title, description, icon, sectionName } = section
  const PanelIcon = icon
  const tc = useTranslations('Common')

  return (
    <Button
      variant='subtle'
      size='link'
      type='button'
      onClick={onClick}
      className={cn(
        'flex-1 flex items-center justify-start gap-3 px-0 py-4 [&_svg]:size-5 disabled:opacity-35 '
      )}
      disabled={!isVisible}>
      <span className='flex items-center justify-center bg-slate-100 rounded-[10px] p-1.5'>
        <PanelIcon className='text-slate-500' />
      </span>
      <div className='flex flex-col gap-1 text-left'>
        <Text
          as='span'
          variant='sm'
          weight='medium'
          className='text-slate-700 max-w-52 w-full truncate sm:max-w-80'>
          {title === sectionName ? tc(title) : sectionName}
        </Text>
        <Text
          as='span'
          variant='sm'
          weight='normal'
          className='text-slate-500 max-w-52 w-full truncate sm:max-w-80'>
          {tc(description)}
        </Text>
      </div>
    </Button>
  )
}

export const EditorSectionReorder = ({ dragControls, isVisible, className }) => {
  return (
    <div
      onPointerDown={(event) => {
        if (isVisible) {
          dragControls.start(event)
        }
      }}
      className={cn(
        'flex items-center gap-2',
        isVisible ? 'cursor-grab' : 'cursor-not-allowed opacity-35',
        className
      )}>
      <GripVertical size={20} className='text-slate-400' />
    </div>
  )
}

export const EditorSectionTrigger = ({ handlePanelClick, isVisible }) => {
  return (
    <Button
      type='button'
      onClick={handlePanelClick}
      variant='subtle'
      size='link'
      disabled={!isVisible}
      className='absolute right-0 inset-y-0 py-5 pr-5 disabled:opacity-35'>
      <ChevronRight size={16} className='text-slate-500' />
    </Button>
  )
}

export const EditorSectionVisibility = ({ section, isVisible }) => {
  const { updateSectionsVisibility } = useEditor()

  return (
    <div className='absolute right-0 inset-y-0 flex items-center gap-2 bg-slate-50 border-l border-slate-200 z-10'>
      <Button
        variant='subtle'
        size='link'
        className='py-7 px-4 text-slate-500'
        onClick={() => updateSectionsVisibility(section)}>
        {isVisible ? <Eye /> : <EyeOff />}
      </Button>
    </div>
  )
}
