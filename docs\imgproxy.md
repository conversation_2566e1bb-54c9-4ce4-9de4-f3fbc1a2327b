Digital Ocean Droplet Setup

Installation steps

1. Install Docker
   ` sudo apt update && sudo apt install -y docker.io docker-compose`
   `sudo systemctl enable --now docker`

2. Verify Docker installation
   `docker --version`
   `docker-compose --version`

3. create a directory for imgproxy
   `mkdir -p ~/imgproxy && cd ~/imgproxy`

4. create a docker-compose.yml file
   `nano docker-compose.yml`

```
    version: "3.8"
    services:
    imgproxy:
    image: darthsim/imgproxy:latest
    container_name: imgproxy
    restart: always
    ports: - "8080:8080"
    environment:
    IMGPROXY_USE_SIGNATURES: "false"
    IMGPROXY_ALLOWED_SOURCES: "https://swiftresume.nyc3.cdn.digitaloceanspaces.com,https://swiftresume.com"
    IMGPROXY_BASE_URL: "https://swiftresume.nyc3.cdn.digitaloceanspaces.com"
    IMGPROXY_ENABLE_WEBP_DETECTION: "true"
    IMGPROXY_ENABLE_AVIF: "true"
    IMGPROXY_ENABLE_GIF: "true"
    IMGPROXY_USE_S3: "false"
    IMGPROXY_LOG_LEVEL: "info" # Change to "debug" for more detailed logs
    networks: - imgproxy_network
    volumes: - ./logs:/var/log/imgproxy # Mounts a directory to store logs

    networks:
    imgproxy_network:
    driver: bridge
```

5. Start the imgproxy container
   `docker-compose up -d`

6. Verify the container is running
   `docker ps`

7. Set Up Nginx Reverse Proxy
   `sudo apt install -y nginx`

8. Create a new Nginx configuration file for domain
   `sudo nano /etc/nginx/sites-available/cdn.swiftresume.com`

9. paste the following configuration to the file

```
server {
    server_name cdn.swiftresume.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    listen 80;
}
```

8. Enable the new configuration
   `sudo ln -s /etc/nginx/sites-available/cdn.swiftresume.com /etc/nginx/sites-enabled/`

9. Test the new configuration
   `sudo nginx -t`

10. Restart Nginx
    `sudo systemctl restart nginx`

11. Secure with SSL (Let’s Encrypt)
    `sudo apt install -y certbot python3-certbot-nginx`

12. Generate the SSL certificate
    `sudo certbot --nginx -d cdn.swiftresume.com`
    after ssl is generate the nginx config file will be updated automatically and look like below

```server {
    server_name cdn.swiftresume.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    listen 443 ssl;
    ssl_certificate /etc/letsencrypt/live/cdn.swiftresume.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/cdn.swiftresume.com/privkey.pem;
}

server {
    listen 80;
    server_name cdn.swiftresume.com;
    return 301 https://$host$request_uri;
}
```

11. Once the certificate is generated, you can test it with the following command to renew the certificate automatically
    `sudo certbot renew --dry-run`

12. Restart Nginx
    `sudo systemctl restart nginx`

13. test imgproxy by running the following command
    https://cdn.swiftresume.com/health
    If imgproxy is working, you should see a 200 OK response.

14. image check url
    https://cdn.swiftresume.com/insecure/rs:fill:300:300/plain/https://swiftresume.nyc3.digitaloceanspaces.com/image.jpg
