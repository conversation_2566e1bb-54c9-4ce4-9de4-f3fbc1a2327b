import { authenticated, authenticatedOrPublished } from '@/access'
import { ArchiveBlock } from '@/blocks/ArchiveBlock/index.js'
import { Banner } from '@/blocks/Banner/index.js'
import { CallToAction } from '@/blocks/CallToAction/index.js'
import { Code } from '@/blocks/Code/index.js'
import { Content } from '@/blocks/Content/index.js'
import { FormBlock } from '@/blocks/Form/index.js'
import { MediaBlock } from '@/blocks/MediaBlock/index.js'
import { slugField } from '@/fields/index.js'
import { populatePublishedAt } from '@/hooks/populatePublishedAt'
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField
} from '@payloadcms/plugin-seo/fields'
import { revalidateDelete, revalidatePage } from './hooks/revalidatePage'

export const pages = {
  slug: 'pages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated
  },
  // This config controls what's populated by default when a page is referenced
  defaultPopulate: {
    title: true,
    slug: true
  },
  admin: {
    defaultColumns: ['title', 'slug', 'updatedAt'],
    livePreview: {
      url: ({ data }) => {
        const baseURL = process.env.NEXT_PUBLIC_URL || 'https://swift.local'
        return `${baseURL}/next/preview?secret=${process.env.PAYLOAD_SECRET}&slug=${data?.slug}&collection=pages`
      }
    },
    preview: (data) => {
      const baseURL = process.env.NEXT_PUBLIC_URL || 'https://swift.local'
      return `${baseURL}/next/preview?secret=${process.env.PAYLOAD_SECRET}&slug=${data?.slug}&collection=pages`
    },
    useAsTitle: 'title',
    slug: 'pages'
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true
    },
    {
      type: 'tabs',
      tabs: [
        {
          fields: [
            {
              name: 'hero',
              type: 'group',
              fields: [
                {
                  name: 'type',
                  type: 'select',
                  defaultValue: 'lowImpact',
                  label: 'Type',
                  options: [
                    {
                      label: 'None',
                      value: 'none'
                    },
                    {
                      label: 'High Impact',
                      value: 'highImpact'
                    },
                    {
                      label: 'Medium Impact',
                      value: 'mediumImpact'
                    },
                    {
                      label: 'Low Impact',
                      value: 'lowImpact'
                    }
                  ],
                  required: true
                },
                {
                  name: 'richText',
                  type: 'richText',
                  label: false
                },
                {
                  name: 'media',
                  type: 'upload',
                  admin: {
                    condition: (_, { type } = {}) =>
                      ['highImpact', 'mediumImpact'].includes(type)
                  },
                  relationTo: 'media',
                  required: true
                }
              ],
              label: false
            }
          ],
          label: 'Hero'
        },
        {
          fields: [
            {
              name: 'layout',
              type: 'blocks',
              blocks: [
                ArchiveBlock,
                Banner,
                CallToAction,
                Code,
                Content,
                FormBlock,
                MediaBlock
              ],
              required: true,
              admin: {
                initCollapsed: true
              }
            }
          ],
          label: 'Content'
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image'
            }),
            MetaTitleField({
              hasGenerateFn: true
            }),
            MetaImageField({
              relationTo: 'media'
            }),
            MetaDescriptionField({}),
            PreviewField({
              // if the `generateUrl` function is configured
              hasGenerateFn: true,
              // field paths to match the target field for data
              titlePath: 'meta.title',
              descriptionPath: 'meta.description'
            })
          ]
        }
      ]
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar'
      }
    },
    ...slugField()
  ],
  hooks: {
    afterChange: [revalidatePage],
    beforeChange: [populatePublishedAt],
    afterDelete: [revalidateDelete]
  },
  versions: {
    drafts: {
      autosave: {
        interval: 100 // We set this interval for optimal live preview
      },
      schedulePublish: true
    },
    maxPerDoc: 50
  }
}
