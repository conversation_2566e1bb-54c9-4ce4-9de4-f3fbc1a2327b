export const detectOrphanedSections = (pages) => {
  const orphanedSections = []

  for (const [pageIndex, page] of pages.entries()) {
    const pageContainer = page?.element

    if (!pageContainer) return orphanedSections

    const headings = pageContainer.querySelectorAll('.smart-break-target')

    for (const heading of headings) {
      const smartClass = [...heading.classList].find((cls) =>
        cls.startsWith('smart-section-')
      )

      if (smartClass) {
        const content = heading.nextElementSibling

        const hasContentText = content?.textContent?.trim()?.length > 0

        const hasNotContent =
          !content || !content.classList.contains('inner-content') || !hasContentText

        if (hasNotContent) {
          orphanedSections.push({
            pageIndex,
            smartClass
          })
        }
      }
    }
  }

  return orphanedSections
}
