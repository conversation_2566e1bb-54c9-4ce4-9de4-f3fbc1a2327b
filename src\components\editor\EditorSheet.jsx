import { AnimatePresence, motion } from 'motion/react'
import { useMediaQuery } from '@raddix/use-media-query'

export const EditorSheet = ({ isSheetActive, children }) => {
  const isDesktop = useMediaQuery('(min-width: 1024px)')

  if (!isDesktop) {
    return null
  }

  return (
    <AnimatePresence>
      {isSheetActive && (
        <motion.div
          className='absolute inset-0 flex-auto bg-white overflow-y-auto thin-scrollbar will-change-transform scrollbar-stable p-4 lg:p-6'
          initial={{ x: '10%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '10%', opacity: 0 }}
          transition={{
            x: {
              type: 'spring',
              stiffness: 300,
              damping: 30,
              mass: 0.5
            },
            opacity: {
              duration: 0.1,
              ease: 'easeOut'
            }
          }}>
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
