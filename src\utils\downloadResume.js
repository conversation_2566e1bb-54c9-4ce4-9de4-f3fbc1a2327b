import { extractPagesContent } from './htmlToReactPdf'

export async function downloadResume(font, action = 'download') {
  const pages = document.querySelectorAll('.shadow-resume-page')
  const parsedPages = extractPagesContent(pages)

  const { generatePdfFromPages } = await import('./generateReactPdf')

  const pdfBlob = await generatePdfFromPages(parsedPages, font)
  if (!pdfBlob) return
  const url = URL.createObjectURL(pdfBlob)
  const link = document.createElement('a')
  link.href = url
  if (action === 'open') {
    link.target = '_blank'
  } else {
    link.download = 'resume.pdf'
  }
  link.click()
  URL.revokeObjectURL(url)
}
