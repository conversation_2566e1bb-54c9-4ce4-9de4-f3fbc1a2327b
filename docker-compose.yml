name: swift

services:
  nginx:
    image: nginx:latest
    ports:
      - 80:80
      - 443:443
    restart: always
    volumes:
      - ./nginx/conf/:/etc/nginx/conf.d/:ro
      - ./nginx/certs:/etc/nginx/ssl
    attach: false
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  mongo:
    image: mongo:latest
    ports:
      - '27017:27017'
    command:
      - --storageEngine=wiredTiger
      - --logpath=/var/log/mongodb/mongod.log
    volumes:
      - data:/data/db
    attach: false
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

volumes:
  data:
