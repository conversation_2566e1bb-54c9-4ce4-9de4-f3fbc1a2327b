import React from 'react'
import { getTranslations } from 'next-intl/server'
import { ImageResponse } from 'next/og'
import { ResumeMain, ResumePage, ResumeSidebar } from '@components'
import { ResumeLayout } from '@components/resumepreview/ui'
import { collections, pageSizes, routes } from '@constants'
import { resumeHasSidebar } from '@utils'
import { loadFonts } from '@utils/loadFonts'

const {
  A4: { width, height }
} = pageSizes

const { resumes } = collections

export const resumetoimage = {
  path: `${routes.resumetoimage}/:resumeId`,
  method: 'get',
  handler: async (request) => {
    const tc = await getTranslations('Common')
    const mt = await getTranslations('Months')
    const { payload } = request
    const { resumeId } = request.routeParams

    const resume = await payload.findByID({
      collection: resumes.slug,
      id: resumeId
    })

    const { layout } = resume.design
    const hasSidebar = resumeHasSidebar(layout)

    // Load fonts based on the resume's font selection
    const fonts = await loadFonts(resume.design.font)

    return new ImageResponse(
      (
        <ResumePage resumeData={resume}>
          <ResumeLayout hasSidebar={hasSidebar} layout={layout}>
            <ResumeMain resumeData={resume} tc={tc} mt={mt} />
            <ResumeSidebar resumeData={resume} tc={tc} mt={mt} />
          </ResumeLayout>
        </ResumePage>
      ),
      {
        width: width,
        height: height,
        fonts: fonts
      }
    )
  }
}
