{"compilerOptions": {"baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@*": ["./src/*"], "@api": ["./src/lib/api"], "@payload-config": ["./src/payload.config.ts"], "@constants": ["./src/constants"], "@debug": ["./src/components/Debug"], "@error": ["./src/utils/SwiftError"], "@data": ["./src/data"], "@plugins": ["./src/plugins"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}