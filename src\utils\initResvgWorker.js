export function initResvgWorker() {
  if (typeof window === 'undefined') return

  const worker = new Worker(new URL('resvgWorker.js', import.meta.url))

  const pending = new Map()
  worker.addEventListener('message', (event) => {
    const { _id, arrayBuffer } = event.data

    const resolve = pending.get(_id)
    if (resolve) {
      resolve(arrayBuffer)
      pending.delete(_id)
    }
  })

  return async (message) => {
    const _id = Math.random()
    worker.postMessage({
      ...message,
      _id
    })
    return new Promise((resolve) => {
      pending.set(_id, resolve)
    })
  }
}
