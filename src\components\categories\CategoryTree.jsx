'use client'

import { useState } from 'react'
import Link from 'next/link'

const CategoryTreeItem = ({ category, level = 0 }) => {
  const [isOpen, setIsOpen] = useState(level < 2) // Auto-expand first 2 levels
  const hasChildren = category.children && category.children.length > 0
  const indent = level * 20

  return (
    <div>
      <div
        className='flex items-center py-2 px-2 hover:bg-gray-50 rounded'
        style={{ paddingLeft: `${indent + 8}px` }}>
        {hasChildren && (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className='mr-2 w-4 h-4 flex items-center justify-center text-gray-400 hover:text-gray-600'>
            {isOpen ? '−' : '+'}
          </button>
        )}
        {!hasChildren && <div className='mr-6' />}

        <Link
          href={`/categories/${category.slug}`}
          className='flex-1 text-gray-700 hover:text-gray-900'>
          {category.title}
        </Link>

        {category.count && (
          <span className='text-sm text-gray-400 ml-2'>({category.count})</span>
        )}
      </div>

      {hasChildren && isOpen && (
        <div>
          {category.children.map((child) => (
            <CategoryTreeItem key={child.id} category={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  )
}

export const CategoryTree = ({ categories, title = 'Categories' }) => {
  return (
    <div className='bg-white border rounded-lg shadow-sm'>
      <div className='px-4 py-3 border-b'>
        <h3 className='font-semibold text-gray-900'>{title}</h3>
      </div>
      <div className='p-2'>
        {categories && categories.length > 0 ? (
          categories.map((category) => (
            <CategoryTreeItem key={category.id} category={category} />
          ))
        ) : (
          <p className='text-gray-500 text-sm p-4'>No categories available</p>
        )}
      </div>
    </div>
  )
}
