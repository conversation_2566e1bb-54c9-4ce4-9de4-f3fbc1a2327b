import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages } from 'next-intl/server'
import { Inter } from 'next/font/google'
import { Toaster } from '@components'
import { ClientProvider } from '@context'
import '@styles/globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter'
})

export const metadata = {
  title: 'Swift Resume',
  description: 'Swift Resume'
}

export default async function RootLayout({ children }) {
  const locale = await getLocale()
  const messages = await getMessages()

  return (
    <html lang={locale}>
      <body className={`${inter.variable} font-sans antialiased`}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <ClientProvider>
            {children}
            <Toaster />
          </ClientProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
