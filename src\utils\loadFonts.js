import { resumeOptions } from './resumeConstant.jsx'

// Function to load fonts for ImageResponse
export const loadFonts = async (fontValue) => {
  const fontOption = resumeOptions.font.options.find(
    (option) => option.value === fontValue
  )
  if (!fontOption) return []

  const fonts = []

  for (const font of fontOption.fonts) {
    try {
      const fontData = await fetch(
        `${process.env.NEXT_PUBLIC_URL || 'http://localhost:3000'}${font.src}`
      ).then((response) => response.arrayBuffer())
      fonts.push({
        name: fontOption.fontFamily.split(',')[0].replaceAll(/['"]/g, '').trim(),
        data: fontData,
        weight: font.fontWeight,
        style: font.fontStyle
      })
    } catch (error) {
      console.error(`Failed to load font: ${font.src}`, error)
    }
  }

  return fonts
}
