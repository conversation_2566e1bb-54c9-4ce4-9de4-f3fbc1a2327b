import React, { Fragment } from 'react'

export const Media = (props) => {
  const { className, htmlElement = 'div', resource } = props

  const isVideo = typeof resource === 'object' && resource?.mimeType?.includes('video')
  const Tag = htmlElement || Fragment

  return (
    <Tag
      {...(htmlElement === null
        ? {}
        : {
            className
          })}>
      {isVideo ? (
        <div className='video-container'>
          <video
            src={resource?.url}
            controls
            className='w-full h-auto'
            poster={resource?.sizes?.thumbnail?.url}>
            <track kind='captions' />
          </video>
        </div>
      ) : (
        <div className='image-container'>
          <img
            src={resource?.url}
            alt={resource?.alt || ''}
            className='w-full h-auto'
            loading='lazy'
          />
        </div>
      )}
    </Tag>
  )
}
