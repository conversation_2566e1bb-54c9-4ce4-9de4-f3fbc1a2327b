'use client'

import { useEffect, useRef, useState } from 'react'
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, Save } from 'lucide-react'
import { useForm } from 'react-hook-form'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input
} from '@components'
import { useResumes } from '@hooks'

export const TitleEdit = ({ name, id }) => {
  const [isEditing, setIsEditing] = useState(false)
  const { isUpdatingResume, updateResumeById } = useResumes()
  const inputRef = useRef(null)
  const initialName = name || ''

  const form = useForm({
    defaultValues: {
      name: initialName
    }
  })

  const { handleSubmit, control } = form

  const handleEditToggle = () => {
    if (isEditing) {
      form.handleSubmit(onSubmit)()
    } else {
      setIsEditing(true)
    }
  }

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const onSubmit = (data) => {
    setIsEditing(false)
    if (data.name !== initialName) {
      updateResumeById({ id, update: { name: data.name } })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className='flex items-center gap-1.5'>
        <FormField
          control={control}
          name='name'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...field}
                  disabled={isUpdatingResume || !isEditing}
                  ref={(element) => {
                    field.ref(element)
                    inputRef.current = element
                  }}
                  placeholder='Untitled resume'
                  className='border-none shadow-none bg-transparent p-1.5 px-2 font-semibold disabled:bg-transparent truncate disabled:cursor-default disabled:text-slate-700'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type='button'
          onClick={handleEditToggle}
          variant='icon'
          className='border-none shadow-none p-2'>
          {isUpdatingResume ? (
            <Loader2 className='animate-spin' />
          ) : isEditing ? (
            <Save className='animate-pulse' />
          ) : (
            <Pencil />
          )}
        </Button>
      </form>
    </Form>
  )
}
