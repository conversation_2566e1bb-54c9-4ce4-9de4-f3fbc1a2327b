'use client'

import * as React from 'react'
import { cva } from 'class-variance-authority'
import * as SwitchPrimitives from '@radix-ui/react-switch'
import { cn } from '@utils'

const switchVariant = cva(
  'peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 ',
  {
    variants: {
      variant: {
        default: 'data-[state=checked]:bg-primary data-[state=unchecked]:bg-slate-300'
      },
      size: {
        default: 'h-5 w-8',
        lg: 'h-6 w-10'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)

const switchThumb = cva(
  'pointer-events-none block rounded-full bg-white shadow-lg ring-0 transition-transform  data-[state=unchecked]:translate-x-0 dark:bg-slate-950',
  {
    variants: {
      size: {
        default: 'h-4 w-4 data-[state=checked]:translate-x-3',
        lg: 'h-5 w-5 data-[state=checked]:translate-x-4'
      }
    },
    defaultVariants: {
      size: 'default'
    }
  }
)

export const Switch = React.forwardRef(({ className, variant, size, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(switchVariant({ variant, size }), className)}
    {...props}
    ref={ref}>
    <SwitchPrimitives.Thumb className={cn(switchThumb({ size }))} />
  </SwitchPrimitives.Root>
))

Switch.displayName = SwitchPrimitives.Root.displayName
