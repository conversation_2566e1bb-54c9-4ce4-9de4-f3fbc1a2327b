export const addPageBreaksToHtml = (originalHTML, orphanedSections) => {
  if (orphanedSections.length === 0) {
    return originalHTML
  }

  const containerDiv = document.createElement('div')
  containerDiv.innerHTML = originalHTML

  for (const { smartClass } of orphanedSections) {
    const heading = containerDiv.querySelector(`.smart-break-target.${smartClass}`)

    if (heading) {
      const container = heading.closest('.section-wrapper')

      if (container) {
        const currentStyle = container.getAttribute('style') || ''

        const pageBreakStyles = [
          'page-break-before: always;',
          'break-before: page;',
          '-webkit-column-break-before: always;'
        ]

        const hasPageBreak = pageBreakStyles.some((style) =>
          currentStyle.includes(style.split(':')[0])
        )

        if (hasPageBreak) {
          continue
        }

        const newStyle = currentStyle
          ? `${currentStyle}; break-before: page; page-break-before: always;`
          : 'break-before: page; page-break-before: always;'

        container.setAttribute('style', newStyle)
      }
    }
  }

  return containerDiv.innerHTML
}
