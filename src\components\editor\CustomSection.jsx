'use client'

import { useEffect } from 'react'
import { useDragControls } from 'motion/react'
import { useTranslations } from 'next-intl'
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form'
import {
  AccordionWrapper,
  AddSection,
  DeleteSectionItem,
  EditorFieldCheckbox,
  EditorFieldItem,
  EditorFieldLabel,
  EditorFieldRow,
  EditorFormBlock,
  EditorFormFieldGroup,
  Form,
  Input,
  MonthPicker,
  ReorderGroup,
  RichTextFieldController,
  SectionContent,
  SectionItem,
  SectionReorder,
  SectionTrigger,
  SectionVisibilityHandler,
  Switch,
  YearPicker
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  useCustomUndoRedo,
  useEditor,
  useFieldAdd,
  useFieldRemove,
  useFormWatch,
  useResume
} from '@hooks'
import {
  handleSectionReorder,
  newCustomSection,
  resumeOptionValue,
  resumeSectionSchemas,
  resumeValues
} from '@utils'
import { EditorPanelHeader } from './EditorPanelHeader'

export const CustomSection = () => {
  const { resume, updateResume } = useResume()
  const sectionKey = resumeOptionValue.customSections
  const data = resume[sectionKey]

  const form = useForm({
    resolver: zodResolver(resumeSectionSchemas.customSections),
    defaultValues: resumeValues.customSections(resume, sectionKey),
    mode: 'onChange'
  })

  const { customSections, _source } = resume

  const { register, watch, control, trigger, unregister, setValue } = form

  const { fields, append, remove, move } = useFieldArray({
    control: control,
    name: sectionKey
  })

  useFormWatch(watch, sectionKey, trigger)

  const handleRemove = useFieldRemove({
    remove,
    sectionKey: sectionKey,
    data
  })

  const handleAdd = useFieldAdd({
    append,
    sectionKey: sectionKey,
    data,
    newItem: newCustomSection
  })

  const handleReorder = (newOrder) => {
    handleSectionReorder(newOrder, fields, move, updateResume, sectionKey)
  }

  useCustomUndoRedo(_source, customSections, sectionKey, setValue)

  return (
    <>
      <EditorPanelHeader
        editable={false}
        sectionKey={sectionKey}
        description='showcaseUniqueExperiences'
      />
      <Form {...form}>
        <form>
          <AddSection text='addSection' onClick={handleAdd} />
          <AccordionWrapper fields={fields}>
            <ReorderGroup values={fields} onReorder={handleReorder}>
              {fields.map((field, index) => {
                return (
                  <CustomSectionAccordion
                    key={field.id}
                    form={form}
                    field={field}
                    index={index}
                    remove={handleRemove}
                    register={register}
                    unregister={unregister}
                  />
                )
              })}
            </ReorderGroup>
          </AccordionWrapper>
        </form>
      </Form>
    </>
  )
}

const CustomSectionAccordion = ({ form, field, index, remove, register, unregister }) => {
  const tc = useTranslations('Common')
  const controls = useDragControls()

  useEffect(() => {
    register(`customSections.${index}.startMonth`, { value: field.startMonth })
    register(`customSections.${index}.startYear`, { value: field.startYear })
    register(`customSections.${index}.showDate`, { value: field.showDate })
    register(`customSections.${index}.description`, { value: field.description })

    return () => {
      unregister([
        `customSections.${index}.startMonth`,
        `customSections.${index}.startYear`,
        `customSections.${index}.showDate`,
        `customSections.${index}.description`
      ])
    }
  }, [unregister, index, register]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <SectionItem id={field.id} value={field} dragListener={false} dragControls={controls}>
      <SectionPanel
        field={field}
        index={index}
        controls={controls}
        remove={remove}
        form={form}
      />
      <SectionContent>
        <EditorFormBlock>
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`customSections.${index}.title`}>
                  {tc('title')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('title')}
                  {...register(`customSections.${index}.title`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`customSections.${index}.subTitle`}>
                  {tc('subTitle')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('subTitle')}
                  {...register(`customSections.${index}.subTitle`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
          <EditorFormFieldGroup>
            <EditorFieldItem>
              <EditorFieldLabel>{tc('date')}</EditorFieldLabel>
              <EditorFieldRow className='flex-row items-center flex-wrap'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`customSections.${index}.startMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`customSections.${index}.startMonth`}
                        label={tc('startMonth')}
                        placeholder={tc('month')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`customSections.${index}.startYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`customSections.${index}.startYear`}
                        label={tc('startYear')}
                        placeholder={tc('year')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='w-auto'>
                  <EditorFieldCheckbox>
                    <Controller
                      name={`customSections.${index}.showDate`}
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          id={`customSections.${index}.showDate`}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          name={field.name}
                          ref={field.ref}
                        />
                      )}
                    />
                    <EditorFieldLabel htmlFor={`customSections.${index}.showDate`}>
                      {tc('showDate')}
                    </EditorFieldLabel>
                  </EditorFieldCheckbox>
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFieldItem>
          </EditorFormFieldGroup>
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`customSections.${index}.description`}>
                  {tc('description')}
                </EditorFieldLabel>
                <RichTextFieldController
                  name={`customSections.${index}.description`}
                  control={form.control}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
      </SectionContent>
    </SectionItem>
  )
}

const SectionPanel = ({ field, index, controls, remove, form }) => {
  const tc = useTranslations('Common')
  const { visiblityControls, sectionsVisibility, updateSectionsVisibility } = useEditor()
  const eyeVisible = sectionsVisibility[index] || false

  const currentValue = useWatch({
    control: form.control,
    name: `customSections.${index}`
  })

  const sectionTitle = currentValue?.title || tc('newCustomSection')
  const isVisible = currentValue?.isVisible || false

  function deleteItemHandler() {
    remove(index)
    updateSectionsVisibility(field, index, 'remove')
  }

  return (
    <div className='px-4 flex items-center gap-1.5'>
      {visiblityControls ? (
        <SectionVisibilityHandler
          isVisible={eyeVisible}
          onClick={() => updateSectionsVisibility(field, index)}
        />
      ) : (
        <SectionReorder dragHandler={(event) => controls.start(event)} />
      )}
      <SectionTrigger label={sectionTitle} isVisible={isVisible} />
      <DeleteSectionItem
        orignalValue={newCustomSection}
        currentValue={currentValue}
        deleteItemHandler={deleteItemHandler}
      />
    </div>
  )
}
