import { resumeHasSidebar, resumeStyles } from '@utils'

export const PersonalInfoTitle = ({
  children,
  palette,
  location,
  accent,
  layout,
  ...props
}) => {
  const hasSidebar = resumeHasSidebar(layout)
  const personalInfoTitleStyle = resumeStyles.personalInfoTitle(
    palette,
    location,
    accent,
    hasSidebar
  )
  return (
    <div style={personalInfoTitleStyle} {...props}>
      {children}
    </div>
  )
}
