# Country-Based Products List API

## Overview

The Country-Based Products List API returns products with pricing specific to the user's detected country. The API automatically detects the user's country from the `x-vercel-ip-country` header and returns products with appropriate pricing for that country.

## Endpoint

```
GET /api/country-based-products-list
```

## Headers

- `x-vercel-ip-country`: Country code (e.g., 'US', 'CA', 'GB')
  - If not provided or invalid, defaults to 'US'

## Response Format

```json
{
  "country": "US",
  "currency": "usd",
  "symbol": "$",
  "products": [
    {
      "id": "product_id",
      "title": "Product Title",
      "description": "Product Description",
      "price": {
        "amount": 1999,
        "formatted": "$19.99",
        "currency": "usd"
      }
    }
  ],
  "total": 1
}
```

## Supported Countries

The API supports the following countries and their corresponding currencies:

| Country | Currency | Symbol |
| ------- | -------- | ------ |
| US      | usd      | $      |
| CA      | cad      | C$     |
| GB      | gbp      | £      |
| EU      | eur      | €      |
| AU      | aud      | A$     |
| IN      | inr      | ₹      |
| JP      | jpy      | ¥      |
| SE      | sek      | kr     |
| NO      | nok      | kr     |
| DK      | dkk      | kr     |
| CH      | chf      | CHF    |
| PL      | pln      | zł     |
| CZ      | czk      | Kč     |
| HU      | huf      | Ft     |
| RO      | ron      | lei    |
| BG      | bgn      | лв     |
| HR      | hrk      | kn     |
| SK      | skk      | Sk     |

## Error Responses

### 404 - No Products Found

```json
{
  "error": "No products found",
  "country": "US",
  "currency": "usd",
  "symbol": "$",
  "products": []
}
```

### 500 - Server Error

```json
{
  "error": "Failed to fetch products",
  "message": "Error details"
}
```

## Usage Examples

### cURL

```bash
curl -H "x-vercel-ip-country: US" http://localhost:3000/api/country-based-products-list
```

### JavaScript

```javascript
const response = await fetch('/api/country-based-products-list', {
  headers: {
    'x-vercel-ip-country': 'US'
  }
})
const data = await response.json()
```

## Implementation Details

1. **Country Detection**: Uses `x-vercel-ip-country` header to detect user's country
2. **Currency Mapping**: Maps country codes to appropriate currencies
3. **Product Filtering**: Only returns products with pricing for the detected country
4. **Price Formatting**: Formats prices with appropriate currency symbols
5. **Error Handling**: Graceful handling of missing or invalid data

## Flow

1. Client sends GET request with country header
2. API extracts and validates country code
3. Maps country to currency and symbol
4. Fetches all products using SDK
5. Filters products with country-specific pricing
6. Formats prices with currency symbols
7. Returns structured response with country info and products
