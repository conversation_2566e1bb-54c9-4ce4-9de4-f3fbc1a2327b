import React, { Fragment } from 'react'
import { ArchiveBlock as ArchiveBlockComponent } from '@/blocks/ArchiveBlock/Component'
import { Banner as BannerComponent } from '@/blocks/Banner/Component'
import { CallToAction as CallToActionComponent } from '@/blocks/CallToAction/Component'
import { Code as CodeComponent } from '@/blocks/Code/Component'
import { ContentBlock as ContentComponent } from '@/blocks/Content/Component'
import { FormBlock as FormBlockComponent } from '@/blocks/Form/Component'
import { MediaBlock as MediaBlockComponent } from '@/blocks/MediaBlock/Component'

const blockComponents = {
  archive: ArchiveBlockComponent,
  banner: BannerComponent,
  cta: CallToActionComponent,
  code: CodeComponent,
  content: ContentComponent,
  formBlock: FormBlockComponent,
  mediaBlock: MediaBlockComponent
}

export const RenderBlocks = ({ blocks }) => {
  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              return (
                <div className='my-16' key={index}>
                  <Block {...block} disableInnerContainer />
                </div>
              )
            }
          }

          // Fallback for unknown block types
          return (
            <div key={index} className='my-8 p-4 bg-gray-100 rounded'>
              <p className='text-gray-600'>Block type: {blockType}</p>
              <pre className='text-xs mt-2'>{JSON.stringify(block, null, 2)}</pre>
            </div>
          )
        })}
      </Fragment>
    )
  }

  return null
}
