'use client'

import { useEffect } from 'react'
import { useDragControls } from 'motion/react'
import { useTranslations } from 'next-intl'
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form'
import {
  AccordionWrapper,
  AddSection,
  DeleteSectionItem,
  EditorFieldCheckbox,
  EditorFieldItem,
  EditorFieldLabel,
  EditorFieldRow,
  EditorFormBlock,
  EditorFormFieldGroup,
  EditorFormTitle,
  Form,
  Input,
  MonthPicker,
  ReorderGroup,
  RichTextFieldController,
  SectionContent,
  SectionItem,
  SectionReorder,
  SectionTrigger,
  SectionVisibilityHandler,
  Switch,
  Text,
  YearPicker
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  useCustomUndoRedo,
  useEditor,
  useFieldAdd,
  useFieldRemove,
  useFormWatch,
  useResume
} from '@hooks'
import {
  handleSectionReorder,
  newExperience,
  resumeOptionValue,
  resumeSectionSchemas,
  resumeValues
} from '@utils'
import { EditorPanelHeader } from './EditorPanelHeader'

export const Experience = () => {
  const { resume, updateResume } = useResume()
  const sectionKey = resumeOptionValue.experience
  const data = resume[sectionKey]

  const form = useForm({
    resolver: zodResolver(resumeSectionSchemas.experience),
    defaultValues: resumeValues.experience(resume, sectionKey),
    mode: 'onChange'
  })

  const { experience, _source } = resume

  const {
    formState: { errors },
    register,
    watch,
    control,
    trigger,
    unregister,
    setValue
  } = form

  const { fields, append, remove, move } = useFieldArray({
    control: control,
    name: sectionKey
  })

  useFormWatch(watch, sectionKey, trigger)

  const handleRemove = useFieldRemove({
    remove,
    sectionKey: sectionKey,
    data
  })

  const handleAdd = useFieldAdd({
    append,
    sectionKey: sectionKey,
    data,
    newItem: newExperience
  })

  const handleReorder = (newOrder) => {
    handleSectionReorder(newOrder, fields, move, updateResume, sectionKey)
  }

  useCustomUndoRedo(_source, experience, sectionKey, setValue)

  return (
    <>
      <EditorPanelHeader
        sectionKey={resumeOptionValue.experience}
        description='yourWorkHistoryAndAchievements'
      />
      <Form {...form}>
        <form>
          <AddSection text='addExperienceAndJob' onClick={handleAdd} />
          <AccordionWrapper fields={fields}>
            <ReorderGroup values={fields} onReorder={handleReorder}>
              {fields.map((field, index) => {
                return (
                  <ExperienceAccordion
                    key={field.id}
                    form={form}
                    field={field}
                    index={index}
                    remove={handleRemove}
                    errors={errors}
                    register={register}
                    unregister={unregister}
                  />
                )
              })}
            </ReorderGroup>
          </AccordionWrapper>
        </form>
      </Form>
    </>
  )
}

const ExperienceAccordion = ({
  form,
  field,
  index,
  remove,
  errors,
  register,
  unregister
}) => {
  const tc = useTranslations('Common')
  const t = useTranslations('Errors')
  const controls = useDragControls()

  const isPresent = useWatch({
    control: form.control,
    name: `experience.${index}.isPresent`
  })

  useEffect(() => {
    register(`experience.${index}.isInternship`, { value: field.isInternship })
    register(`experience.${index}.startMonth`, { value: field.startMonth })
    register(`experience.${index}.startYear`, { value: field.startYear })
    register(`experience.${index}.endMonth`, { value: field.endMonth })
    register(`experience.${index}.endYear`, { value: field.endYear })
    register(`experience.${index}.isPresent`, { value: field.isPresent })
    register(`experience.${index}.description`, { value: field.description })

    return () => {
      unregister([
        `experience.${index}.isInternship`,
        `experience.${index}.startMonth`,
        `experience.${index}.startYear`,
        `experience.${index}.endMonth`,
        `experience.${index}.endYear`,
        `experience.${index}.isPresent`,
        `experience.${index}.description`
      ])
    }
  }, [unregister, index, register]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <SectionItem id={field.id} value={field} dragListener={false} dragControls={controls}>
      <SectionPanel
        field={field}
        index={index}
        controls={controls}
        remove={remove}
        form={form}
      />
      <SectionContent>
        <EditorFormBlock>
          <EditorFormTitle title={tc('yourRole')} />
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`experience.${index}.title`}>
                  {tc('title')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('title')}
                  {...register(`experience.${index}.title`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`experience.${index}.subTitle`}>
                  {tc('subTitle')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('subTitle')}
                  {...register(`experience.${index}.subTitle`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldCheckbox>
                  <Controller
                    name={`experience.${index}.isInternship`}
                    control={form.control}
                    render={({ field }) => (
                      <Switch
                        id={`experience.${index}.isInternship`}
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                  <EditorFieldLabel htmlFor={`experience.${index}.isInternship`}>
                    {tc('markAsInternshipt')}
                  </EditorFieldLabel>
                </EditorFieldCheckbox>
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
        <EditorFormBlock>
          <EditorFormTitle title={tc('dates')} />
          <EditorFormFieldGroup>
            <EditorFieldItem>
              <Text
                as='span'
                variant='xs'
                weight='medium'
                className='block text-slate-700'>
                {tc('startDate')}
              </Text>
              <EditorFieldRow className='flex-row'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`experience.${index}.startMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`experience.${index}.startMonth`}
                        label={tc('startMonth')}
                        placeholder={tc('month')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`experience.${index}.startYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`experience.${index}.startYear`}
                        label={tc('startYear')}
                        placeholder={tc('year')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFieldItem>
            <EditorFieldItem>
              <Text
                as='span'
                variant='xs'
                weight='medium'
                className='block text-slate-700'>
                {tc('endDate')}
              </Text>
              <EditorFieldRow className='flex-row items-center flex-wrap'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`experience.${index}.endMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`experience.${index}.endMonth`}
                        label={tc('endMonth')}
                        disabled={isPresent}
                        placeholder={tc('month')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`experience.${index}.endYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`experience.${index}.endYear`}
                        label={tc('endYear')}
                        placeholder={tc('year')}
                        disabled={isPresent}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='w-auto'>
                  <EditorFieldCheckbox>
                    <Controller
                      name={`experience.${index}.isPresent`}
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          id={`experience.${index}.isPresent`}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          name={field.name}
                          ref={field.ref}
                        />
                      )}
                    />
                    <EditorFieldLabel htmlFor={`experience.${index}.isPresent`}>
                      {tc('present')}
                    </EditorFieldLabel>
                  </EditorFieldCheckbox>
                </EditorFieldItem>
              </EditorFieldRow>
              {errors?.experience?.[index]?.endYear && (
                <Text variant='xs' weight='medium' className='text-red-500 mt-2'>
                  {t(errors.experience[index].endYear.message)}
                </Text>
              )}
            </EditorFieldItem>
          </EditorFormFieldGroup>
        </EditorFormBlock>
        <EditorFormBlock>
          <EditorFormTitle title={tc('keyAccomplishments')} />
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel
                  className='sr-only'
                  htmlFor={`experience.${index}.description`}>
                  {tc('keyAccomplishments')}
                </EditorFieldLabel>
                <RichTextFieldController
                  name={`experience.${index}.description`}
                  control={form.control}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
      </SectionContent>
    </SectionItem>
  )
}

const SectionPanel = ({ field, index, controls, remove, form }) => {
  const tc = useTranslations('Common')
  const { visiblityControls, sectionsVisibility, updateSectionsVisibility } = useEditor()
  const eyeVisible = sectionsVisibility[index] || false

  const currentValue = useWatch({
    control: form.control,
    name: `experience.${index}`
  })

  const sectionTitle = currentValue?.title || tc('newExperience')
  const isVisible = currentValue?.isVisible || false

  function deleteItemHandler() {
    remove(index)
    updateSectionsVisibility(field, index, 'remove')
  }

  return (
    <div className='px-4 flex items-center gap-1.5'>
      {visiblityControls ? (
        <SectionVisibilityHandler
          isVisible={eyeVisible}
          onClick={() => updateSectionsVisibility(field, index)}
        />
      ) : (
        <SectionReorder dragHandler={(event) => controls.start(event)} />
      )}
      <SectionTrigger label={sectionTitle} isVisible={isVisible} />
      <DeleteSectionItem
        orignalValue={newExperience}
        currentValue={currentValue}
        deleteItemHandler={deleteItemHandler}
      />
    </div>
  )
}
