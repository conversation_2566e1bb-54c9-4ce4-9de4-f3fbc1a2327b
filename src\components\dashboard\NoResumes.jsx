import { getTranslations } from 'next-intl/server'
import { EmptyResumeCard, EmptyResumeCardImage, Text } from '@components'

export async function NoResumes() {
  const t = await getTranslations('DashboardPage')

  return (
    <div className='flex flex-col gap-8 my-auto'>
      <div className='flex flex-col gap-1.5 text-center'>
        <Text as='h2' variant='3xl' className='mb-1.5 text-slate-700'>
          {t('title')}
        </Text>
        <Text
          as='p'
          variant='base'
          weight='medium'
          className='text-slate-700 text-balance'>
          {t('description')}
        </Text>
      </div>
      <div className='w-full overflow-hidden'>
        <div className='flex flex-nowrap gap-16 justify-center'>
          {Array.from({ length: 6 }).map((_, index) => (
            <EmptyResumeCardImage key={`left-${index}`} />
          ))}
          <EmptyResumeCard
            className='size-64'
            imageClass='border-t border-x border-blue-200 rounded-t-xl overflow-hidden aspect-[1/1.1]'
            btnClass='bottom-12'
          />
          {Array.from({ length: 6 }).map((_, index) => (
            <EmptyResumeCardImage key={`right-${index}`} />
          ))}
        </div>
      </div>
    </div>
  )
}
