export const subscriptionCreated = async (arguments_) => {
  const { event, payload } = arguments_

  const { id: subscriptionId, customer: stripeCustomerId, metadata } = event.data.object
  const userId = metadata?.userId
  const logs = true

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return
  }

  if (logs) {
    payload.logger.info(
      `Syncing Stripe subscription with ID: ${subscriptionId} to Payload...`
    )
  }

  try {
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 0
    })

    if (!user && user.id !== userId) {
      if (logs) {
        payload.logger.info(`- No user found with ID: ${userId}`)
      }
      return
    }

    if (logs) {
      payload.logger.info(`- Found user with ID: ${userId}`)
    }

    try {
      const subscription = await payload.findByID({
        collection: 'subscriptions',
        depth: 0,
        where: {
          subscriptionId: {
            equals: subscriptionId
          }
        }
      })

      if (subscription) {
        if (logs) {
          payload.logger.info(`- Found subscription with ID: ${subscriptionId}`)
        }
        await payload.update({
          collection: 'subscriptions',
          id: subscription.id,
          data: {
            status: event.data.object.status
          }
        })

        if (logs) {
          payload.logger.info(`✅ Successfully updated subscription.`)
        }
        return
      }
    } catch (error) {
      payload.logger.error(`-No Existing Subscription: ${error}`)
    }

    const newSubscription = await payload.create({
      collection: 'subscriptions',
      data: {
        subscriptionId: subscriptionId,
        user: userId,
        stripeCustomerId: stripeCustomerId,
        status: event.data.object.status
      }
    })

    if (logs) {
      payload.logger.info(`- Created new subscription with ID: ${newSubscription.id}`)
    }

    if (newSubscription) {
      await payload.update({
        collection: 'users',
        id: userId,
        data: {
          subscription: newSubscription.id
        }
      })
    }

    if (logs) {
      payload.logger.info(`✅ Successfully created subscription.`)
    }
  } catch (error) {
    payload.logger.error(`- Error creating subscription: ${error}`)
  }
}
