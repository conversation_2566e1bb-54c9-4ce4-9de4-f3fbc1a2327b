'use client'

import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Button,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle
} from '@components'
import { useResumes } from '@hooks'
import { useMediaQuery } from '@raddix/use-media-query'

export const ResumeDeleteModal = ({ resumeId, setResumeId }) => {
  const { deleteResumeById, isDeletingResume } = useResumes()
  const tc = useTranslations('Common')
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  return isDesktop ? (
    <AlertDialog open={resumeId !== null}>
      <AlertDialogContent className='max-w-sm'>
        <AlertDialogHeader>
          <AlertDialogTitle>{tc('deleteResume')}</AlertDialogTitle>
          <AlertDialogDescription>{tc('deleteResumeAlert')}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => setResumeId(null)}>
            {tc('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            variant='destructive'
            onClick={() => deleteResumeById(resumeId)}
            disabled={isDeletingResume}>
            {isDeletingResume ? (
              <>
                <Loader2 className='animate-spin' />
                {tc('deleting')}...
              </>
            ) : (
              tc('delete')
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  ) : (
    <Drawer open={resumeId !== null} onOpenChange={() => setResumeId(null)}>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>{tc('deleteResume')}</DrawerTitle>
          <DrawerDescription>{tc('deleteResumeAlert')}</DrawerDescription>
        </DrawerHeader>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant='secondary'>{tc('cancel')}</Button>
          </DrawerClose>
          <Button
            variant='destructive'
            disabled={isDeletingResume}
            onClick={() => deleteResumeById(resumeId)}>
            {isDeletingResume ? (
              <>
                <Loader2 className='animate-spin' />
                {tc('deleting')}...
              </>
            ) : (
              tc('delete')
            )}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
