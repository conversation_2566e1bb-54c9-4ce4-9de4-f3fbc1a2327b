import { isMember } from '@access'
import { emailTypes } from '@constants'
import { sendEmail } from '@utils'

export const sendWelcomeEmail = async ({ doc, operation, req }) => {
  try {
    // Check if this is a create operation, user has email, and user is a member
    if (operation === 'create' && doc.email && isMember(doc.role)) {
      await sendEmail({
        to: doc.email,
        type: emailTypes.WELCOME,
        data: doc
      })
      console.log(`Welcome email sent to: ${doc.email}`)
    }
  } catch (error) {
    console.error('Error sending welcome email:', error)
    // Don't throw the error to prevent user creation from failing
    // Log it for monitoring purposes
    if (req?.payload?.logger) {
      req.payload.logger.error(
        `Failed to send welcome email to ${doc.email}: ${error.message}`
      )
    }
  }

  return doc
}
