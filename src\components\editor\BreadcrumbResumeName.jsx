'use client'

import { useTranslations } from 'next-intl'
import { BreadcrumbPage } from '@components'
import { useResume } from '@hooks'
import { getResumeTitle } from '@utils'

export const BreadcrumbResumeName = () => {
  const tc = useTranslations('Common')
  const {
    resume: { name, jobTitle }
  } = useResume()

  const displayName = getResumeTitle({ name, jobTitle }) || tc('newResume')

  return <BreadcrumbPage className='truncate'>{displayName}</BreadcrumbPage>
}
