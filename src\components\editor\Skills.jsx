'use client'

import { ArrowUp, GripVertical, X } from 'lucide-react'
import { useDragControls, useMotionValue } from 'motion/react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import {
  <PERSON><PERSON>,
  Editor<PERSON>ieldLabel,
  EditorPanelHeader,
  Form,
  Input,
  ReorderGroup,
  ReorderItem,
  SparkelIcon,
  Text
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRaisedShadow, useResume } from '@hooks'
import { resumeItemSchemas, resumeOptionValue } from '@utils'

export const Skills = () => {
  const tc = useTranslations('Common')
  const { resume, updateResume } = useResume()

  const form = useForm({
    resolver: zodResolver(resumeItemSchemas.skill),
    defaultValues: {
      skill: '',
      rating: 0,
      isVisible: true
    }
  })

  const handleSubmit = (data) => {
    if (!data.skill) return
    const isExistingSkill = resume?.skills.some((s) => s.skill === data.skill)
    if (isExistingSkill) {
      form.reset()
      return
    }
    const newSkills = [
      ...(resume?.skills || []),
      { skill: data.skill, rating: 0, isVisible: true }
    ]
    updateResume({ skills: newSkills })
    form.reset()
  }

  const handleRemoveSkill = (skillToRemove) => {
    const updatedSkills = (resume?.skills || []).filter(
      (skill) => skill.skill !== skillToRemove.skill
    )
    updateResume({ skills: updatedSkills })
  }

  const handleReorder = (newSkills) => {
    updateResume({ skills: newSkills })
  }

  return (
    <>
      <EditorPanelHeader
        sectionKey={resumeOptionValue.skills}
        description='keyAreasThatIllustrateYourStrengths'
      />
      <div className='relative mb-5'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <EditorFieldLabel className='sr-only' htmlFor='skill'>
              {tc('enterYourSkills')}
            </EditorFieldLabel>
            <Input
              className='pr-40'
              type='text'
              placeholder={tc('enterYourSkills')}
              {...form.register(`skill`)}
            />
            <Button
              type='button'
              variant='white'
              size='sm'
              className='absolute inset-y-1.5 right-12'>
              <SparkelIcon className='fill-yellow-300' />
              {tc('aiSuggest')}
            </Button>
            <Button
              type='submit'
              variant='default'
              size='sm'
              className='absolute inset-y-1.5 right-2.5'>
              <ArrowUp size={16} />
            </Button>
          </form>
        </Form>
      </div>

      <ReorderGroup values={resume?.skills} onReorder={handleReorder}>
        {resume?.skills.map((item) => (
          <SkillItem key={item.skill} item={item} onRemove={handleRemoveSkill} />
        ))}
      </ReorderGroup>
    </>
  )
}

const SkillItem = ({ item, onRemove }) => {
  const dragControls = useDragControls()
  const y = useMotionValue(0)
  const boxShadow = useRaisedShadow(y)
  return (
    <ReorderItem
      id={item.skill}
      value={item}
      dragListener={false}
      dragControls={dragControls}
      className='relative'
      style={{ boxShadow, y }}>
      <div className='p-3 bg-slate-50 border border-slate-200 rounded flex items-center justify-between gap-1 select-none'>
        <GripVertical
          size={18}
          className='text-slate-500 cursor-grab'
          onPointerDown={(event) => dragControls.start(event)}
        />
        <Text variant='sm' weight='semibold' className='flex-1 text-slate-600'>
          {item.skill}
        </Text>
        <Button
          type='button'
          variant='subtle'
          size='link'
          className='[&_svg]:size-3.5'
          onClick={() => onRemove(item)}>
          <X size={14} />
        </Button>
      </div>
    </ReorderItem>
  )
}
