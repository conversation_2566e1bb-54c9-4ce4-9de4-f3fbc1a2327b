'use client'

import { useEffect, useState } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  Button,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Text
} from '@components'
import { routes, validationSchemas } from '@constants'
import { forgotPasswordEmail } from '@data'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'

const forgotPasswordSchema = z.object({
  email: validationSchemas.email
})

export const ForgotPasswordForm = () => {
  const searchParameters = useSearchParams()
  const initialEmail = searchParameters.get('email')
  const t = useTranslations('ForgotPasswordPage')
  const tc = useTranslations('Common')
  const [cooldownTime, setCooldownTime] = useState(0)

  const form = useForm({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: initialEmail || ''
    }
  })

  const onSubmit = (data) => {
    resetPasswordEmail(data)
  }

  const {
    mutate: resetPasswordEmail,
    isPending,
    error,
    isSuccess
  } = useMutation({
    mutationFn: forgotPasswordEmail,
    onSuccess: () => {
      setCooldownTime(30)
    }
  })

  useEffect(() => {
    let timer
    if (cooldownTime > 0) {
      timer = setInterval(() => {
        setCooldownTime((previous) => Math.max(0, previous - 1))
      }, 1000)
    }
    return () => clearInterval(timer)
  }, [cooldownTime])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <div className='space-y-4'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('email')}</FormLabel>
                <FormControl>
                  <Input type='email' placeholder={tc('emailPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='space-y-2'>
          <Button
            type='submit'
            className='w-full'
            isLoading={isPending}
            disabled={isPending || cooldownTime > 0}>
            {isPending ? (
              <>
                <Loader2 className='animate-spin' /> {tc('sending')}...
              </>
            ) : (
              t('sendResetLink')
            )}
          </Button>
          {cooldownTime > 0 && (
            <Text className='text-center text-muted-foreground' variant='xs'>
              {t('resendIn', { seconds: cooldownTime })}
            </Text>
          )}
        </div>
        <Text as='div' variant='sm' weight='medium' className='mt-2 block text-center'>
          {tc('returnToSignIn')}{' '}
          <Link className='text-primary' href={routes.signIn}>
            {tc('signInLink')}
          </Link>
        </Text>
        <ErrorDisplay error={error} />
        {isSuccess && (
          <Text
            as='div'
            variant='sm'
            weight='medium'
            className='text-green-600 text-center'>
            {tc('passwordResetInstruction')}
          </Text>
        )}
      </form>
    </Form>
  )
}
