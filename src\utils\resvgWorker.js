import * as resvg from '@resvg/resvg-wasm'

const wasmPath = new URL('@resvg/resvg-wasm/index_bg.wasm', import.meta.url)
fetch(wasmPath).then((response) => resvg.initWasm(response))

self.addEventListener('message', (event) => {
  const { svg, width, _id } = event.data

  const renderer = new resvg.Resvg(svg, {
    fitTo: {
      mode: 'width',
      value: width
    }
  })
  const image = renderer.render()
  const pngBuffer = image.asPng()

  const arrayBuffer = pngBuffer.buffer.slice(
    pngBuffer.byteOffset,
    pngBuffer.byteOffset + pngBuffer.byteLength
  )
  self.postMessage({ _id, arrayBuffer }, [arrayBuffer])
})
