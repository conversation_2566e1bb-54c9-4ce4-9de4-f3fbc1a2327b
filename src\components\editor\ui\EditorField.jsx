import { Label } from '@components'
import { cn } from '@utils'

export const EditorFieldRow = ({ children, className, ...props }) => {
  return (
    <div className={cn('flex flex-col md:flex-row gap-2.5', className)} {...props}>
      {children}
    </div>
  )
}

export const EditorFieldItem = ({ children, className, ...props }) => {
  return (
    <div className={cn('flex w-full flex-col gap-1.5', className)} {...props}>
      {children}
    </div>
  )
}

export const EditorFieldLabel = ({ children, className, ...props }) => {
  return (
    <Label className={className} {...props}>
      {children}
    </Label>
  )
}

export const EditorFieldCheckbox = ({ children, className, ...props }) => {
  return (
    <div className={cn('flex flex-row gap-2 items-center', className)} {...props}>
      {children}
    </div>
  )
}
