import { unstable_cache } from 'next/cache'
import { getPayloadClient } from '@/getPayload'

export async function getRedirects(depth = 1) {
  const payload = await getPayloadClient()

  const { docs: redirects } = await payload.find({
    collection: 'redirects',
    depth
  })

  return redirects
}

/**
 * Returns a unstable_cache function mapped with the cache tag for 'redirects'.
 * Cache all redirects together to avoid multiple fetches.
 */
export const getCachedRedirects = () =>
  unstable_cache(async () => getRedirects(), ['redirects'], {
    tags: ['redirects']
  })
