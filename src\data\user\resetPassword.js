import { api } from '@api'
import { auth, payload } from '@constants'
import { SwiftError } from '@error'
import { matchPayloadErrorMessage } from '@utils'

const { tokenExpired, resetPasswordError } = auth.errors

export const resetPassword = async (bodyData) => {
  const { password, token } = bodyData

  const { data, error } = await api.resetPassword({ body: { password, token } })

  if (error) {
    const tokenExpiredError = matchPayloadErrorMessage(error, payload.tokenExpired)
    if (tokenExpiredError) {
      throw new SwiftError(tokenExpired)
    }
    throw new SwiftError(resetPasswordError)
  }

  return data
}
