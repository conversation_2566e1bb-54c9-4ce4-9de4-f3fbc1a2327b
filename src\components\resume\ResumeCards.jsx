'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { ResumeMenu, Text } from '@components'
import { global, routes } from '@constants'
import { getResumeTitle, imagePlaceholder } from '@utils'

export const ResumeCard = ({ resume }) => {
  const { id, name, jobTitle, updatedAt } = resume
  const tc = useTranslations('Common')

  const resumeName = getResumeTitle({ name, jobTitle }) || tc('newResume')

  const formattedDate = new Date(updatedAt).toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric'
  })

  const updatedAtDate = new Date(updatedAt).getTime()

  const imageUrl = `${process.env.NEXT_PUBLIC_URL}/${global.apiRoute}${routes.resumetoimage}/${id}?v=${updatedAtDate}`

  return (
    <div className='relative'>
      <Link
        href={routes.editor(id)}
        className='relative flex flex-col rounded-lg overflow-hidden border border-slate-200 shadow-card bg-resume-card transition-shadow hover:shadow-md'>
        <div className='relative px-1.5 pt-1.5'>
          <Image
            src={imageUrl}
            alt={resumeName}
            className='w-full h-full object-cover object-top aspect-[1.7/1] border border-slate-200 rounded-md shadow-sm'
            width={262}
            height={154}
            placeholder='blur'
            blurDataURL={imagePlaceholder(262, 154)}
            unoptimized
          />
        </div>
        <div className='p-4'>
          <div className='flex items-start justify-between gap-2'>
            <div className='flex-1 min-w-0'>
              <Text
                variant='sm'
                weight='semibold'
                className='text-slate-700 block truncate mb-0.5 max-w-48 w-full'>
                {resumeName}
              </Text>
              <Text variant='xs' weight='medium' className='text-slate-400'>
                {tc('edited')} {formattedDate}
              </Text>
            </div>
          </div>
        </div>
      </Link>
      <ResumeMenu name={resumeName} id={id} resume={resume} />
    </div>
  )
}
