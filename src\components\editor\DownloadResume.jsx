'use client'

import { useCallback, useMemo, useState } from 'react'
import blobStream from 'blob-stream'
import { Download, Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import PDFDocument from 'pdfkit/js/pdfkit.standalone'
import satori from 'satori'
import { initResvgWorker } from '@/utils/initResvgWorker'
import { Button } from '@components'
import { pageSizes } from '@constants'
import { useResume } from '@hooks'
import { getClientSideFonts } from '@utils'

const {
  A4: { width, height }
} = pageSizes

export const DownloadResume = () => {
  const tc = useTranslations('Common')
  const [isLoading, setIsLoading] = useState(false)
  const { resume } = useResume()
  const renderPNG = useMemo(() => initResvgWorker(), [])

  const handleDownload = useCallback(async () => {
    setIsLoading(true)
    try {
      const pages = document.querySelectorAll('.shadow-resume-page')

      const { default: parse } = await import('html-react-parser')

      const document_ = new PDFDocument({
        compress: false,
        size: [width, height]
      })

      for (const [index, page] of pages.entries()) {
        const pageContent = page.outerHTML
        const jsx = parse(pageContent)

        const result = await satori(jsx, {
          width,
          height,
          fonts: await getClientSideFonts()
        })

        const pngBuffer =
          (await renderPNG?.({
            svg: result,
            width: width * 4
          })) || ''

        document_.image(pngBuffer, 0, 0, {
          width,
          height
        })

        if (index !== pages.length - 1) {
          document_.addPage()
        }
      }
      const resumeName = resume?.name || 'resume'
      const toDayDate = new Date()
      const formattedDate = toDayDate
        .toLocaleDateString('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        })
        .replaceAll('/', '-')

      const fileName = `${resumeName}-${formattedDate}.pdf`

      const stream = document_.pipe(blobStream())

      stream.on('finish', () => {
        const blob = stream.toBlob('application/pdf')
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.append(link)
        link.click()
        link.remove()
        URL.revokeObjectURL(url)
      })

      document_.end()
    } catch (error) {
      console.error('Error generating SVG:', error)
    }
    setIsLoading(false)
  }, [resume, renderPNG])

  return (
    <Button size='md' onClick={handleDownload} disabled={isLoading}>
      {isLoading ? (
        <Loader2 size={16} className='animate-spin' />
      ) : (
        <Download size={16} />
      )}
      <span className='hidden lg:block'>{tc('download')}</span>
    </Button>
  )
}
