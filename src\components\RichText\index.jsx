import React from 'react'
import { convertLexicalToReact } from '@/utils'

export const RichText = ({ data, className = '', enableGutter = true }) => {
  if (!data) {
    return null
  }

  // Handle Lexical format (new format with root.children structure)
  if (data.root && data.root.children) {
    const convertedContent = convertLexicalToReact(data)
    if (!convertedContent) {
      return null
    }
    return (
      <div className={`rich-text ${enableGutter ? 'prose max-w-none' : ''} ${className}`}>
        {convertedContent}
      </div>
    )
  }

  // Handle old array format
  if (!Array.isArray(data)) {
    return null
  }

  const renderNode = (node, index) => {
    if (typeof node === 'string') {
      return <span key={index}>{node}</span>
    }

    if (node.type === 'paragraph') {
      return (
        <p key={index} className='mb-4'>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </p>
      )
    }

    if (node.type === 'heading') {
      const HeadingTag = `h${node.tag || 1}`
      return (
        <HeadingTag key={index} className='mb-4 font-bold'>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </HeadingTag>
      )
    }

    if (node.type === 'list') {
      const ListTag = node.listType === 'ordered' ? 'ol' : 'ul'
      return (
        <ListTag key={index} className='mb-4 ml-6'>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </ListTag>
      )
    }

    if (node.type === 'list-item') {
      return (
        <li key={index} className='mb-2'>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </li>
      )
    }

    if (node.type === 'link') {
      return (
        <a key={index} href={node.url} className='text-blue-600 hover:underline'>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </a>
      )
    }

    if (node.bold) {
      return (
        <strong key={index}>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </strong>
      )
    }

    if (node.italic) {
      return (
        <em key={index}>
          {node.children?.map((child, childIndex) => renderNode(child, childIndex))}
        </em>
      )
    }

    if (node.text) {
      return <span key={index}>{node.text}</span>
    }

    // Fallback for unknown node types
    if (node.children) {
      return (
        <span key={index}>
          {node.children.map((child, childIndex) => renderNode(child, childIndex))}
        </span>
      )
    }

    return null
  }

  return (
    <div className={`rich-text ${enableGutter ? 'prose max-w-none' : ''} ${className}`}>
      {data.map((node, index) => renderNode(node, index))}
    </div>
  )
}
