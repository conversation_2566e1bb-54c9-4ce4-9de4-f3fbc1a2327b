import { Suspense } from 'react'
import { redirect } from 'next/navigation'
import {
  AllResumes,
  ErrorBoundary,
  RecentResumes,
  ResumeGridSkelton,
  TopBar
} from '@components'
import { routes } from '@constants'
import { getCurrentUser } from '@context'

export default async function Dashboard() {
  // Check authentication on the server side
  try {
    const user = await getCurrentUser()
    if (!user) {
      redirect(routes.signIn)
    }
  } catch {
    // If getCurrentUser fails, redirect to sign-in
    redirect(routes.signIn)
  }

  return (
    <>
      <TopBar />
      <div className='flex-1 flex flex-col overflow-y-auto'>
        <ErrorBoundary>
          <Suspense fallback={<ResumeGridSkelton items={4} />}>
            <RecentResumes />
          </Suspense>
        </ErrorBoundary>
        <ErrorBoundary>
          <Suspense fallback={<ResumeGridSkelton items={4} />}>
            <AllResumes />
          </Suspense>
        </ErrorBoundary>
      </div>
    </>
  )
}
