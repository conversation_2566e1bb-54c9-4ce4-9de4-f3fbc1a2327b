import React from 'react'
import { cn } from '@/utilities/ui'

export const CollectionArchive = (props) => {
  const { posts } = props

  return (
    <div className={cn('container')}>
      <div>
        <div className='grid grid-cols-4 sm:grid-cols-8 lg:grid-cols-12 gap-y-4 gap-x-4 lg:gap-y-8 lg:gap-x-8 xl:gap-x-8'>
          {posts?.map((result, index) => {
            if (typeof result === 'object' && result !== null) {
              return (
                <div className='col-span-4' key={index}>
                  <div className='h-full bg-white border rounded-lg p-4 shadow-sm'>
                    <h3 className='text-lg font-semibold mb-2'>{result.title}</h3>
                    {result.description && (
                      <p className='text-sm text-gray-600 mb-3'>{result.description}</p>
                    )}
                    {result.categories && result.categories.length > 0 && (
                      <div className='flex flex-wrap gap-1 mb-3'>
                        {result.categories.map((category, catIndex) => (
                          <span
                            key={catIndex}
                            className='px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded'>
                            {category.title}
                          </span>
                        ))}
                      </div>
                    )}
                    <div className='text-xs text-gray-500'>
                      {new Date(result.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              )
            }

            return null
          })}
        </div>
      </div>
    </div>
  )
}
