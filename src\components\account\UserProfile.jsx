'use client'

import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@components'
import { validationSchemas } from '@constants'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'

const userDetailsSchema = z.object({
  firstName: validationSchemas.firstName,
  lastName: validationSchemas.lastName.optional().or(z.literal(''))
})

export const UserDetails = () => {
  const { currentUser, updateUserById } = useAuth()
  const t = useTranslations('Errors')
  const tc = useTranslations('Common')

  const form = useForm({
    resolver: zodResolver(userDetailsSchema),
    defaultValues: {
      firstName: currentUser.firstName,
      lastName: currentUser.lastName
    },
    mode: 'onBlur'
  })

  const handleFieldBlur = async (name, value) => {
    // Only check for empty value on firstName field
    if (name === 'firstName' && !value?.trim()) {
      form.setValue(name, currentUser[name], {
        shouldValidate: true
      })
      return
    }

    // Skip if value hasn't changed
    if (value === currentUser[name]) return

    form.setValue(name, value, {
      shouldValidate: true,
      shouldDirty: true
    })

    try {
      const isValid = await form.trigger(name)
      if (!isValid) return

      toast.promise(
        updateUserById({
          id: currentUser.id,
          [name]: value
        }),
        {
          loading: tc('updating') + '...',
          success: tc('updatedSuccessfully'),
          error: t('updateFailed')
        }
      )
    } catch (error) {
      console.error('Update failed:', error)
    }
  }

  return (
    <Form {...form}>
      <form className='flex gap-2.5'>
        <div className='w-1/2'>
          <FormField
            control={form.control}
            name='firstName'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-slate-600'>{tc('firstName')}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder={tc('firstName')}
                    onBlur={(event) => handleFieldBlur('firstName', event.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='w-1/2'>
          <FormField
            control={form.control}
            name='lastName'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-slate-600'>{tc('lastName')}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder={tc('lastName')}
                    onBlur={(event) => handleFieldBlur('lastName', event.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  )
}
