'use client'

import {
  AnimatePresence,
  Reorder,
  motion,
  useDragControls,
  useMotionValue
} from 'motion/react'
import {
  EditorSectionDetails,
  EditorSectionPanel,
  EditorSectionReorder,
  EditorSectionTrigger,
  EditorSectionVisibility
} from '@components'
import { useEditor, useRaisedShadow } from '@hooks'
import { resumeOptionValue } from '@utils'

export const EditorPanelItem = ({ section }) => {
  const { handlePanelClick, sectionsVisibility, visiblityControls } = useEditor()
  const { id, sectionKey } = section
  const isVisible = id in sectionsVisibility ? sectionsVisibility[id] : section.isVisible
  const isPersonalDetails = sectionKey === resumeOptionValue.personalDetails
  const y = useMotionValue(0)
  const boxShadow = useRaisedShadow(y)
  const dragControls = useDragControls()

  return (
    <Reorder.Item
      id={id}
      value={section}
      dragListener={false}
      className='relative'
      style={{ boxShadow, y }}
      dragControls={dragControls}>
      <EditorSectionPanel isVisible={isVisible}>
        <EditorSectionReorder
          dragControls={dragControls}
          isVisible={isVisible}
          className={isPersonalDetails ? 'invisible' : ''}
        />
        <EditorSectionDetails
          section={section}
          isVisible={isVisible}
          onClick={() => handlePanelClick(sectionKey)}
        />
        <AnimatePresence mode='wait'>
          <motion.div
            key={`${isPersonalDetails ? 'personal-details' : `animated-${visiblityControls}`}`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            className='absolute right-0 inset-y-0'>
            {visiblityControls && !isPersonalDetails ? (
              <EditorSectionVisibility section={section} isVisible={isVisible} />
            ) : (
              <EditorSectionTrigger
                isVisible={isVisible}
                handlePanelClick={() => handlePanelClick(sectionKey)}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </EditorSectionPanel>
    </Reorder.Item>
  )
}
