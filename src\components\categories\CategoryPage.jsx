'use client'

import Link from 'next/link'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import { RichText } from '@/components/RichText'
import { buildCategoryTree } from '@/utils/categoryUtils'
import { CategoryBreadcrumbs, CategoryTree } from './index'

export const CategoryPage = ({
  categories,
  currentCategory,
  posts,
  currentPost,
  isPreview = false,
  isDraft = false
}) => {
  // Build hierarchical tree from flat categories
  const categoryTree = buildCategoryTree(categories)

  // Get child categories of current category
  const childCategories = currentCategory
    ? buildCategoryTree(categories, currentCategory.id)
    : categoryTree

  // If we have a current post, display it
  if (currentPost) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='max-w-4xl mx-auto'>
          {isDraft && <LivePreviewListener />}

          {/* Breadcrumbs */}
          <nav className='flex items-center space-x-2 text-sm text-gray-600 mb-4'>
            <Link href='/posts' className='hover:text-gray-900'>
              Posts
            </Link>
            <span>/</span>
            <span>{currentPost.title}</span>
          </nav>

          {/* Post content */}
          <article className='bg-white border rounded-lg p-8'>
            {isPreview && (
              <div className='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>
                <strong>Preview Mode:</strong> This is a preview of your post. It may not
                be published yet.
              </div>
            )}
            <h1 className='text-4xl font-bold text-gray-900 mb-4'>{currentPost.title}</h1>

            {currentPost.heroImage && (
              <div className='mb-6'>
                <img
                  src={currentPost.heroImage.url}
                  alt={currentPost.heroImage.alt || currentPost.title}
                  className='w-full h-64 object-cover rounded-lg'
                />
              </div>
            )}

            {currentPost.content && (
              <div className='prose max-w-none'>
                <RichText data={currentPost.content} />
              </div>
            )}

            <div className='mt-8 pt-6 border-t border-gray-200'>
              <div className='flex items-center text-sm text-gray-500'>
                <span>{new Date(currentPost.createdAt).toLocaleDateString()}</span>
                {currentPost.categories && currentPost.categories.length > 0 && (
                  <>
                    <span className='mx-2'>•</span>
                    <span>
                      {currentPost.categories.map((cat, index) => (
                        <span key={cat.id || index}>
                          {index > 0 && ', '}
                          <Link
                            href={`/categories/${cat.slug}`}
                            className='hover:text-gray-700'>
                            {cat.title}
                          </Link>
                        </span>
                      ))}
                    </span>
                  </>
                )}
              </div>
            </div>
          </article>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
        {/* Sidebar with category tree */}
        <div className='lg:col-span-1'>
          <CategoryTree categories={categoryTree} title='All Categories' />
        </div>

        {/* Main content */}
        <div className='lg:col-span-3'>
          {/* Breadcrumbs */}
          {currentCategory && <CategoryBreadcrumbs category={currentCategory} />}

          {/* Page title */}
          <h1 className='text-3xl font-bold text-gray-900 mb-6'>
            {currentCategory ? currentCategory.title : 'All Categories'}
          </h1>

          {/* Child categories if any */}
          {childCategories && childCategories.length > 0 && !posts && (
            <div className='mb-8'>
              <h2 className='text-xl font-semibold text-gray-900 mb-4'>Subcategories</h2>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {childCategories.map((category) => (
                  <div
                    key={category.id}
                    className='bg-white border rounded-lg p-4 hover:shadow-md transition-shadow'>
                    <h3 className='font-semibold text-gray-900 mb-2'>
                      <Link href={`/categories/${category.slug}`}>{category.title}</Link>
                    </h3>
                    {category.children && category.children.length > 0 && (
                      <p className='text-sm text-gray-600'>
                        {category.children.length} subcategorie(s)
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Posts in this category */}
          {posts && posts.length > 0 && (
            <div>
              <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                Posts in {currentCategory?.title || 'All Categories'}
              </h2>
              <div className='space-y-4'>
                {posts.map((post) => (
                  <article
                    key={post.id}
                    className='bg-white border rounded-lg p-6 hover:shadow-md transition-shadow'>
                    <h3 className='text-xl font-semibold mb-2'>
                      <Link
                        href={`/posts/${post.slug}`}
                        className='text-gray-900 hover:text-blue-600'>
                        {post.title}
                      </Link>
                    </h3>
                    {post.excerpt && <p className='text-gray-600 mb-3'>{post.excerpt}</p>}
                    <div className='flex items-center text-sm text-gray-500'>
                      <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                      {post.categories && post.categories.length > 0 && (
                        <>
                          <span className='mx-2'>•</span>
                          <span>
                            {post.categories.map((cat, index) => (
                              <span key={cat.id || index}>
                                {index > 0 && ', '}
                                <Link
                                  href={`/categories/${cat.slug}`}
                                  className='hover:text-gray-700'>
                                  {cat.title}
                                </Link>
                              </span>
                            ))}
                          </span>
                        </>
                      )}
                    </div>
                  </article>
                ))}
              </div>
            </div>
          )}

          {/* Empty state */}
          {(!posts || posts.length === 0) &&
            (!childCategories || childCategories.length === 0) && (
              <div className='text-center py-12'>
                <p className='text-gray-600'>
                  {currentCategory
                    ? `No posts found in ${currentCategory.title}`
                    : 'No categories available'}
                </p>
              </div>
            )}
        </div>
      </div>
    </div>
  )
}
