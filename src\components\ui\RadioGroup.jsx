'use client'

import * as React from 'react'
import { cva } from 'class-variance-authority'
import { Circle } from 'lucide-react'
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group'
import { cn } from '@utils'

const radioGroupVariants = cva('', {
  variants: {
    variant: {
      default: 'grid gap-2',
      fancy: 'flex gap-1 p-1 border border-slate-200 rounded bg-white'
    }
  },
  defaultVariants: {
    variant: 'default'
  }
})

const RadioGroup = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn(radioGroupVariants({ variant: 'default' }), className)}
      {...props}
      ref={ref}
    />
  )
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const RadioGroupItem = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        'aspect-square h-4 w-4 rounded-full border border-slate-200 text-slate-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-slate-950 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:text-slate-50 dark:focus-visible:ring-slate-300',
        className
      )}
      {...props}>
      <RadioGroupPrimitive.Indicator className='flex items-center justify-center'>
        <Circle className='h-3.5 w-3.5 fill-primary' />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  )
})

RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

export { RadioGroup, RadioGroupItem }
