'use client'

import { useRef } from 'react'
import { useEventListener } from '@hooks'

const onScroll = (event) => {
  const element = event.target
  if (element.scrollTop > 10) {
    element.classList.add('scrolled')
  } else {
    element.classList.remove('scrolled')
  }
}

export const LayoutContainer = ({ children }) => {
  const containerRef = useRef(null)

  useEventListener('scroll', onScroll, containerRef)

  return (
    <div
      ref={containerRef}
      className='flex-auto flex h-full overflow-y-auto thin-scrollbar'>
      {children}
    </div>
  )
}
