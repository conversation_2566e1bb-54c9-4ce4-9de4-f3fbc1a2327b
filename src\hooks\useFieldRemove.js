import { useCallback } from 'react'
import { useResume } from '@hooks'
import { removeId } from '@utils'

export const useFieldRemove = ({ remove, sectionKey, data }) => {
  const { updateResume } = useResume()
  const handleRemove = useCallback(
    (index) => {
      const currentItem = data?.[index]
      if (!currentItem) {
        remove(index)
        return
      }

      let updatedArray = data
        .filter((_, index_) => index_ !== index)
        .map((item) => removeId(item))
      remove(index)
      updateResume({ [sectionKey]: updatedArray })
    },
    [remove, sectionKey, data, updateResume]
  )
  return handleRemove
}
