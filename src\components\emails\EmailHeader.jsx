import * as React from 'react'
import { Head, Img, Section } from '@react-email/components'

export const EmailHeader = () => {
  return (
    <>
      <Head>
        <link rel='preconnect' href='https://fonts.googleapis.com' />
        <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='true' />
        {/* eslint-disable-next-line @next/next/no-page-custom-font */}
        <link
          href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap'
          rel='stylesheet'
        />
      </Head>
      <Section>
        <Img
          src={`${process.env.NEXT_PUBLIC_URL}/images/logo.png`}
          width='150'
          height='auto'
          alt='Swift Resume'
          className='my-0'
        />
      </Section>
    </>
  )
}
