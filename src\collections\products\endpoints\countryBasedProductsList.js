export const countryBasedProductsList = {
  path: '/country-based-products-list',
  method: 'get',
  summary: 'Get country-based products list',
  handler: async (request) => {
    try {
      // 1. Get country from headers (like geoLocation API)
      const country = request.headers.get('x-vercel-ip-country') || 'US'
      console.log('Detected country:', country)

      // 2. Validate country and default to US if invalid
      const validCountry = country && country.length === 2 ? country : 'US'
      console.log('Valid country:', validCountry)

      // 3. Map country to currency
      const countryCurrencyMap = {
        US: 'usd',
        CA: 'cad',
        GB: 'gbp',
        EU: 'eur',
        AU: 'aud',
        IN: 'inr',
        JP: 'jpy',
        DE: 'eur',
        FR: 'eur',
        IT: 'eur',
        ES: 'eur',
        NL: 'eur',
        SE: 'sek',
        NO: 'nok',
        DK: 'dkk',
        CH: 'chf',
        AT: 'eur',
        BE: 'eur',
        IE: 'eur',
        FI: 'eur',
        PT: 'eur',
        GR: 'eur',
        PL: 'pln',
        CZ: 'czk',
        HU: 'huf',
        RO: 'ron',
        BG: 'bgn',
        HR: 'hrk',
        SK: 'skk',
        SI: 'eur',
        EE: 'eur',
        LV: 'eur',
        LT: 'eur',
        LU: 'eur',
        MT: 'eur',
        CY: 'eur'
      }

      const currency = countryCurrencyMap[validCountry] || 'usd'
      console.log('Currency:', currency)

      // 4. Get currency symbol
      const currencySymbolMap = {
        usd: '$',
        cad: 'C$',
        gbp: '£',
        eur: '€',
        aud: 'A$',
        inr: '₹',
        jpy: '¥',
        sek: 'kr',
        nok: 'kr',
        dkk: 'kr',
        chf: 'CHF',
        pln: 'zł',
        czk: 'Kč',
        huf: 'Ft',
        ron: 'lei',
        bgn: 'лв',
        hrk: 'kn',
        skk: 'Sk'
      }

      const symbol = currencySymbolMap[currency] || '$'
      console.log('Symbol:', symbol)

      // 5. Get all products using Payload directly (like OTP endpoint)
      console.log('Fetching products from database...')
      const productsResult = await request.payload.find({
        collection: 'products',
        limit: 100 // Get up to 100 products
      })

      const products = productsResult.docs || []
      console.log('Total products found:', products.length)
      console.log(
        'Products:',
        products.map((p) => ({ id: p.id, title: p.title, hasPriceJSON: !!p.priceJSON }))
      )

      if (!products || !Array.isArray(products) || products.length === 0) {
        console.log('No products found in database')
        return Response.json(
          {
            error: 'No products found',
            country: validCountry,
            currency,
            symbol,
            products: []
          },
          { status: 404 }
        )
      }

      // 6. Process each product - Return complete product objects with filtered currency options
      const processedProducts = products.map((product) => {
        try {
          console.log('Processing product:', product.id, JSON.stringify(product))

          // Start with the complete product object
          const productData = { ...product }

          // Filter priceJSON to only include current country's currency option
          if (product.priceJSON) {
            try {
              const priceJSON = JSON.parse(product.priceJSON)
              if (priceJSON.data && priceJSON.data[0]) {
                const originalCurrencyOptions = priceJSON.data[0].currency_options || {}
                const countryPrice = originalCurrencyOptions[currency]

                if (countryPrice) {
                  // Create a new priceJSON with only the current country's currency option
                  const filteredPriceJSON = {
                    ...priceJSON,
                    data: [
                      {
                        ...priceJSON.data[0],
                        currency_options: {
                          [currency]: countryPrice
                        }
                      }
                    ]
                  }
                  productData.priceJSON = filteredPriceJSON
                } else {
                  // If no pricing for this country, keep original but clear currency_options
                  const filteredPriceJSON = {
                    ...priceJSON,
                    data: [
                      {
                        ...priceJSON.data[0],
                        currency_options: {}
                      }
                    ]
                  }
                  productData.priceJSON = filteredPriceJSON
                }
              }
            } catch (error) {
              console.error('Error parsing priceJSON for product:', product.id, error)
              // Keep original priceJSON if parsing fails
              try {
                productData.priceJSON = JSON.parse(product.priceJSON)
              } catch {
                productData.priceJSON = product.priceJSON
              }
            }
          }

          // Filter trialpriceJSON to only include current country's currency option
          if (product.trialpriceJSON) {
            try {
              const trialPriceJSON = JSON.parse(product.trialpriceJSON)
              if (trialPriceJSON.data && trialPriceJSON.data[0]) {
                const originalTrialCurrencyOptions =
                  trialPriceJSON.data[0].currency_options || {}
                const trialCountryPrice = originalTrialCurrencyOptions[currency]

                if (trialCountryPrice) {
                  // Create a new trialpriceJSON with only the current country's currency option
                  const filteredTrialPriceJSON = {
                    ...trialPriceJSON,
                    data: [
                      {
                        ...trialPriceJSON.data[0],
                        currency_options: {
                          [currency]: trialCountryPrice
                        }
                      }
                    ]
                  }
                  productData.trialpriceJSON = filteredTrialPriceJSON
                } else {
                  // If no trial pricing for this country, keep original but clear currency_options
                  const filteredTrialPriceJSON = {
                    ...trialPriceJSON,
                    data: [
                      {
                        ...trialPriceJSON.data[0],
                        currency_options: {}
                      }
                    ]
                  }
                  productData.trialpriceJSON = filteredTrialPriceJSON
                }
              }
            } catch (error) {
              console.error(
                'Error parsing trialpriceJSON for product:',
                product.id,
                error
              )
              // Keep original trialpriceJSON if parsing fails
              try {
                productData.trialpriceJSON = JSON.parse(product.trialpriceJSON)
              } catch {
                productData.trialpriceJSON = product.trialpriceJSON
              }
            }
          }

          return productData
        } catch (error) {
          console.error('Error processing product:', error)
          return {
            ...product,
            error: 'Failed to process product'
          }
        }
      })

      console.log('Processed products:', processedProducts.length)

      // 7. Return response with all products and country-specific pricing
      return Response.json({
        country: validCountry,
        currency,
        symbol,
        products: processedProducts,
        total: processedProducts.length
      })
    } catch (error) {
      console.error('Error in country-based products list:', error)
      return Response.json(
        {
          error: 'Failed to fetch products',
          message: error.message
        },
        { status: 500 }
      )
    }
  }
}
