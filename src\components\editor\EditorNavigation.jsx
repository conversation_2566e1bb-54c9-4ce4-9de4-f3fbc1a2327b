'use client'

import { <PERSON>, ChevronLeft, Eye, Repeat2, SwatchBook } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button, Text } from '@components'
import { useEditor, useResume } from '@hooks'
import { resumeOptionValue } from '@utils'

export const EditorNavigation = () => {
  const { isSheetActive, visiblityControls, activeTab } = useEditor()
  const tc = useTranslations('Common')

  return (
    <div className='px-4 py-3 bg-slate-50 border-b border-slate-200 flex items-center justify-between'>
      {activeTab === 'editor' ? (
        <>
          {isSheetActive ? (
            <>
              <BackButton />
              <Text
                as='span'
                variant='xs'
                weight='medium'
                className='text-slate-700 lg:hidden'>
                {tc('resumeSections')}
              </Text>
            </>
          ) : (
            <Text as='span' variant='xs' weight='medium' className='text-slate-700'>
              {tc('resumeSections')}
            </Text>
          )}
          <div className='flex items-center gap-2.5'>
            {visiblityControls ? <SaveButton /> : <VisibilityButton />}
          </div>
        </>
      ) : (
        <>
          <Text as='span' variant='xs' weight='medium' className='text-slate-700'>
            {tc('designYourResume')}
          </Text>
          <div className='flex items-center gap-2.5'>
            <RandomizeButton onRandomize={() => {}} />
            <ChooseTemplateButton onChooseTemplate={() => {}} />
          </div>
        </>
      )}
    </div>
  )
}

export const BackButton = () => {
  const tc = useTranslations('Common')
  const { handleBack } = useEditor()
  return (
    <Button
      variant='subtle'
      size='link'
      className='text-xs gap-1.5 hidden lg:flex'
      onClick={handleBack}>
      <ChevronLeft size={16} />
      {tc('back')}
    </Button>
  )
}

export const SaveButton = () => {
  const { saveItemsVisibility } = useEditor()
  const { isUpdatingResume } = useResume()
  const tc = useTranslations('Common')
  return (
    <Button
      variant='subtle'
      size='link'
      onClick={saveItemsVisibility}
      isDisabled={isUpdatingResume}
      className='text-xs gap-1'>
      <Check />
      {tc('save')}
    </Button>
  )
}

export const VisibilityButton = () => {
  const tc = useTranslations('Common')
  const { isUpdatingResume } = useResume()
  const { showVisibilityControls, activePanelId, isSheetActive } = useEditor()

  if (
    isSheetActive &&
    (activePanelId === resumeOptionValue.personalDetails ||
      activePanelId === resumeOptionValue.skills)
  ) {
    return null
  }
  return (
    <Button
      variant='subtle'
      size='link'
      onClick={showVisibilityControls}
      isDisabled={isUpdatingResume}
      className='text-xs gap-1'>
      <Eye />
      {tc('visibility')}
    </Button>
  )
}

export const RandomizeButton = ({ onRandomize }) => {
  const tc = useTranslations('Common')
  return (
    <Button
      variant='subtle'
      size='link'
      onClick={onRandomize}
      className='text-xs gap-1 group transition-colors'>
      <Repeat2 className='text-slate-600 group-hover:text-slate-500' />
      <span className='hidden lg:block'>{tc('randomize')}</span>
    </Button>
  )
}

export const ChooseTemplateButton = ({ onChooseTemplate }) => {
  const tc = useTranslations('Common')
  return (
    <Button
      variant='subtle'
      size='link'
      onClick={onChooseTemplate}
      className='text-xs gap-1 group transition-colors'>
      <SwatchBook className='text-slate-900 group-hover:text-slate-500' />
      <span className='hidden lg:block'>{tc('chooseATemplate')}</span>
    </Button>
  )
}
