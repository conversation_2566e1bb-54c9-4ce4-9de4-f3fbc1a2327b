'use client'

import { useController } from 'react-hook-form'
import { RichTextField } from './RichTextField'

export const RichTextFieldController = ({ name, control, placeholder }) => {
  const { field } = useController({
    name,
    control,
    defaultValue: null
  })

  return (
    <RichTextField
      value={field.value}
      onChange={field.onChange}
      placeholder={placeholder}
    />
  )
}
