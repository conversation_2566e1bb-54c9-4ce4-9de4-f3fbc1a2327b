'use client'

import { Check } from 'lucide-react'
import {
  Avatar,
  AvatarImage,
  FormControl,
  FormItem,
  FormLabel,
  RadioGroup,
  RadioGroupItem
} from '@components'
import { useResume } from '@hooks'

export const FancyAvtarGroup = ({ optionsData, value, onChange }) => {
  const { options, styles } = optionsData
  const {
    resume: { profileImage }
  } = useResume()
  const thumbnail = profileImage?.thumbnailURL || profileImage?.url || null

  return (
    <RadioGroup value={value} onValueChange={onChange} className='flex gap-3'>
      {options.map((option, index) => {
        return (
          <FormItem key={`${option.value}-${index}`}>
            <FormControl className='sr-only'>
              <RadioGroupItem
                value={option.value}
                id={option.value}
                className='sr-only'
              />
            </FormControl>
            <FormLabel
              htmlFor={option.value}
              className='cursor-pointer relative flex items-center justify-center'>
              <Avatar
                style={styles?.[option.value]}
                className='size-11 bg-slate-200 flex items-center justify-center'>
                {thumbnail && <AvatarImage src={thumbnail} alt='profile image' />}
                {value === option.value && (
                  <span className='absolute flex items-center justify-center size-6 rounded-full bg-primary'>
                    <Check size={18} className='text-white' />
                  </span>
                )}
              </Avatar>
            </FormLabel>
          </FormItem>
        )
      })}
    </RadioGroup>
  )
}
