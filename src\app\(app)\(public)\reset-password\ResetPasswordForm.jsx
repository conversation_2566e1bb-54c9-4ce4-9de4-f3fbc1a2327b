'use client'

import { useEffect } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import {
  Button,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Text
} from '@components'
import { auth, routes, validationSchemas } from '@constants'
import { resetPassword } from '@data'
import { SwiftError } from '@error'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'
import { useMutation } from '@tanstack/react-query'
import { withPasswordValidation } from '@utils'

const { tokenExpired } = auth.errors

const resetPasswordSchema = withPasswordValidation(
  z.object({
    password: validationSchemas.password,
    confirmPassword: validationSchemas.confirmPassword
  })
)

export const ResetPasswordForm = () => {
  const tc = useTranslations('Common')
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const { currentUser } = useAuth()

  const form = useForm({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: ''
    }
  })

  const {
    mutate: resetPasswordMutation,
    isPending,
    error,
    isSuccess
  } = useMutation({
    mutationFn: async (data) => {
      const response = await resetPassword({ token, password: data.password })
      return response
    },
    onSuccess: (data) => {
      // Redirect to sign in page after 3 seconds
      if (data?.user?.email) {
        const redirectUrl = currentUser
          ? routes.dashboard
          : routes.signIn + '?email=' + data.user.email
        setTimeout(() => {
          router.push(redirectUrl)
        }, 3000)
      }
    }
  })

  const onSubmit = (data) => {
    if (!token) {
      throw new SwiftError(tokenExpired)
    }
    resetPasswordMutation(data)
  }

  // if no token found redirect to sign in page
  useEffect(() => {
    if (!token) {
      router.push(routes.signIn)
    }
  }, [token, router])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <div className='space-y-4'>
          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('password')}</FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder={tc('passwordPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='confirmPassword'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('confirmPassword')}</FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder={tc('confirmPasswordPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='space-y-2'>
          <Button
            type='submit'
            className='w-full'
            isLoading={isPending}
            disabled={isPending}>
            {isPending ? (
              <>
                <Loader2 className='animate-spin' /> {tc('resetting')}...
              </>
            ) : (
              tc('resetPassword')
            )}
          </Button>
        </div>

        <Text as='div' variant='sm' weight='medium' className='mt-2 block text-center'>
          {tc('returnToSignIn')}{' '}
          <Link className='text-primary' href={routes.signIn}>
            {tc('signInLink')}
          </Link>
        </Text>

        <ErrorDisplay error={error} />
        {isSuccess && (
          <Text
            as='div'
            variant='sm'
            weight='medium'
            className='text-green-600 text-center'>
            {tc('passwordResetSuccessfully')}
          </Text>
        )}
      </form>
    </Form>
  )
}
