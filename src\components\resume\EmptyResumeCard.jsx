import { EmptyResumeCardImage } from '@components'
import { CreateResumeButton } from '@components/CreateResumeButton'
import { cn } from '@utils'

export const EmptyResumeCard = ({ className, imageClass, btnClass }) => {
  return (
    <div
      className={cn(
        'relative flex-shrink-0 shadow-btn-primary border-2 border-blue-500 rounded-lg overflow-hidden',
        className
      )}
      style={{
        background:
          'linear-gradient(0.45deg, #FFFFFF 10.31%, #F7FAFF 60.42%, #D6E5FF 99.61%)',
        boxShadow:
          '1px 4px 20px 0px rgba(159, 160, 176, 0.1), 0px -1px 9px 0px rgba(153, 153, 153, 0.07), 0px 1px 0px 0px rgba(255, 255, 255, 0.35)'
      }}>
      <EmptyResumeCardImage
        className={cn('border-2 border-white rounded-lg px-7 pt-7', className)}
        imageClass={imageClass}
      />
      <div className={cn('absolute bottom-6 left-1/2 -translate-x-1/2', btnClass)}>
        <CreateResumeButton />
      </div>
    </div>
  )
}
