import { isAdmin } from '@collections/access'
import { isValidEmail } from '@collections/validations'
import { collections } from '@constants'
import { requestOtp, verifyOtp } from './endpoints'

export const otp = {
  slug: collections.otp.slug,
  admin: {
    useAsTitle: 'email'
  },
  access: {
    create: isAdmin,
    read: isAdmin,
    update: isAdmin,
    delete: isAdmin
  },
  timestamps: true,
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      validate: isValidEmail
    },
    {
      name: 'otp',
      type: 'text',
      required: true
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true
    }
  ],
  endpoints: [requestOtp, verifyOtp]
}
