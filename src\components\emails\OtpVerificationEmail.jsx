import * as React from 'react'
import { emailTheme } from '@constants'
import {
  Body,
  Container,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text
} from '@react-email/components'
import { EmailFooter } from './EmailFooter'
import { EmailHeader } from './EmailHeader'

export const OTPVerificationEmail = ({ firstName, email, otp }) => {
  return (
    <Html>
      <Preview>Change email verification</Preview>
      <Tailwind config={emailTheme}>
        <Body className='bg-white my-auto mx-auto font-inter px-2'>
          <Container className='my-10 mx-auto p-5 max-w-[465px]'>
            <EmailHeader />

            <Heading className='text-2xl font-normal text-slate my-8'>
              Change email verification
            </Heading>

            <Text className='text-base text-slate'>Hi {firstName},</Text>

            <Text className='text-base text-slate'>
              We received a request to change the email on this account. If you made this
              request, you can update your email using the following OTP:
            </Text>

            <Section className='my-8'>
              <div className='border border-solid border-gray-200 rounded-md p-7 text-center'>
                <Text className='text-4xl font-bold text-slate tracking-wider m-0'>
                  {otp}
                </Text>
              </div>
            </Section>
            <Text className='text-base text-slate'>
              If you didn&apos;t try to change your account email address, you can safely
              ignore this email.
            </Text>

            <EmailFooter email={email} />
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}
