'use client'

import { useContext } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { ResumeContext } from '@context'

export const KeyboardShortcutsEditorProvider = () => {
  const { undo, redo } = useContext(ResumeContext)

  const options = {
    enableOnFormTags: ['INPUT', 'TEXTAREA']
  }

  useHotkeys(
    ['meta+z', 'ctrl+z'],
    (event) => {
      event.preventDefault()
      undo()
    },
    options
  )

  useHotkeys(
    ['meta+shift+z', 'ctrl+y'],
    (event) => {
      event.preventDefault()
      redo()
    },
    options
  )

  return null
}
