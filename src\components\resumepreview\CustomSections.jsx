import { convertLexicalToReact, months } from '@utils'
import {
  MonthYear,
  RichText,
  SectionContainer,
  SectionItem,
  SectionItems,
  SectionTitle,
  SectionTop,
  SubTitle,
  Title
} from './ui'

export const CustomSections = ({ resumeData, location, mt }) => {
  const {
    customSections,
    design: { layout, palette, headingUnderlineStyle }
  } = resumeData

  const visibleCustomSections = customSections.filter((item) => item.isVisible)

  if (!visibleCustomSections?.length) return null

  return (
    <SectionContainer layout={layout}>
      <SectionTitle
        location={location}
        layout={layout}
        palette={palette}
        headingUnderlineStyle={headingUnderlineStyle}
      />
      <SectionItems layout={layout}>
        {visibleCustomSections.map((item, index) => {
          const { title, subTitle, startMonth, startYear, showDate, description } = item

          const startMonthLabel = months.find(
            (month) => month.value === startMonth
          )?.label

          const startDate = startMonthLabel
            ? `${mt(startMonthLabel)} ${startYear}`
            : startYear

          const htmlContent = convertLexicalToReact(description)

          return (
            <SectionItem key={`custom-${index}`}>
              <SectionTop>
                <Title palette={palette} location={location}>
                  {title}
                </Title>
                {subTitle && (
                  <SubTitle palette={palette} location={location}>
                    {subTitle}
                  </SubTitle>
                )}
                {showDate && (startMonth || startYear) && (
                  <MonthYear palette={palette} location={location}>
                    {startDate}
                  </MonthYear>
                )}
              </SectionTop>

              {htmlContent && (
                <RichText palette={palette} location={location}>
                  {htmlContent}
                </RichText>
              )}
            </SectionItem>
          )
        })}
      </SectionItems>
    </SectionContainer>
  )
}
