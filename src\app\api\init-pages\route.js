import { getPayloadClient } from '@/getPayload'

export async function POST() {
  try {
    const payload = await getPayloadClient()

    // Check if any pages exist
    const { docs: existingPages } = await payload.find({
      collection: 'pages',
      limit: 1
    })

    if (existingPages.length === 0) {
      // Create a default home page
      await payload.create({
        collection: 'pages',
        data: {
          title: 'Welcome to Swift',
          slug: 'home',
          _status: 'published',
          layout: []
        }
      })

      // Create a default about page
      await payload.create({
        collection: 'pages',
        data: {
          title: 'About Us',
          slug: 'about',
          _status: 'published',
          layout: []
        }
      })

      return Response.json({
        success: true,
        message: 'Default pages created successfully'
      })
    }

    return Response.json({
      success: true,
      message: 'Pages already exist'
    })
  } catch (error) {
    console.error('Error creating default pages:', error)
    return Response.json(
      {
        success: false,
        error: error.message
      },
      { status: 500 }
    )
  }
}
