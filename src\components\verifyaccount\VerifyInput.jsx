import { useTranslations } from 'next-intl'
import { Text } from '@components'
import { VerifyInputForm } from './VerifyInputForm'

export const VerifyInput = ({ setCurrentStep }) => {
  const t = useTranslations('VerifyModal')
  return (
    <>
      <Text as='h2' weight='semibold' className='text-2xl text-neutral-900 mt-12'>
        {t('title')}
      </Text>
      <Text className='text-neutral-500 mt-2'>{t('description')}</Text>
      <VerifyInputForm setCurrentStep={setCurrentStep} />
    </>
  )
}
