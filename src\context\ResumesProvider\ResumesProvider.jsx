'use client'

import React, { createContext, useEffect, useMemo, useState } from 'react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { createResume, deleteResumeById, updateResumeById } from '@actions'
import { ResumeDeleteModal } from '@components'
import { routes } from '@constants'
import { useMutation } from '@tanstack/react-query'
import { isDev } from '@utils'

export const ResumesContext = createContext()

export const ResumesProvider = ({ children }) => {
  const [resumeId, setResumeId] = useState(null)
  const router = useRouter()

  const createResumeMutation = useMutation({
    mutationFn: async (payload) => {
      const data = await createResume(payload)
      return data
    },
    onSuccess: (resume) => {
      router.push(routes.editor(resume.id))
    }
  })

  const updateResumeMutation = useMutation({
    mutationFn: async (payload) => {
      const data = await updateResumeById(payload)
      return data
    }
  })

  const deleteResumeMutation = useMutation({
    mutationFn: async (id) => {
      return deleteResumeById(id)
    },
    onSuccess: () => {
      toast.success('Resume deleted successfully')
      setResumeId(null)
    },
    onError: () => {
      toast.error('Failed to delete resume')
    }
  })

  const createResumeMutate = createResumeMutation.mutate
  const updateResumeMutate = updateResumeMutation.mutate
  const deleteResumeMutate = deleteResumeMutation.mutate

  const value = useMemo(
    () => ({
      createResume: createResumeMutate,
      isCreatingResume: createResumeMutation.isPending,
      createResumeError: createResumeMutation.error,
      createResumeSuccess: createResumeMutation.isSuccess,

      updateResumeById: updateResumeMutate,
      isUpdatingResume: updateResumeMutation.isPending,
      updateResumeError: updateResumeMutation.error,
      updateResumeSuccess: updateResumeMutation.isSuccess,

      deleteResumeById: (id) => deleteResumeMutate(id),
      isDeletingResume: deleteResumeMutation.isPending,
      deleteResumeError: deleteResumeMutation.error,
      deleteResumeSuccess: deleteResumeMutation.isSuccess,
      setResumeId
    }),
    [
      createResumeMutate,
      createResumeMutation.isPending,
      createResumeMutation.error,
      createResumeMutation.isSuccess,
      updateResumeMutate,
      updateResumeMutation.isPending,
      updateResumeMutation.error,
      updateResumeMutation.isSuccess,
      deleteResumeMutate,
      deleteResumeMutation.isPending,
      deleteResumeMutation.error,
      deleteResumeMutation.isSuccess,
      setResumeId
    ]
  )

  useEffect(() => {
    if (isDev) {
      window.state = window.state || {}
      window.state.useResumes = value
    }
  }, [value])

  return (
    <ResumesContext.Provider value={value}>
      {children}
      <ResumeDeleteModal resumeId={resumeId} setResumeId={setResumeId} />
    </ResumesContext.Provider>
  )
}
