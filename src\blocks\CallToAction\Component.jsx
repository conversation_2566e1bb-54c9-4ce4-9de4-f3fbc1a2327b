import React from 'react'
import { RichText } from '@/components/RichText'

export const CallToAction = ({ content, link, disableInnerContainer = false }) => {
  const ctaContent = (
    <div className='bg-gray-50 border border-gray-200 rounded-lg p-8 my-8 text-center'>
      {content && <RichText data={content} enableGutter={false} />}
      {link && (
        <div className='mt-4'>
          <a
            href={link.url || `/${link.reference?.slug}`}
            className='inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors'
            target={link.newTab ? '_blank' : '_self'}>
            {link.label || 'Learn More'}
          </a>
        </div>
      )}
    </div>
  )

  if (disableInnerContainer) {
    return ctaContent
  }

  return <div className='container my-16'>{ctaContent}</div>
}
