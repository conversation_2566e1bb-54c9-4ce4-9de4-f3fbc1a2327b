import { payload } from '@constants'
import { client } from '@lib/api-sdk/services.gen'

client.setConfig({
  baseUrl: process.env.NEXT_PUBLIC_URL,
  credentials: 'include'
})

const isServer = typeof window === 'undefined'

// Server-side authentication interceptor
if (isServer) {
  client.interceptors.request.use(async (request) => {
    try {
      const { cookies } = await import('next/headers')
      const cookieStore = await cookies()
      const token = cookieStore.get(payload.authTokenId)?.value

      // Set the Authorization header if the token exists
      if (token) {
        request.headers.set('Authorization', `Bearer ${token}`)
      }

      // Also include cookies for server requests (needed for Payload auth)
      const cookieHeader = cookieStore.toString()
      if (cookieHeader) {
        request.headers.set('Cookie', cookieHeader)
      }
    } catch (error) {
      console.warn('Failed to get auth token for server request:', error)
    }

    return request
  })
}

// Headers interceptor for custom headers
client.interceptors.request.use((request) => {
  // Credentials are now set globally in client config
  // No need to modify the request object here
  return request
})

client.interceptors.request.use(async (request) => {
  request.headers.set('x-swift', true)

  return request
})

export * as api from '@lib/api-sdk/services.gen'
