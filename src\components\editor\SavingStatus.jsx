'use client'

import { Check } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { LoadingSpinner, Text } from '@components'
import { useResume } from '@hooks'

export const SavingStatus = () => {
  const tc = useTranslations('Common')
  const { isSaving } = useResume()
  return isSaving ? (
    <div className='flex items-center gap-2'>
      <LoadingSpinner size='xs' />
      <Text variant='xs' weight='medium' className='text-slate-400'>
        {tc('savingChanges')}...
      </Text>
    </div>
  ) : (
    <Text variant='xs' weight='medium' className='flex items-center gap-1 text-slate-400'>
      <Check size={16} /> {tc('allChangesSaved')}
    </Text>
  )
}
