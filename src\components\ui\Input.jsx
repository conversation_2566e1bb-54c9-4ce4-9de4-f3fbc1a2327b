'use client'

import * as React from 'react'
import { cn } from '@utils'

export const Input = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <input
      className={cn(
        'flex w-full rounded-lg border border-slate-300 bg-white px-2.5 py-2 text-base/4 font-normal shadow-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-foreground placeholder:text-slate-500 focus:border-blue-500 focus-visible:outline-none transition-colors disabled:cursor-not-allowed disabled:bg-slate-50 disabled:text-slate-500',
        className
      )}
      ref={ref}
      {...props}
    />
  )
})

Input.displayName = 'Input'
