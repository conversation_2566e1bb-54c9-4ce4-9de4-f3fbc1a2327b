import { formatSlugHook } from './formatSlug'

export { SlugComponent } from './SlugComponent'

export const slugField = (fieldToUse = 'title', overrides = {}) => {
  const { slugOverrides, checkboxOverrides } = overrides

  const checkBoxField = {
    name: 'slugLock',
    type: 'checkbox',
    defaultValue: true,
    admin: {
      hidden: true,
      position: 'sidebar'
    },
    ...checkboxOverrides
  }

  const slugFieldConfig = {
    name: 'slug',
    type: 'text',
    index: true,
    label: 'Slug',
    ...slugOverrides,
    hooks: {
      beforeValidate: [formatSlugHook(fieldToUse)]
    },
    admin: {
      position: 'sidebar',
      ...slugOverrides?.admin,
      components: {
        Field: {
          path: '@/fields/slug/SlugComponent.jsx#SlugComponent',
          clientProps: {
            fieldToUse,
            checkboxFieldPath: checkBoxField.name
          }
        }
      }
    }
  }

  return [slugFieldConfig, checkBoxField]
}
