import { Chunker } from 'pagedjs'

export class VirtualChunker extends Chunker {
  constructor(pagesArea, pageTemplate, settings) {
    super(pagesArea, pageTemplate, settings)
  }

  setup(renderTo) {
    super.setup(renderTo)

    if (this.pagesArea.parentNode) {
      this.pagesArea.remove()
    }

    this.hiddenContainer = document.createElement('div')
    this.hiddenContainer.style.position = 'absolute'
    this.hiddenContainer.style.left = '-9999px'
    this.hiddenContainer.style.visibility = 'hidden'
    this.hiddenContainer.style.pointerEvents = 'none'

    document.body.append(this.hiddenContainer)
    this.hiddenContainer.append(this.pagesArea)
  }

  destroy() {
    if (this.hiddenContainer && this.hiddenContainer.parentNode) {
      this.hiddenContainer.remove()
    }
    super.destroy()
  }
}
