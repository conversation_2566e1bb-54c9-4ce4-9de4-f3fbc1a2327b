import React from 'react'
import ReactPDF, { Font, pdf } from '@react-pdf/renderer'
import { resumeOptions } from './resumeConstant'

export const generatePdfFromPages = async (parsedPages, font) => {
  // const fonts = resumeOptions.font.options.find((option) => option.value === font).fonts
  const fontOption = resumeOptions.font.options.find((option) => option.value === font)
  const fonts = fontOption.fonts

  // Strip fallbacks like ', sans-serif'
  const cleanFamily = fontOption.fontFamily.split(',')[0].replaceAll(/['"]/g, '').trim()

  try {
    Font.register({ family: cleanFamily, fonts })
  } catch (error) {
    console.error('Error registering font:', error)
  }

  const pdfDocument = (
    <ReactPDF.Document>
      {parsedPages.map((page, index) => {
        const pageElement = page.content[0]
        return convertSingleObject(pageElement, index)
      })}
    </ReactPDF.Document>
  )

  try {
    const pdfBlob = await pdf(pdfDocument).toBlob()
    return pdfBlob
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw error
  }
}

const convertObjectArrayToReactPdf = (objectArray) => {
  if (!Array.isArray(objectArray)) {
    return convertSingleObject(objectArray)
  }

  return objectArray.map((item, index) => convertSingleObject(item, index))
}

const cleanFontFamily = (fontFamily) => {
  if (!fontFamily) return fontFamily
  return fontFamily.split(',')[0].replaceAll(/['"]/g, '').trim()
}

const convertSingleObject = (object, key = 0) => {
  if (!object) return null

  if (object.type === 'text') {
    return object.content
  }

  if (object.type === 'element') {
    let { tagName, styles, children } = object

    if (styles?.fontFamily) {
      styles = {
        ...styles,
        fontFamily: cleanFontFamily(styles.fontFamily)
      }
    }

    switch (tagName) {
      case 'div': {
        const className = object.attributes?.class || ''

        if (className.includes('shadow-resume-page')) {
          return (
            <ReactPDF.Page key={key} size='A4' style={styles}>
              {children && convertObjectArrayToReactPdf(children)}
            </ReactPDF.Page>
          )
        }

        if (className.includes('page-number')) {
          return (
            <ReactPDF.Text key={key} style={styles}>
              {children && convertObjectArrayToReactPdf(children)}
            </ReactPDF.Text>
          )
        }

        return (
          <ReactPDF.View key={key} style={styles}>
            {children && convertObjectArrayToReactPdf(children)}
          </ReactPDF.View>
        )
      }

      case 'p':
      case 'span': {
        return (
          <ReactPDF.Text key={key} style={styles}>
            {/* {JSON.stringify(styles)} */}
            {children && convertObjectArrayToReactPdf(children)}
          </ReactPDF.Text>
        )
      }

      case 'ul':
      case 'ol': {
        return (
          <ReactPDF.View key={key} style={styles}>
            {children && convertObjectArrayToReactPdf(children)}
          </ReactPDF.View>
        )
      }

      case 'li': {
        return (
          <ReactPDF.View
            key={key}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'start',
              gap: '8px',
              marginLeft: '-16px'
            }}>
            <ReactPDF.Text>•</ReactPDF.Text>
            <ReactPDF.Text style={{ flex: 1 }}>
              {children && convertObjectArrayToReactPdf(children)}
            </ReactPDF.Text>
          </ReactPDF.View>
        )
      }
      case 'img': {
        return <ReactPDF.Image key={key} src={object.attributes?.src} style={styles} />
      }

      default: {
        return (
          <ReactPDF.View key={key} style={styles}>
            {children && convertObjectArrayToReactPdf(children)}
          </ReactPDF.View>
        )
      }
    }
  }

  return null
}
