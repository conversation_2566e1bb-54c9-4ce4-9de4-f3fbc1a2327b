import { api } from '@api'
import { resumes } from '@constants'
import { SwiftError } from '@error'

const { errors } = resumes

export const getProducts = async (options = {}) => {
  const { data, error } = await api.getCountryBasedProductsList(options)

  if (error) {
    throw new SwiftError(errors.getError)
  }

  return {
    products: data.products,
    count: data.total,
    userCountry: data?.country,
    userCurrency: data?.currency,
    currencySymbol: data?.symbol
  }
}
