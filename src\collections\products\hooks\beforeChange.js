import Stripe from 'stripe'

const stripeSecretKey = process.env.STRIPE_SECRET_KEY
const stripe = new Stripe(stripeSecretKey || '', { apiVersion: '2022-11-15' })

const logs = false

export const beforeProductChange = async ({ req, data }) => {
  const { payload } = req

  const newDocument = {
    ...data,
    skipSync: false // Force sync to Stripe
  }

  if (data.skipSync) {
    if (logs) payload.logger.info(`Skipping product 'beforeChange' hook`)
    return newDocument
  }

  if (!data.stripeProductID) {
    if (logs) {
      payload.logger.info(`No Stripe product assigned to this document, skipping hook`)
    }
    return newDocument
  }

  if (logs) payload.logger.info(`Looking up product from Stripe...`)

  try {
    const stripeProduct = await stripe.products.retrieve(data.stripeProductID)

    if (logs) payload.logger.info(`Found product from Stripe: ${stripeProduct.name}`)

    // Optionally sync name too
    // newDocument.name = stripeProduct.name
    newDocument.description = stripeProduct.description
  } catch (error) {
    payload.logger.error(`Error fetching product from Stripe: ${error}`)
    return newDocument
  }

  if (logs) payload.logger.info(`Looking up price from Stripe...`)

  try {
    const allPrices = await stripe.prices.list({
      product: data.stripeProductID,
      limit: 100,
      expand: ['data.currency_options']
    })

    if (data.trialStripeProductID) {
      const trialPrices = await stripe.prices.list({
        product: data.trialStripeProductID,
        limit: 100,
        expand: ['data.currency_options']
      })

      newDocument.trialpriceJSON = JSON.stringify(trialPrices)
    }

    newDocument.priceJSON = JSON.stringify(allPrices)
  } catch (error) {
    payload.logger.error(`Error fetching prices from Stripe: ${error}`)
  }

  return newDocument
}
