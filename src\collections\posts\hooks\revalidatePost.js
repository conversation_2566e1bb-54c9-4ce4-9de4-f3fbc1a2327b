export const revalidatePost = async ({ doc, previousDoc, req }) => {
  if (req.payload.config.custom?.revalidate) {
    await req.payload.config.custom.revalidate(`/posts/${doc.slug}`)

    if (previousDoc?.slug && previousDoc.slug !== doc.slug) {
      await req.payload.config.custom.revalidate(`/posts/${previousDoc.slug}`)
    }
  }

  return doc
}

export const revalidateDelete = async ({ req, doc }) => {
  if (req.payload.config.custom?.revalidate) {
    await req.payload.config.custom.revalidate(`/posts/${doc.slug}`)
  }

  return doc
}
