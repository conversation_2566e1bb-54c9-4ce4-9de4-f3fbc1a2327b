import {
  getColorTheme,
  getFontFamily,
  resumeHasSidebar,
  resumeOptionValue,
  resumeOptions,
  resumeStyles
} from '@utils'

export const ResumePage = ({ resumeData, children }) => {
  const { design } = resumeData
  const { layout, palette, accent, backgroundPureWhite, font } = design

  const hasSidebar = resumeHasSidebar(layout)
  const pageStyle = {
    ...resumeStyles.page(palette, backgroundPureWhite),
    fontFamily: getFontFamily(font)
  }
  const theme = getColorTheme(palette, 'sidebar')
  const accentStyles = resumeOptions.accent.styles[accent]

  return (
    <div
      data-layout={layout}
      style={pageStyle}
      className='shadow-resume-page border border-black/5 rounded'>
      <div style={{ display: 'flex', position: 'relative', height: '100%' }}>
        {accent === resumeOptionValue.ribbon && !hasSidebar && (
          <div
            style={{
              backgroundColor: theme.fill,
              width: '100%',
              height: accentStyles.bandHeight,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0
            }}
          />
        )}
        {accent === resumeOptionValue.blur && (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={`${process.env.NEXT_PUBLIC_URL}/images/${accentStyles.backgroundImage}`}
            alt='blur'
            width={595}
            height={842}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              width: '100%',
              height: '100%'
            }}
          />
        )}
        {children}
      </div>
    </div>
  )
}
