/** @type {import('eslint').Linter.Config} */
module.exports = {
  extends: [
    'eslint:recommended',
    'plugin:unicorn/recommended',
    'plugin:@tanstack/query/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:import/recommended',
    'next/core-web-vitals',
    'prettier'
  ],
  plugins: ['jsx-a11y', 'disable', 'unicorn'],
  processor: 'disable/disable',
  parserOptions: {
    project: ['./tsconfig.json'],
    tsconfigRootDir: __dirname
  },
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  rules: {
    'comma-dangle': 'off',
    'import/extensions': 'off',
    'no-duplicate-imports': 'warn',
    'unicorn/filename-case': [
      'error',
      {
        cases: {
          camelCase: true,
          pascalCase: true
        }
      }
    ],
    'unicorn/no-null': 0,
    'unicorn/no-array-reduce': 0,
    'unicorn/prefer-top-level-await': 0,
    'unicorn/prefer-global-this': 0,
    'unicorn/prevent-abbreviations': [
      'error',
      {
        allowList: {
          params: true,
          Params: true,
          env: true,
          Env: true,
          dev: true,
          Dev: true,
          prod: true,
          Prod: true,
          props: true,
          Props: true,
          ref: true,
          Ref: true
        }
      }
    ],
    'unicorn/import-style': [
      'error',
      {
        styles: {
          util: false,
          path: {
            named: true
          }
        }
      }
    ],
    'import/no-default-export': 'error',
    'import/no-empty-named-blocks': 'error',
    'no-else-return': [
      'error',
      {
        allowElseIf: false
      }
    ]
  },
  overrides: [
    {
      files: ['src/app/**/*.{js,jsx,ts,tsx}', '*config*.{js,ts,mjs,cjs}'],
      rules: {
        'import/no-default-export': 0,
        'import/no-anonymous-default-export': 0,
        'unicorn/filename-case': 0,
        'unicorn/prefer-module': 0
      }
    },
    {
      files: ['src/i18n/**/*.{js,ts}'],
      rules: {
        'import/no-default-export': 0
      }
    },
    {
      files: ['src/components/emails/**/*.{js,jsx,ts,tsx}'], // react email has default exports
      rules: {
        'import/named': 0,
        'import/no-unresolved': 0
      }
    }
  ]
}
