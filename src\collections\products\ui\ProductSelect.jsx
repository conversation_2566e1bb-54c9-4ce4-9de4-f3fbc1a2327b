'use client'

import { useEffect, useState } from 'react'
import { CopyToClipboard, Select, useField } from '@payloadcms/ui'

export const ProductSelect = (props) => {
  const { field, path } = props
  const { name, label } = field
  const { value: stripeProductID, setValue } = useField({ path })

  const [options, setOptions] = useState([])

  // const { value: stripeProductID } = useFormFields(([fields]) => fields[name])

  useEffect(() => {
    const getStripeProducts = async () => {
      const productsFetch = await fetch('/api/stripe/rest', {
        method: 'post',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stripeMethod: 'products.list',
          stripeArgs: [{ limit: 100 }]
        })
      })

      const response = await productsFetch.json()

      console.log('response', response)
      const { data } = response

      if (data && 'data' in data) {
        const fetchedProducts = data.data.reduce(
          (accumulator, item) => {
            accumulator.push({
              label: item.name || item.id,
              value: item.id
            })
            return accumulator
          },
          [
            {
              label: 'Select a product',
              value: ''
            }
          ]
        )
        setOptions(fetchedProducts)
      }
    }

    getStripeProducts()
  }, [])

  const stripeBaseURL = `https://dashboard.stripe.com/${
    process.env.PAYLOAD_PUBLIC_STRIPE_IS_TEST_KEY ? 'test/' : ''
  }products`

  const href = `${stripeBaseURL}/${stripeProductID}`

  const productLabel =
    options.find((option) => option.value === stripeProductID)?.label || stripeProductID

  const option = options.find((option) => option.value === stripeProductID)

  return (
    <div>
      <p style={{ marginBottom: '0' }}>{typeof label === 'string' ? label : 'Product'}</p>
      <p style={{ marginBottom: '0.75rem', color: 'var(--theme-elevation-400)' }}>
        {`Select the related Stripe product or `}
        <a
          href={`${stripeBaseURL}/create`}
          target='_blank'
          rel='noopener noreferrer'
          style={{ color: 'var(--theme-text)' }}>
          create a new one
        </a>
        {'.'}
      </p>

      <Select
        name={name}
        label={label}
        options={options}
        value={option}
        onChange={(option) => {
          setValue(option?.value)
        }}
      />

      {stripeProductID && (
        <div style={{ marginTop: '1rem', marginBottom: '1.5rem' }}>
          <div>
            <span className='label' style={{ color: '#9A9A9A' }}>
              {`Manage "${productLabel}" in Stripe`}
            </span>
            <CopyToClipboard value={href} />
          </div>
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              fontWeight: '600'
            }}>
            <a href={href} target='_blank' rel='noreferrer noopener'>
              {href}
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
