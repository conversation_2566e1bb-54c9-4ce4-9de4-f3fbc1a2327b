import Link from 'next/link'

export const CategoryBreadcrumbs = ({ category }) => {
  if (!category?.breadcrumbs || category.breadcrumbs.length === 0) {
    return null
  }

  return (
    <nav className='flex items-center space-x-2 text-sm text-gray-600 mb-4'>
      <Link href='/categories' className='hover:text-gray-900'>
        Categories
      </Link>
      {category.breadcrumbs.map((breadcrumb, index) => (
        <div key={breadcrumb.id || index} className='flex items-center space-x-2'>
          <span>/</span>
          {breadcrumb.url ? (
            <Link href={breadcrumb.url} className='hover:text-gray-900'>
              {breadcrumb.label}
            </Link>
          ) : (
            <span>{breadcrumb.label}</span>
          )}
        </div>
      ))}
    </nav>
  )
}
