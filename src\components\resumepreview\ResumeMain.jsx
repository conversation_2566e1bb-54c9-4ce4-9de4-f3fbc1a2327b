import { resumeHasSidebar } from '@utils'
import { PersonalDetails } from './PersonalDetails'
import { SectionComponent } from './SectionComponent'
import { Summary } from './Summary'
import { MainContainer, SectionsContainer } from './ui'

export const ResumeMain = ({ resumeData, tc = {}, mt = {} }) => {
  const { design } = resumeData
  const { layout, sections, spacing } = design

  const hasSidebar = resumeHasSidebar(layout)

  if (!sections?.length) return null

  const mainSections = hasSidebar
    ? sections.filter((section) => section.isVisible && !section.showInSidebar)
    : sections.filter((section) => section.isVisible)

  return (
    <MainContainer hasSidebar={hasSidebar} layout={layout}>
      <PersonalDetails resumeData={resumeData} tc={tc} location='main' />
      <SectionsContainer spacing={spacing}>
        <Summary resumeData={resumeData} tc={tc} location='main' />
        {mainSections.map((section) => {
          return (
            <SectionComponent
              key={section.id}
              sectionKey={section.sectionKey}
              resumeData={resumeData}
              tc={tc}
              mt={mt}
              location='main'
            />
          )
        })}
      </SectionsContainer>
    </MainContainer>
  )
}
