import React from 'react'

export const convertLexicalToReact = (description) => {
  if (!description) return null

  if (typeof description === 'string') {
    return description
  }

  const editorState = description

  if (!editorState?.root?.children) {
    return null
  }

  const style = { marginBottom: '8px', marginTop: 0 }
  let currentListType = null

  const convertNode = (node, key) => {
    if (!node) return null

    switch (node.type) {
      case 'paragraph': {
        const paragraphContent =
          node.children?.map((child, index) => convertNode(child, index)) || null
        return paragraphContent && paragraphContent.length > 0 ? (
          <p style={style} key={key}>
            {paragraphContent}
          </p>
        ) : null
      }

      case 'text': {
        let text = node.text || ''
        let element = text
        if (node.format) {
          if (node.format & 1)
            element = <strong style={{ fontWeight: 700 }}>{element}</strong>
          if (node.format & 2) element = <em>{element}</em>
          if (node.format & 4) element = <s>{element}</s>
          if (node.format & 8) element = <u>{element}</u>
        }
        return <React.Fragment key={key}>{element}</React.Fragment>
      }

      case 'link': {
        const linkContent =
          node.children?.map((child, index) => convertNode(child, index)) || null
        return (
          <a style={style} href={node.url || '#'} key={key}>
            {linkContent}
          </a>
        )
      }

      case 'list': {
        currentListType = node.listType === 'number' ? 'ol' : 'ul'

        const listItems =
          node.children?.map((child, index) => convertNode(child, index)) || null
        const listTag = currentListType

        const listStyle = {
          display: 'flex',
          flexDirection: 'column',
          listStyle: listTag === 'ol' ? 'decimal' : 'disc',
          paddingLeft: '16px',
          ...style
        }
        const ListTag = listTag
        return (
          <ListTag style={listStyle} data-list={listTag} key={key}>
            {listItems}
          </ListTag>
        )
      }

      case 'listitem': {
        const itemContent =
          node.children?.map((child, index) => convertNode(child, index)) || null

        return (
          <li style={{ marginBottom: '5px' }} key={key}>
            {itemContent}
          </li>
        )
      }

      case 'heading': {
        const headingContent =
          node.children?.map((child, index) => convertNode(child, index)) || null
        const level = Math.min(Math.max(Number(node.tag?.replace('h', '')) || 1, 1), 6)
        const HeadingTag = `h${level}`
        return React.createElement(HeadingTag, { style, key }, headingContent)
      }

      case 'linebreak': {
        return <br key={key} />
      }

      default: {
        if (node.children) {
          return node.children.map((child, index) => convertNode(child, index))
        }
        return null
      }
    }
  }

  return editorState.root.children.map((child, index) => convertNode(child, index))
}
