import { addDataAndFileToRequest } from 'payload'
import Stripe from 'stripe'

const stripeSecretKey = process.env.STRIPE_SECRET_KEY
const stripe = new Stripe(stripeSecretKey || '', { apiVersion: '2022-11-15' })

export const createSubscription = {
  path: '/create-subscription',
  method: 'post',
  handler: async (request) => {
    await addDataAndFileToRequest(request)
    const { data, payload } = request
    console.log('data', request?.data)
    const { email, userId, amount, currency, subscription_data } = data

    let stripeCustomerId = data.stripeCustomerId || null
    const trialPlan = data.trialPlan || null

    if (!email) {
      return Response.json({ error: 'Please enter your email' })
    }

    if (!userId) {
      return Response.json({
        error: 'Your session has expired. Please reload the page and try again.'
      })
    }

    if (!amount && !currency && !subscription_data.items) {
      return Response.json({ error: 'Failed to create subscription. Please try again.' })
    }

    // Ensure we have a Stripe customer ID first
    if (!stripeCustomerId) {
      try {
        const customer = await stripe.customers.create({
          email: email,
          metadata: {
            userId: userId
          }
        })
        stripeCustomerId = customer.id
      } catch {
        console.log('Error creating customer')
        return Response.json({
          error: 'Failed to create subscription. Please try again.'
        })
      }
    }

    // Check for existing active subscription (Database first, then validate with Stripe)
    try {
      const existingSubscriptions = await payload.find({
        collection: 'subscriptions',
        where: {
          user: {
            equals: userId
          },
          status: {
            in: ['active', 'trialing', 'past_due', 'unpaid']
          }
        },
        limit: 1
      })

      if (existingSubscriptions.docs.length > 0) {
        const existingSubscription = existingSubscriptions.docs[0]
        console.log('Found existing subscription:', existingSubscription.subscriptionId)

        try {
          // Validate the subscription still exists in Stripe and get current state
          const currentSubscription = await stripe.subscriptions.retrieve(
            existingSubscription.subscriptionId,
            { expand: ['items'] }
          )

          // Double-check the subscription is still active in Stripe
          const activeStatuses = [
            'active',
            'trialing',
            'past_due',
            'unpaid',
            'incomplete'
          ]
          if (activeStatuses.includes(currentSubscription.status)) {
            // Delete all existing items and add all new items
            const itemsToDelete = currentSubscription.items.data.map((item) => ({
              id: item.id,
              deleted: true
            }))

            // Add all new items
            const itemsToAdd = subscription_data.items.map((item) => ({
              price: item.price,
              quantity: item.quantity || 1
            }))

            // Prepare update options
            const updateOptions = {
              items: [...itemsToDelete, ...itemsToAdd],
              metadata: {
                userId: userId,
                orderType: 'subscription'
              },
              payment_behavior: 'default_incomplete',
              expand: ['latest_invoice.payment_intent', 'pending_setup_intent']
            }

            // Handle trial plan for subscription updates
            if (trialPlan) {
              updateOptions.add_invoice_items = [{ price: trialPlan }]
              // Note: trial_period_days cannot be set on existing subscriptions
              // Trial periods are only for new subscriptions
              console.log(
                'Trial plan specified for existing subscription - adding as invoice item only'
              )
            } else {
              // If no trial plan is specified but the existing subscription has a trial,
              // end the trial immediately to start billing normally
              if (currentSubscription.status === 'trialing') {
                updateOptions.trial_end = 'now'
                console.log(
                  'Ending existing trial immediately as new plan has no trial period'
                )
              }
            }

            // Update the existing Stripe subscription
            const updatedSubscription = await stripe.subscriptions.update(
              existingSubscription.subscriptionId,
              updateOptions
            )

            return Response.json({
              clientSecret:
                updatedSubscription.latest_invoice.payment_intent.client_secret,
              subscriptionId: updatedSubscription.id,
              type: updatedSubscription.pending_setup_intent ? 'setup' : 'payment',
              stripeCustomerId: stripeCustomerId || existingSubscription.stripeCustomerId,
              updated: true
            })
          }
        } catch (error) {
          console.log('Error updating existing subscription', error)
          return Response.json({
            error: 'Failed to update subscription. Please try again.'
          })
        }
      }
    } catch (error) {
      console.log('Error checking for existing subscriptions', error)
      // Continue with creating new subscription if query fails
    }

    let metaData = {
      userId: userId,
      orderType: 'subscription'
    }

    const subscriptionOptions = {
      customer: stripeCustomerId,
      items: subscription_data.items,
      metadata: metaData,
      currency: currency,
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent', 'pending_setup_intent']
    }

    if (trialPlan) {
      subscriptionOptions.add_invoice_items = [{ price: trialPlan }]
      subscriptionOptions.trial_period_days = 7
    }

    try {
      const subscription = await stripe.subscriptions.create(subscriptionOptions)
      return Response.json({
        clientSecret: subscription.latest_invoice.payment_intent.client_secret,
        subscriptionId: subscription.id,
        type: subscription.pending_setup_intent ? 'setup' : 'payment',
        stripeCustomerId: stripeCustomerId,
        updated: false
      })
    } catch (error) {
      console.log('Error creating subscription', error)
      return Response.json({
        error: 'Failed to create subscription. Please try again.'
      })
    }
  }
}
