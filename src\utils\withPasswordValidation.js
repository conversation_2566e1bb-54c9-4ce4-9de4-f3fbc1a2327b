import { z } from 'zod'
import { auth } from '@constants'

const { errors } = auth
// validation middleware to check if password and confirm password are the same
export const withPasswordValidation = (schema) =>
  schema.superRefine((data, context) => {
    if (data.password !== data.confirmPassword) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: errors.missmatchError.digest,
        path: ['confirmPassword']
      })
    }
  })
