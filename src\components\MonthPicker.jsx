'use client'

import { useTranslations } from 'next-intl'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@components'
import { cn, months } from '@utils'

export const MonthPicker = ({
  className,
  triggerClass,
  placeholder,
  label,
  onChange,
  disabled,
  ...props
}) => {
  const t = useTranslations('Months')
  return (
    <Select
      onValueChange={onChange}
      className={cn(className)}
      disabled={disabled}
      {...props}>
      <SelectTrigger className={cn(triggerClass)}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel className='sr-only'>{label}</SelectLabel>
          {months.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {t(option.label)}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}
