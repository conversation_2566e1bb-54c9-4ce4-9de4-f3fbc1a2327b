export const subscriptionDeleted = async (arguments_) => {
  const { event, payload } = arguments_

  const { id: subscriptionId, metadata } = event.data.object
  const userId = metadata?.userId
  const logs = true

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return
  }

  if (logs) {
    payload.logger.info(
      `Syncing Stripe subscription deletion with ID: ${subscriptionId} to Payload...`
    )
  }

  try {
    await payload.delete({
      collection: 'subscriptions',
      where: {
        subscriptionId: {
          equals: subscriptionId
        }
      }
    })

    if (userId) {
      await payload.update({
        collection: 'users',
        id: userId,
        data: {
          subscriptionId: null
        }
      })
      if (logs) {
        payload.logger.info(`- Successfully updated user.`)
      }
    }

    if (logs) {
      payload.logger.info(`✅ Successfully deleted subscription.`)
    }
  } catch (error) {
    payload.logger.error(`- Error deleting subscription: ${error}`)
  }
}
