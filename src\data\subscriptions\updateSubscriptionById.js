import { api } from '@api'
import { subscriptions } from '@constants'
import { SwiftError } from '@error'

export const updateSubscriptionById = async (id, update) => {
  const { data, error } = await api.updateSubscriptionById({
    path: { id },
    body: update
  })

  if (error) {
    console.error('Error updating subscription', error)
    throw new SwiftError(subscriptions.errors.updateError)
  }

  console.log('Updated subscription', data)

  return data
}
