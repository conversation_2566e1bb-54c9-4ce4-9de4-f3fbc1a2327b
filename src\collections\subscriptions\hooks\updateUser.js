export const updateUser = async ({ doc, req }) => {
  const { payload } = req

  try {
    const subscriptionId = doc?.subscriptionId || null
    if (subscriptionId) {
      await payload.update({
        collection: 'users',
        id: doc.user,
        data: {
          subscriptionId: subscriptionId
        }
      })
    }
  } catch (error) {
    payload.logger.error(`Error updating user: ${error}`)
  }

  return doc
}
