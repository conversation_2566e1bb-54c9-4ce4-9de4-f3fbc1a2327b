import {
  ContactContainer,
  ContactItem,
  PersonalInfoDetail,
  PersonalInfoTitle
} from './ui'

export const ContactDetails = ({ resumeData, tc, location }) => {
  const { design, phone, email, links } = resumeData
  const { layout, palette, accent } = design

  return (
    <ContactContainer location={location}>
      {email && (
        <ContactItem layout={layout}>
          <PersonalInfoTitle
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            <span>{tc('email')}:</span>
          </PersonalInfoTitle>
          <PersonalInfoDetail
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            <span>{email}</span>
          </PersonalInfoDetail>
        </ContactItem>
      )}
      {phone && (
        <ContactItem layout={layout}>
          <PersonalInfoTitle
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            <span>{tc('phone')}:</span>
          </PersonalInfoTitle>
          <PersonalInfoDetail
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            <span>{phone}</span>
          </PersonalInfoDetail>
        </ContactItem>
      )}
      {links?.length > 0 &&
        links.map((link, index) => (
          <ContactItem key={`link-${index}`} layout={layout}>
            {link.name && (
              <PersonalInfoTitle
                palette={palette}
                location={location}
                accent={accent}
                layout={layout}>
                <span>{link.name}:</span>
              </PersonalInfoTitle>
            )}
            {link.url && (
              <PersonalInfoDetail
                palette={palette}
                location={location}
                accent={accent}
                layout={layout}>
                <span>{link.url}</span>
              </PersonalInfoDetail>
            )}
          </ContactItem>
        ))}
    </ContactContainer>
  )
}
