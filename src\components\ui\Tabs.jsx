'use client'

import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as TabsPrimitive from '@radix-ui/react-tabs'
import { cn } from '@utils'

const Tabs = TabsPrimitive.Root

const indicatorVariants = {
  rounded: {
    containerClass:
      'absolute bg-white h-7 w-1/2 transition-all duration-300 ease-in-out rounded-md',
    positioning: (style) => ({
      left: style.left,
      top: style.top,
      width: style.width,
      height: style.height
    })
  },
  underline: {
    containerClass:
      'absolute border-b-2 border-primary transition-all duration-300 ease-in-out',
    positioning: (style) => ({
      left: style.left,
      top: '100%',
      width: style.width,
      height: 0
    })
  }
}

const TabsList = React.forwardRef(function TabsList(
  { className, variant = 'rounded', baseClassName, ...props },
  ref
) {
  const [indicatorStyle, setIndicatorStyle] = useState({
    left: 0,
    top: '100%',
    width: 0,
    height: 0
  })
  const tabsListRef = useRef(null)

  const updateIndicator = useCallback(() => {
    if (!tabsListRef.current) return

    const activeTab = tabsListRef.current.querySelector('[data-state="active"]')
    if (!activeTab) return

    const activeRect = activeTab.getBoundingClientRect()
    const tabsRect = tabsListRef.current.getBoundingClientRect()

    requestAnimationFrame(() => {
      const baseStyle = {
        left: activeRect.left - tabsRect.left,
        top: activeRect.top - tabsRect.top,
        width: activeRect.width,
        height: activeRect.height
      }

      const variantConfig = indicatorVariants[variant]
      const finalStyle = variantConfig ? variantConfig.positioning(baseStyle) : baseStyle

      setIndicatorStyle(finalStyle)
    })
  }, [variant])

  useEffect(() => {
    const timeoutId = setTimeout(updateIndicator, 0)

    window.addEventListener('resize', updateIndicator)
    const observer = new MutationObserver(updateIndicator)

    if (tabsListRef.current) {
      observer.observe(tabsListRef.current, {
        attributes: true,
        childList: true,
        subtree: true
      })
    }

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', updateIndicator)
      observer.disconnect()
    }
  }, [updateIndicator])

  const variantConfig = indicatorVariants[variant]

  return (
    <div className={cn('relative', baseClassName)} ref={tabsListRef}>
      <TabsPrimitive.List
        ref={ref}
        className={cn('relative inline-flex items-center', className)}
        {...props}
      />
      {variantConfig && (
        <div className={variantConfig.containerClass} style={indicatorStyle} />
      )}
    </div>
  )
})
TabsList.displayName = 'TabsList'

const TabsTrigger = React.forwardRef(function TabsTrigger({ className, ...props }, ref) {
  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-primary z-10',
        className
      )}
      {...props}
    />
  )
})
TabsTrigger.displayName = 'TabsTrigger'

const TabsContent = React.forwardRef(function TabsContent({ className, ...props }, ref) {
  return (
    <TabsPrimitive.Content
      ref={ref}
      className={cn(
        'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        className
      )}
      {...props}
    />
  )
})
TabsContent.displayName = 'TabsContent'

export { Tabs, TabsContent, TabsList, TabsTrigger }
