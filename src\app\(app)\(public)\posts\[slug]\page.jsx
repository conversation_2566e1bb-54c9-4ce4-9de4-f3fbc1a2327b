import { draftMode } from 'next/headers'
import { notFound } from 'next/navigation'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import { CategoryPage } from '@/components/categories'
import { getPayloadClient } from '@/getPayload'

export default async function PostPage({ params, searchParams }) {
  const { slug } = await params
  const { preview } = await searchParams
  const payload = await getPayloadClient()
  const { isEnabled: draft } = await draftMode()

  try {
    // If preview mode, don't filter by published status
    const whereClause = {
      slug: {
        equals: slug
      }
    }

    // Only filter by published status if not in preview mode and not in draft mode
    if (!preview && !draft) {
      whereClause._status = {
        equals: 'published'
      }
    }

    const { docs: posts } = await payload.find({
      collection: 'posts',
      where: whereClause,
      depth: 2,
      draft: draft || preview
    })

    if (!posts || posts.length === 0) {
      notFound()
    }

    const post = posts[0]

    const { docs: categories } = await payload.find({
      collection: 'categories',
      depth: 1
    })

    return (
      <>
        {draft && <LivePreviewListener />}
        <CategoryPage
          categories={categories}
          currentPost={post}
          isPreview={!!preview}
          isDraft={draft}
        />
      </>
    )
  } catch (error) {
    console.error('Error fetching post:', error)
    notFound()
  }
}

export async function generateStaticParams() {
  const payload = await getPayloadClient()

  try {
    const { docs: posts } = await payload.find({
      collection: 'posts',
      where: {
        _status: {
          equals: 'published'
        }
      },
      depth: 0
    })

    return posts.map((post) => ({
      slug: post.slug
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}
