'use client'

import { useEffect } from 'react'
import { useDragControls } from 'motion/react'
import { useTranslations } from 'next-intl'
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form'
import {
  AccordionWrapper,
  AddSection,
  DeleteSectionItem,
  EditorFieldCheckbox,
  EditorFieldItem,
  EditorFieldLabel,
  EditorFieldRow,
  EditorFormBlock,
  EditorFormFieldGroup,
  EditorFormTitle,
  Form,
  Input,
  MonthPicker,
  ReorderGroup,
  RichTextFieldController,
  SectionContent,
  SectionItem,
  SectionReorder,
  SectionTrigger,
  SectionVisibilityHandler,
  Switch,
  Text,
  YearPicker
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  useCustomUndoRedo,
  useEditor,
  useFieldAdd,
  useFieldRemove,
  useFormWatch,
  useResume
} from '@hooks'
import {
  handleSectionReorder,
  newEducation,
  resumeOptionValue,
  resumeSectionSchemas,
  resumeValues
} from '@utils'
import { EditorPanelHeader } from './EditorPanelHeader'

export const Education = () => {
  const { resume, updateResume } = useResume()
  const sectionKey = resumeOptionValue.education
  const data = resume[sectionKey]

  const form = useForm({
    resolver: zodResolver(resumeSectionSchemas.education),
    defaultValues: resumeValues.education(resume, sectionKey),
    mode: 'onChange'
  })

  const { education, _source } = resume

  const {
    formState: { errors },
    register,
    watch,
    control,
    trigger,
    unregister,
    setValue
  } = form

  const { fields, append, remove, move } = useFieldArray({
    control: control,
    name: sectionKey
  })

  useFormWatch(watch, sectionKey, trigger)

  const handleRemove = useFieldRemove({
    remove,
    sectionKey: sectionKey,
    data
  })

  const handleAdd = useFieldAdd({
    append,
    sectionKey: sectionKey,
    data,
    newItem: newEducation
  })

  const handleReorder = (newOrder) => {
    handleSectionReorder(newOrder, fields, move, updateResume, sectionKey)
  }

  useCustomUndoRedo(_source, education, sectionKey, setValue)

  return (
    <>
      <EditorPanelHeader
        sectionKey={resumeOptionValue.education}
        description='whereYouStudiedAndYourQualifications'
      />
      <Form {...form}>
        <form>
          <AddSection text='addEducation' onClick={handleAdd} />
          <AccordionWrapper fields={fields}>
            <ReorderGroup values={fields} onReorder={handleReorder}>
              {fields.map((field, index) => {
                return (
                  <EducationAccordion
                    key={field.id}
                    form={form}
                    field={field}
                    index={index}
                    remove={handleRemove}
                    errors={errors}
                    register={register}
                    unregister={unregister}
                  />
                )
              })}
            </ReorderGroup>
          </AccordionWrapper>
        </form>
      </Form>
    </>
  )
}

const EducationAccordion = ({
  form,
  field,
  index,
  remove,
  errors,
  register,
  unregister
}) => {
  const tc = useTranslations('Common')
  const t = useTranslations('Errors')
  const controls = useDragControls()

  const isPresent = useWatch({
    control: form.control,
    name: `education.${index}.isPresent`
  })

  useEffect(() => {
    register(`education.${index}.startMonth`, { value: field.startMonth })
    register(`education.${index}.startYear`, { value: field.startYear })
    register(`education.${index}.endMonth`, { value: field.endMonth })
    register(`education.${index}.endYear`, { value: field.endYear })
    register(`education.${index}.isPresent`, { value: field.isPresent })
    register(`education.${index}.description`, { value: field.description })

    return () => {
      unregister([
        `education.${index}.startMonth`,
        `education.${index}.startYear`,
        `education.${index}.endMonth`,
        `education.${index}.endYear`,
        `education.${index}.isPresent`,
        `education.${index}.description`
      ])
    }
  }, [unregister, index, register]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <SectionItem id={field.id} value={field} dragListener={false} dragControls={controls}>
      <SectionPanel
        field={field}
        index={index}
        controls={controls}
        remove={remove}
        form={form}
      />
      <SectionContent>
        <EditorFormBlock>
          <EditorFormTitle title={tc('yourStudy')} />
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`education.${index}.title`}>
                  {tc('fieldOfStudy')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('fieldOfStudy')}
                  {...register(`education.${index}.title`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`education.${index}.subTitle`}>
                  {tc('subTitle')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('subTitle')}
                  {...register(`education.${index}.subTitle`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
        <EditorFormBlock>
          <EditorFormTitle title={tc('dates')} />
          <EditorFormFieldGroup>
            <EditorFieldItem>
              <EditorFieldLabel>{tc('startDate')}</EditorFieldLabel>
              <EditorFieldRow className='flex-row'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`education.${index}.startMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`education.${index}.startMonth`}
                        label={tc('startMonth')}
                        placeholder={tc('month')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`education.${index}.startYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`education.${index}.startYear`}
                        label={tc('startYear')}
                        placeholder={tc('year')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFieldItem>
          </EditorFormFieldGroup>
          <EditorFormFieldGroup>
            <EditorFieldItem>
              <EditorFieldLabel>{tc('endDate')}</EditorFieldLabel>
              <EditorFieldRow className='flex-row items-center flex-wrap'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`education.${index}.endMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`education.${index}.endMonth`}
                        label={tc('endMonth')}
                        placeholder={tc('month')}
                        disabled={isPresent}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`education.${index}.endYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`education.${index}.endYear`}
                        label={tc('endYear')}
                        placeholder={tc('year')}
                        disabled={isPresent}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='w-auto'>
                  <EditorFieldCheckbox>
                    <Controller
                      name={`education.${index}.isPresent`}
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          id={`education.${index}.isPresent`}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          name={field.name}
                          ref={field.ref}
                        />
                      )}
                    />
                    <EditorFieldLabel htmlFor={`education.${index}.isPresent`}>
                      {tc('present')}
                    </EditorFieldLabel>
                  </EditorFieldCheckbox>
                </EditorFieldItem>
              </EditorFieldRow>
              {errors?.education?.[index]?.endYear && (
                <Text variant='xs' weight='medium' className='text-red-500 mt-2'>
                  {t(errors.education[index].endYear.message)}
                </Text>
              )}
            </EditorFieldItem>
          </EditorFormFieldGroup>
        </EditorFormBlock>
        <EditorFormBlock>
          <EditorFormTitle title={tc('description')} />
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel
                  className='sr-only'
                  htmlFor={`education.${index}.description`}>
                  {tc('description')}
                </EditorFieldLabel>
                <RichTextFieldController
                  name={`education.${index}.description`}
                  control={form.control}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
      </SectionContent>
    </SectionItem>
  )
}

const SectionPanel = ({ field, index, controls, remove, form }) => {
  const tc = useTranslations('Common')

  const { visiblityControls, sectionsVisibility, updateSectionsVisibility } = useEditor()

  const eyeVisible = sectionsVisibility[index] || false

  const currentValue = useWatch({
    control: form.control,
    name: `education.${index}`
  })

  const sectionTitle = currentValue?.title || tc('newEducation')

  const isVisible = currentValue?.isVisible || false

  function deleteItemHandler() {
    remove(index)
    updateSectionsVisibility(field, index, 'remove')
  }

  return (
    <div className='px-4 flex items-center gap-1.5'>
      {visiblityControls ? (
        <SectionVisibilityHandler
          isVisible={eyeVisible}
          onClick={() => updateSectionsVisibility(field, index)}
        />
      ) : (
        <SectionReorder dragHandler={(event) => controls.start(event)} />
      )}
      <SectionTrigger label={sectionTitle} isVisible={isVisible} />
      <DeleteSectionItem
        orignalValue={newEducation}
        currentValue={currentValue}
        deleteItemHandler={deleteItemHandler}
      />
    </div>
  )
}
