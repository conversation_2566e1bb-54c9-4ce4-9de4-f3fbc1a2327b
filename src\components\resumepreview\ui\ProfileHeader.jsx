import { resumeOptionValue, resumeOptions, resumeStyles } from '@utils'

export const ProfileHeader = ({ children, accent, hasSidebar }) => {
  const profileHeaderStyle = resumeStyles.profileHeader(accent, hasSidebar)
  const accentStyles = resumeOptions.accent.styles[accent]

  const imgSource =
    accent === resumeOptionValue.header && !hasSidebar
      ? `${process.env.NEXT_PUBLIC_URL}/images/${accentStyles.backgroundImage}`
      : null

  return (
    <div style={profileHeaderStyle}>
      <div
        style={{
          display: 'flex',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0
        }}>
        {imgSource && (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={imgSource}
            width={535}
            height={200}
            alt='header background'
            fetchPriority='low'
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        )}
      </div>
      {children}
    </div>
  )
}
