import React from 'react'

export const ArchiveBlock = ({ collection, limit, disableInnerContainer = false }) => {
  const archiveContent = (
    <div className='my-8'>
      <h3 className='text-xl font-semibold mb-4'>Archive</h3>
      <p className='text-gray-600'>
        Archive content for {collection} (limit: {limit})
      </p>
    </div>
  )

  if (disableInnerContainer) {
    return archiveContent
  }

  return <div className='container my-16'>{archiveContent}</div>
}
