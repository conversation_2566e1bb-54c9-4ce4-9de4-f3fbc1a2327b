import { draftMode } from 'next/headers'
import { notFound } from 'next/navigation'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import { RenderBlocks } from '@/components/RenderBlocks'
import { RichText } from '@/components/RichText'
import { getPayloadClient } from '@/getPayload'

export default async function DynamicPage({ params, searchParams }) {
  const { slug } = await params
  const { preview } = await searchParams
  const payload = await getPayloadClient()
  const { isEnabled: draft } = await draftMode()

  // Handle home page
  const pageSlug = slug?.[0] || 'home'

  try {
    // If preview mode or draft mode, don't filter by published status
    const whereClause = {
      slug: {
        equals: pageSlug
      }
    }

    // Only filter by published status if not in preview mode and not in draft mode
    if (!preview && !draft) {
      whereClause._status = {
        equals: 'published'
      }
    }

    console.log('Looking for page with slug:', pageSlug)
    const { docs: pages } = await payload.find({
      collection: 'pages',
      where: whereClause,
      depth: 2,
      draft: draft || preview
    })

    console.log('Found pages:', pages?.length || 0)

    if (!pages || pages.length === 0) {
      console.log('No pages found for slug:', pageSlug)
      notFound()
    }

    const page = pages[0]

    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='max-w-4xl mx-auto'>
          {draft && <LivePreviewListener />}

          {preview && (
            <div className='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>
              <strong>Preview Mode:</strong> This is a preview of your page. It may not be
              published yet.
            </div>
          )}

          <article className='bg-white border rounded-lg p-8'>
            <h1 className='text-4xl font-bold text-gray-900 mb-4'>{page.title}</h1>

            {page.hero?.media && (
              <div className='mb-6'>
                <img
                  src={page.hero.media.url}
                  alt={page.hero.media.alt || page.title}
                  className='w-full h-64 object-cover rounded-lg'
                />
              </div>
            )}

            {page.hero?.richText && (
              <div className='prose max-w-none mb-6'>
                <RichText data={page.hero.richText} />
              </div>
            )}

            {page.layout && (
              <div className='prose max-w-none'>
                <RenderBlocks blocks={page.layout} />
              </div>
            )}

            <div className='mt-8 pt-6 border-t border-gray-200'>
              <div className='flex items-center text-sm text-gray-500'>
                <span>Last updated: {new Date(page.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </article>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching page:', error)
    notFound()
  }
}

export async function generateStaticParams() {
  const payload = await getPayloadClient()

  try {
    const { docs: pages } = await payload.find({
      collection: 'pages',
      where: {
        _status: {
          equals: 'published'
        }
      },
      depth: 0
    })

    return pages.map((page) => ({
      slug: page.slug === 'home' ? [] : [page.slug]
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}
