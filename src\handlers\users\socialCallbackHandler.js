import { SignJWT } from 'jose'
import crypto from 'node:crypto'
import { generatePayloadCookie, getFieldsToSign } from 'payload'
import { cookies } from 'next/headers'
import { auth, collections, routes } from '@constants'
import { SwiftError } from '@utils'

export const socialCallbackHandler = async (request) => {
  const { provider } = request.routeParams
  const { code, state } = request.query
  let stateData = null

  try {
    // Validate required parameters
    if (!code || typeof code !== 'string') {
      throw new SwiftError(auth.errors.invalidCode)
    }

    // Decode state parameter
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64').toString())
    } catch {
      throw new SwiftError(auth.errors.invalidState)
    }

    // Verify state token from cookie
    const cookieStore = await cookies()
    const storedToken = cookieStore.get('oauth_state')?.value

    if (!storedToken || storedToken !== stateData.token) {
      throw new SwiftError(auth.errors.invalidState)
    }

    // Clear the state cookie
    cookieStore.delete('oauth_state')

    // Extract redirect URLs from state
    const { successUrl } = stateData

    // Validate provider configuration
    const providerUrls = auth.social.urls[provider]
    if (!providerUrls) {
      throw new SwiftError(auth.errors.providerError)
    }

    // Get absolute URLs from payload config
    const serverURL = request.payload.config.serverURL || process.env.NEXT_PUBLIC_URL
    const redirectUri = `${serverURL}${routes.socialCallback(provider)}`

    // Get provider-specific credentials
    const clientId = process.env[`${provider.toUpperCase()}_CLIENT_ID`]
    const clientSecret = process.env[`${provider.toUpperCase()}_CLIENT_SECRET`]

    const collectionConfig = request.payload.collections[collections.users.slug].config
    const payloadConfig = request.payload.config

    if (!clientId || !clientSecret) {
      console.error(`Missing credentials for provider: ${provider}`)
      throw new SwiftError(auth.errors.providerError)
    }

    const tokenData = await getTokenFromCode(
      code,
      providerUrls,
      redirectUri,
      clientId,
      clientSecret
    )

    const profile = await getUserProfile(tokenData, providerUrls)

    if (!profile.email) {
      throw new SwiftError(auth.errors.providerError)
    }

    let existingUser = await request.payload.find({
      collection: collections.users.slug,
      where: { email: { equals: profile.email } },
      limit: 1
    })

    let user = existingUser?.docs[0] || null
    const userData = getUserProfileData(profile, provider)

    if (user) {
      // Only update if the provider isn't already linked
      const existingProviderLogin = (user.socialLogins || []).find(
        (login) => login.provider === provider
      )

      if (!existingProviderLogin) {
        const updatedSocialLogins = [
          ...(user.socialLogins || []),
          {
            provider,
            sub: profile.sub
          }
        ]

        const result = await request.payload.update({
          req: request,
          collection: collections.users.slug,
          id: user.id,
          data: {
            ...userData,
            socialLogins: updatedSocialLogins,
            collection: collections.users.slug
          },
          showHiddenFields: true
        })
        user = result
      }
    } else {
      const avatarUrl = getUserAvatarUrl(profile, provider)
      // Create new user with initial social login
      const result = await request.payload.create({
        req: request,
        collection: collections.users.slug,
        data: {
          ...userData,
          socialLogins: [
            {
              provider,
              sub: profile.sub
            }
          ],
          password: crypto.randomBytes(32).toString('hex'),
          collection: collections.users.slug,
          avatarUrl: avatarUrl,
          role: collections.users.roles.member
        },
        showHiddenFields: true
      })
      user = result
    }

    // login - OAuth2
    const fieldsToSign = getFieldsToSign({
      collectionConfig,
      email: user.email,
      user
    })

    // /////////////////////////////////////
    // beforeLogin - Collection
    // /////////////////////////////////////

    await collectionConfig.hooks.beforeLogin.reduce(async (priorHook, hook) => {
      await priorHook
      user =
        (await hook({
          collection: collectionConfig,
          context: request.context,
          req: request,
          user
        })) || user
    }, Promise.resolve())

    const issuedAt = Math.floor(Date.now() / 1000)
    const exp = issuedAt + collectionConfig.auth.tokenExpiration
    const secretKey = new TextEncoder().encode(request.payload.secret)

    const jwtToken = await new SignJWT(fieldsToSign)
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setIssuedAt(issuedAt)
      .setExpirationTime(exp)
      .sign(secretKey)

    request.user = user

    // /////////////////////////////////////
    // afterLogin - Collection
    // /////////////////////////////////////

    await collectionConfig.hooks.afterLogin.reduce(async (priorHook, hook) => {
      await priorHook

      user =
        (await hook({
          collection: collectionConfig,
          context: request.context,
          req: request,
          token: jwtToken,
          user
        })) || user
    }, Promise.resolve())

    //generate and set payload cookie
    const payloadCookie = generatePayloadCookie({
      cookiePrefix: payloadConfig.cookiePrefix,
      token: jwtToken,
      collectionAuthConfig: collectionConfig.auth
    })

    return new Response(null, {
      status: 302,
      headers: {
        Location: successUrl || routes.dashboard,
        'Set-Cookie': payloadCookie
      }
    })
  } catch (error) {
    console.error('Social callback error:', error)
    let redirectUrl = routes.signIn
    let errorMessage = auth.errors.socialAuthFailed.digest

    // Use error-specific message if available
    if (error instanceof SwiftError) {
      errorMessage = error.digest
    }

    // Try to get errorUrl from stateData if available
    if (stateData?.errorUrl) {
      redirectUrl = stateData.errorUrl
    }

    return new Response(null, {
      status: 302,
      headers: {
        Location: `${redirectUrl}?error=${errorMessage}`
      }
    })
  }
}

async function getTokenFromCode(code, providerUrls, redirectUri, clientId, clientSecret) {
  // Exchange code for access token
  const tokenResponse = await fetch(providerUrls.tokenURL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json'
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      client_id: clientId,
      client_secret: clientSecret
    })
  })

  if (!tokenResponse.ok) {
    const error = await tokenResponse.json().catch(() => ({}))
    console.error('Token exchange error:', error)
    throw new SwiftError(auth.errors.providerError)
  }

  const tokenData = await tokenResponse.json()
  if (!tokenData.access_token) {
    throw new SwiftError(auth.errors.providerError)
  }

  return tokenData
}

async function getUserProfile(tokenData, providerUrls) {
  const profileResponse = await fetch(providerUrls.userInfoURL, {
    headers: {
      Authorization: `Bearer ${tokenData.access_token}`,
      Accept: 'application/json'
    }
  })

  if (!profileResponse.ok) {
    const error = await profileResponse.json().catch(() => ({}))
    console.error('Profile fetch error:', error)
    throw new SwiftError(auth.errors.providerError)
  }

  return profileResponse.json()
}

function getUserProfileData(profile, provider) {
  switch (provider) {
    case auth.social.providers.LINKEDIN: {
      return {
        email: profile.email,
        firstName: profile.given_name,
        lastName: profile.family_name,
        sub: profile.sub
      }
    }
    default: {
      return null
    }
  }
}

function getUserAvatarUrl(profile, provider) {
  switch (provider) {
    case auth.social.providers.LINKEDIN: {
      return profile.picture
    }
    default: {
      return null
    }
  }
}
