import { convertLexicalToReact } from '@utils'
import { RichText, SectionContainer, SectionItem, SectionItems, SectionTitle } from './ui'

export const Summary = ({ resumeData, tc, location }) => {
  const {
    summary,
    design: { layout, palette, headingUnderlineStyle }
  } = resumeData

  const htmlContent = convertLexicalToReact(summary)

  if (!htmlContent) return null

  return (
    <SectionContainer layout={layout}>
      <SectionTitle
        title={tc('summary')}
        layout={layout}
        palette={palette}
        location={location}
        headingUnderlineStyle={headingUnderlineStyle}
      />
      <SectionItems layout={layout}>
        <SectionItem>
          {htmlContent && (
            <RichText palette={palette} location={location}>
              {htmlContent}
            </RichText>
          )}
        </SectionItem>
      </SectionItems>
    </SectionContainer>
  )
}
