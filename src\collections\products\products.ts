import { isAdmin, isAnyone } from '@collections/access'
import { collections } from '@constants'
import { countryBasedProductsList } from './endpoints'
import { beforeProductChange } from './hooks'

// Removed direct import to avoid client/server mixing

export const products = {
  slug: collections.products.slug,
  access: {
    create: isAdmin,
    read: isAnyone,
    update: isAdmin,
    delete: isAdmin
  },
  timestamps: true,
  admin: {
    defaultColumns: ['title'],
    useAsTitle: 'title'
  },
  fields: [
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: 'Description',
      type: 'text',
      required: false
    },
    {
      name: 'priceJSON',
      label: 'price JSON',
      type: 'json',
      required: false
    },
    {
      name: 'stripeProductID',
      label: 'Stripe Product',
      type: 'text',
      admin: {
        components: {
          Field: '@/collections/products/ui/ProductSelect#ProductSelect'
        }
      }
    },
    {
      name: 'skipSync',
      label: 'Skip Sync',
      type: 'checkbox',
      admin: {
        position: 'sidebar',
        readOnly: true,
        hidden: true
      }
    },
    {
      name: 'trialpriceJSON',
      label: 'Trial price JSON',
      type: 'json',
      required: false
    },
    {
      name: 'trialStripeProductID',
      label: 'Trial Stripe Product',
      type: 'text',
      admin: {
        components: {
          Field: '@/collections/products/ui/ProductSelect#ProductSelect'
        }
      }
    },
    {
      name: 'trialPeriod',
      label: 'Trial Period (Days)',
      type: 'number',
      required: false
    },
    {
      name: 'isPopular',
      label: 'Is Popular',
      type: 'checkbox',
      required: false
    }
  ],
  hooks: {
    beforeChange: [beforeProductChange]
  },
  endpoints: [countryBasedProductsList]
}
