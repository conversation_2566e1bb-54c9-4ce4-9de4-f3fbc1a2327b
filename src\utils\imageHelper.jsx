export const shimmer = (width, height) => `
 <svg width="${width}" height="${height}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient id="g">
            <stop stop-color="#f3f4f6" offset="20%" />
            <stop stop-color="#e5e7eb" offset="50%" />
            <stop stop-color="#f3f4f6" offset="70%" />
        </linearGradient>
    </defs>
    <rect width="${width}" height="${height}" fill="#f3f4f6" rx="0" ry="0" />
    <rect id="r" width="${width}" height="${height}" fill="url(#g)" rx="0" ry="0">
        <animate attributeName="x" from="-${width}" to="${width}" dur="1s" repeatCount="indefinite" />
    </rect>
</svg>`

export const toBase64 = (string_) =>
  typeof window === 'undefined'
    ? Buffer.from(string_).toString('base64')
    : window.btoa(string_)

export const imagePlaceholder = (width, height) =>
  `data:image/svg+xml;base64,${toBase64(shimmer(width, height))}`
