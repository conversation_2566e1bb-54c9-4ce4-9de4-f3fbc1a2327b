import { link } from '@/fields/link.js'
import { revalidateFooter } from './hooks/revalidateFooter'

export const Footer = {
  slug: 'footer',
  access: {
    read: () => true
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true
      }
    }
  ],
  hooks: {
    afterChange: [revalidateFooter]
  }
}
