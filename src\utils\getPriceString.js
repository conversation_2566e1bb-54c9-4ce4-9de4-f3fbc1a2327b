// utils/getPriceString.js

export function getPriceString(priceJSON, currency, currencySymbol) {
  if (!priceJSON) {
    console.warn('No priceJSON provided')
    return ''
  }

  try {
    const parsedPrice = priceJSON
    const priceData = parsedPrice.data?.[0]

    if (
      priceData &&
      priceData.currency_options &&
      currency in priceData.currency_options
    ) {
      const price = priceData.currency_options[currency]
      return `${currencySymbol}${price.unit_amount / 100}`
    }
    console.warn('Currency not found in price data')
  } catch (error) {
    console.error('Invalid JSON in priceJSON:', error)
  }

  return ''
}
