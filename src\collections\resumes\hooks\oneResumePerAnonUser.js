import { isAnon } from '@collections/access'
import { resumes } from '@constants'
import { SwiftError } from '@error'

export const oneResumePerAnonUser = async ({ req, data, operation }) => {
  if (operation !== 'create') {
    return
  }

  if (!isAnon({ req })) {
    return
  }

  if (req.user.resumes.docs.length > 0) {
    throw new SwiftError(resumes.errors.oneResumePerAnonUser)
  }

  return data
}
