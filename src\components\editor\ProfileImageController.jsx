'use client'

import { useRef, useState } from 'react'
import { Image as ImageIcon, Loader2, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useController } from 'react-hook-form'
import { toast } from 'sonner'
import { Text } from '@components'
import { allowedImageFileTypes, collections, validationSchemas } from '@constants'
import { createMedia } from '@data'
import { useAuth } from '@hooks'
import { cn } from '@utils'

const ProfileImage = ({ profileImageUrl, onChange }) => {
  const { currentUser } = useAuth()
  const tc = useTranslations('Common')
  const te = useTranslations('Errors')
  const [isLoading, setIsLoading] = useState(false)
  const [profileImage, setProfileImage] = useState(profileImageUrl || null)
  const fileInput = useRef(null)

  const handleFileChange = async (event) => {
    const file = event.target.files?.[0]

    if (file) {
      const validation = validationSchemas.file.safeParse(file)
      if (!validation.success) {
        const errorMessage = validation.error.issues[0].message
        const params =
          errorMessage === 'fileTooLarge'
            ? {
                maxFileSize: collections.media.maxFileSizeMb
              }
            : {
                fileTypes: allowedImageFileTypes.join(', ')
              }
        toast.error(te(errorMessage, params))
        fileInput.current.value = ''
        return
      }

      setIsLoading(true)
      try {
        setProfileImage(URL.createObjectURL(file))
        const media = await createMedia(file, currentUser.id)
        console.log('media', media)
        onChange(media)
      } catch (error) {
        setProfileImage(null)
        console.log(error)
      } finally {
        setIsLoading(false)
        fileInput.current.value = ''
      }
    }
  }

  const removeImage = () => {
    setProfileImage(null)
    onChange(null)
  }

  const hasImage = Boolean(profileImage)

  return (
    <div
      className={cn(
        'relative size-32',
        hasImage
          ? 'overflow-hidden border border-slate-200 rounded-md'
          : 'flex items-center justify-center bg-slate-50 rounded-md transition-colors border border-dashed border-slate-200 hover:bg-slate-100 hover:border-slate-300'
      )}>
      {hasImage && (
        // eslint-disable-next-line @next/next/no-img-element
        <img src={profileImage} alt='profile' className='w-full h-full object-cover' />
      )}

      {hasImage && !isLoading && (
        <div className='absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center'>
          <ImageIcon size={32} className='mx-auto mb-1.5 text-white' />
          <button
            onClick={removeImage}
            className='absolute top-1.5 right-1.5 p-0.5 text-slate-500 bg-slate-50 rounded-full'
            aria-label='Remove image'>
            <X size={12} />
          </button>
        </div>
      )}

      {!hasImage && (
        <div className='flex flex-col items-center justify-center'>
          <ImageIcon size={32} className='mx-auto mb-1.5 text-slate-400' />
          <Text
            as='span'
            variant='xs'
            weight='medium'
            className='block text-center text-slate-700'>
            {tc('addAProfileImage')}
          </Text>
        </div>
      )}

      {isLoading && (
        <div className='absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-50'>
          <Loader2 size={24} className='animate-spin text-white mb-1.5' />
          <Text
            as='span'
            variant='xs'
            weight='medium'
            className='block text-center text-white'>
            {tc('uploading')}
          </Text>
        </div>
      )}

      <input
        type='file'
        className={cn(
          'absolute inset-0',
          hasImage || isLoading ? 'hidden' : 'opacity-0 cursor-pointer'
        )}
        ref={fileInput}
        onChange={handleFileChange}
        accept='image/*'
        disabled={isLoading}
      />
    </div>
  )
}

export const ProfileImageController = ({ name, control, profileImageUrl }) => {
  const { field } = useController({
    name,
    control,
    defaultValue: null
  })

  return <ProfileImage profileImageUrl={profileImageUrl} onChange={field.onChange} />
}
