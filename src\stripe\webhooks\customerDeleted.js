export const customerDeleted = async (arguments_) => {
  const { event, payload } = arguments_

  const { id: stripeCustomerID, metadata } = event.data.object
  const userId = metadata?.userId
  const logs = true

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return
  }

  if (logs) {
    payload.logger.info(
      `Syncing Stripe customer deletion with ID: ${stripeCustomerID} to Payload...`
    )
  }

  try {
    await payload.update({
      collection: 'users',
      id: userId,
      data: {
        stripeCustomerId: null
      }
    })

    if (logs) {
      payload.logger.info(`✅ Successfully synced customer deletion.`)
    }
  } catch (error) {
    payload.logger.error(`- Error deleting user: ${error}`)
  }
}
