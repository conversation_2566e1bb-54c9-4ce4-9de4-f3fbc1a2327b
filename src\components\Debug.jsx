'use client'

/* eslint-plugin-disable jsx-a11y */
import React, { useState } from 'react'
import { isProd } from '@utils'

export const Debug = ({ data, label }) => {
  const [isOpen, setIsOpen] = useState(false)

  // Don't render anything if it's production
  if (isProd) {
    return null
  }

  // Toggle the debug bar visibility
  const toggleDebugBar = () => setIsOpen(!isOpen)

  return (
    <div>
      <div
        style={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: '#888',
          color: '#fff',
          padding: '10px',
          textAlign: 'center',
          zIndex: 9999,
          cursor: 'pointer'
        }}
        onClick={toggleDebugBar}>
        Debug Info {isOpen ? '▼' : '▲'}
      </div>

      {isOpen && (
        <div
          style={{
            position: 'fixed',
            bottom: '40px', // Space between the toggle bar and the content
            left: 0,
            right: 0,
            backgroundColor: '#f5f5f5',
            color: '#333',
            padding: '10px',
            zIndex: 9999,
            maxHeight: '70vh',
            overflowY: 'auto',
            boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)'
          }}>
          <p>
            <strong>{label}</strong>
          </p>
          <pre
            style={{
              backgroundColor: '#fff',
              padding: '10px',
              borderRadius: '5px',
              fontSize: '14px'
            }}>
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
