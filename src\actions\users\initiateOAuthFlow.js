'use server'

import { randomBytes } from 'node:crypto'
import { z } from 'zod'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { auth, routes } from '@constants'
import { SwiftError, isProd } from '@utils'

const initiateOAuthSchema = z.object({
  provider: z.enum(Object.values(auth.social.providers)).describe('OAuth provider'),
  successUrl: z.string().describe('Success URL'),
  errorUrl: z.string().describe('Error URL')
})

export async function initiateOAuthFlow(previousState, formData) {
  let url = null
  const errorUrl = formData.get('errorUrl') || routes.signIn

  try {
    // Validate form data using Zod
    const validatedData = initiateOAuthSchema.parse({
      provider: formData.get('provider'),
      successUrl: formData.get('successUrl') || routes.dashboard,
      errorUrl: errorUrl
    })

    const provider = validatedData.provider
    const providerConfig = auth.social.urls[provider]

    if (!providerConfig) {
      throw new SwiftError(auth.errors.providerError)
    }

    // Generate state object with security token and redirect URLs
    const stateData = {
      token: randomBytes(32).toString('hex'),
      successUrl: validatedData.successUrl,
      errorUrl: validatedData.errorUrl
    }
    const state = Buffer.from(JSON.stringify(stateData)).toString('base64')

    // Store token in an HTTP-only cookie for verification
    const cookieStore = await cookies()
    cookieStore.set('oauth_state', stateData.token, {
      httpOnly: true,
      secure: isProd,
      sameSite: 'lax',
      maxAge: 60 * 5 // 5 minutes
    })

    // Build authorization URL
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env[`${provider.toUpperCase()}_CLIENT_ID`],
      redirect_uri: `${process.env.NEXT_PUBLIC_URL}${routes.socialCallback(provider)}`,
      state,
      scope: providerConfig.scope
    })

    url = `${providerConfig.authorizationURL}?${params.toString()}`
  } catch (error) {
    console.error('Error initiating OAuth flow:', error)

    if (error instanceof SwiftError) {
      url = `${errorUrl}?error=${error.digest}`
    } else if (error instanceof z.ZodError) {
      url = `${errorUrl}?error=${auth.errors.invalidProvider.digest}`
    } else {
      url = `${errorUrl}?error=${auth.errors.authFailed.digest}`
    }
  }

  redirect(url)
}
