'use client'

import { Reorder } from 'motion/react'
import { useTranslations } from 'next-intl'
import {
  EditorContent,
  EditorNavigation,
  EditorPanelContainer,
  EditorPanelItem,
  EditorSheet,
  EditorTopbar,
  PrefillWithLinkedin,
  ResumeTitle
} from '@components'
import { useEditor, useResume } from '@hooks'
import { editorSections, getResumeTitle, resumeOptionValue } from '@utils'

export const EditorPanel = () => {
  const { activePanelId, isSheetActive } = useEditor()
  const tc = useTranslations('Common')
  const { resume, updateResume } = useResume()
  const { name, jobTitle } = resume
  const resumeName = getResumeTitle({ name, jobTitle }) || tc('newResume')

  const sectionOrder = resume.design.sections || []

  const orderedSections = sectionOrder
    .map((sectionItem) => {
      const sectionDetails = editorSections.find(
        (section) => section.id === sectionItem.sectionKey
      )
      return sectionDetails
        ? {
            ...sectionDetails,
            ...sectionItem
          }
        : null
    })
    .filter(Boolean)

  const handleReorder = (newOrder) => {
    if (!resume?.design) return

    const personalDetails = resume.design.sections.find(
      (section) => section.sectionKey === resumeOptionValue.personalDetails
    )

    const updatedSections = [
      ...(personalDetails ? [personalDetails] : []),
      ...newOrder.filter(
        (section) => section.sectionKey !== resumeOptionValue.personalDetails
      )
    ]

    updateResume({
      design: {
        ...resume.design,
        sections: updatedSections
      }
    })
  }

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      <EditorNavigation />
      <div className='relative flex-auto overflow-y-auto overflow-hidden thin-scrollbar'>
        <EditorTopbar>
          {resumeName && <ResumeTitle title={resumeName} />}
          <PrefillWithLinkedin className='ml-auto' />
        </EditorTopbar>
        <EditorPanelContainer className={`${isSheetActive ? 'hidden' : 'flex'}`}>
          <Reorder.Group
            className='relative'
            axis='y'
            values={orderedSections}
            onReorder={handleReorder}>
            {orderedSections.map((section) => {
              return <EditorPanelItem key={section.id} section={section} />
            })}
          </Reorder.Group>
        </EditorPanelContainer>
        <EditorSheet isSheetActive={isSheetActive}>
          <EditorContent activePanelId={activePanelId} />
        </EditorSheet>
      </div>
    </div>
  )
}
