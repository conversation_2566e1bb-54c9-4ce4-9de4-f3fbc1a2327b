'use client'

import { useState } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@components'
import { validationSchemas } from '@constants'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth, useSubscription } from '@hooks'

const getRegisterSchema = () =>
  z.object({
    firstName: validationSchemas.firstName,
    email: validationSchemas.email
  })

export const VerifyInputForm = ({ setCurrentStep }) => {
  const tc = useTranslations('Common')
  const [loading, setLoading] = useState(false)
  const { currentUser, updateUserById, requestOtp } = useAuth()
  const { updateSubscriptionState } = useSubscription()

  const curruntUserEmail = currentUser?.email
  const isNotAnonEmail = curruntUserEmail && !curruntUserEmail.includes('anon')

  const form = useForm({
    resolver: zodResolver(getRegisterSchema()),
    defaultValues: {
      firstName: '',
      email: isNotAnonEmail ? curruntUserEmail : ''
    }
  })

  const onSubmit = async (data) => {
    setLoading(true)
    await updateUserById(
      {
        firstName: data.firstName,
        email: data.email,
        id: currentUser.id
      },
      {
        onSuccess: (data) => {
          requestOtp(
            { email: data.doc.email, firstName: data.doc.firstName },
            {
              onSuccess: () => {
                setCurrentStep('otp')
                updateSubscriptionState({
                  type: 'updateState',
                  payload: {
                    userEmail: data.doc.email
                  }
                })
              }
            }
          )
        },
        onError: (error) => {
          console.error('Error updating user', error)
          toast.error(error.message)
        }
      }
    )
    setLoading(false)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <div className='space-y-4 mt-4'>
          <FormField
            control={form.control}
            name='firstName'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('name')}</FormLabel>
                <FormControl>
                  <Input type='text' placeholder='Bill' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('email')}</FormLabel>
                <FormControl>
                  <Input type='email' placeholder='<EMAIL>' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <Button isLoading={loading} type='submit' className='w-full h-10 rounded-xl'>
          {loading ? <Loader2 className='animate-spin' /> : tc('continue')}
        </Button>
      </form>
    </Form>
  )
}
