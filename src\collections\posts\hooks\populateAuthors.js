export const populateAuthors = async ({ doc, req }) => {
  if (doc?.authors) {
    const populatedAuthors = await Promise.all(
      doc.authors.map(async (author) => {
        try {
          const populatedAuthor = await req.payload.findByID({
            collection: 'users',
            id: typeof author === 'object' ? author.id : author,
            depth: 0
          })

          return {
            id: populatedAuthor.id,
            name:
              `${populatedAuthor.firstName || ''} ${populatedAuthor.lastName || ''}`.trim() ||
              populatedAuthor.email
          }
        } catch {
          // If author not found, return a placeholder or skip
          console.warn(
            `Author not found for ID: ${typeof author === 'object' ? author.id : author}`
          )
          return null
        }
      })
    )

    // Filter out null values (missing authors)
    const validAuthors = populatedAuthors.filter((author) => author !== null)

    return {
      ...doc,
      populatedAuthors: <AUTHORS>
    }
  }

  return doc
}
