'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export const SearchComponent = () => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSearch = async (event) => {
    event.preventDefault()
    if (!query.trim()) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
      const data = await response.json()
      setResults(data.results || [])
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleResultClick = (result) => {
    if (result.collection === 'posts') {
      router.push(`/posts/${result.slug}`)
    } else if (result.collection === 'pages') {
      router.push(`/${result.slug}`)
    }
  }

  return (
    <div className='w-full max-w-md'>
      <form onSubmit={handleSearch} className='flex gap-2'>
        <input
          type='text'
          value={query}
          onChange={(event) => setQuery(event.target.value)}
          placeholder='Search...'
          className='flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        />
        <button
          type='submit'
          disabled={isLoading}
          className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50'>
          {isLoading ? 'Searching...' : 'Search'}
        </button>
      </form>

      {results.length > 0 && (
        <div className='mt-4 bg-white border border-gray-200 rounded-md shadow-lg'>
          {results.map((result, index) => (
            <button
              key={index}
              onClick={() => handleResultClick(result)}
              onKeyDown={(event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                  handleResultClick(result)
                }
              }}
              className='w-full text-left p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 focus:outline-none focus:ring-2 focus:ring-blue-500'>
              <h3 className='font-semibold text-gray-900'>{result.title}</h3>
              <p className='text-sm text-gray-600 mt-1'>{result.description}</p>
              <span className='text-xs text-gray-500 mt-1 capitalize'>
                {result.collection}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
