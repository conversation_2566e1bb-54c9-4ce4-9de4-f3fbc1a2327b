/**
 * Builds a hierarchical tree structure from flat category data
 * @param {Array} categories - Flat array of categories
 * @param {string|null} parentId - ID of parent category (null for root categories)
 * @returns {Array} Hierarchical tree structure
 */
export const buildCategoryTree = (categories, parentId = null) => {
  if (!categories || !Array.isArray(categories)) {
    return []
  }

  return categories
    .filter((category) => {
      // Handle both string IDs and populated parent objects
      const categoryParentId =
        typeof category.parent === 'object' && category.parent
          ? category.parent.id
          : category.parent

      return categoryParentId === parentId
    })
    .map((category) => ({
      ...category,
      children: buildCategoryTree(categories, category.id)
    }))
    .sort((a, b) => a.title.localeCompare(b.title))
}

/**
 * Flattens a hierarchical category tree into a flat array
 * @param {Array} categoryTree - Hierarchical category tree
 * @returns {Array} Flat array of categories
 */
export const flattenCategoryTree = (categoryTree) => {
  if (!categoryTree || !Array.isArray(categoryTree)) {
    return []
  }

  const flattened = []

  const flatten = (categories, level = 0) => {
    for (const category of categories) {
      flattened.push({
        ...category,
        level,
        children: undefined // Remove children from flattened structure
      })

      if (category.children && category.children.length > 0) {
        flatten(category.children, level + 1)
      }
    }
  }

  flatten(categoryTree)
  return flattened
}

/**
 * Finds a category by slug in a hierarchical tree
 * @param {Array} categoryTree - Hierarchical category tree
 * @param {string} slug - Category slug to find
 * @returns {Object|null} Found category or null
 */
export const findCategoryBySlug = (categoryTree, slug) => {
  if (!categoryTree || !Array.isArray(categoryTree) || !slug) {
    return null
  }

  for (const category of categoryTree) {
    if (category.slug === slug) {
      return category
    }

    if (category.children && category.children.length > 0) {
      const found = findCategoryBySlug(category.children, slug)
      if (found) {
        return found
      }
    }
  }

  return null
}

/**
 * Gets all parent categories for a given category
 * @param {Array} categories - Flat array of categories
 * @param {Object} category - Category to get parents for
 * @returns {Array} Array of parent categories (from root to immediate parent)
 */
export const getCategoryParents = (categories, category) => {
  if (!category || !category.parent || !categories) {
    return []
  }

  const parents = []
  let currentParentId =
    typeof category.parent === 'object' ? category.parent.id : category.parent

  while (currentParentId) {
    const parent = categories.find((cat) => cat.id === currentParentId)
    if (parent) {
      parents.unshift(parent) // Add to beginning for correct order
      currentParentId =
        typeof parent.parent === 'object' ? parent.parent.id : parent.parent
    } else {
      break
    }
  }

  return parents
}

/**
 * Gets the full category path as a string
 * @param {Array} categories - Flat array of categories
 * @param {Object} category - Category to get path for
 * @param {string} separator - Path separator (default: ' > ')
 * @returns {string} Full category path
 */
export const getCategoryPath = (categories, category, separator = ' > ') => {
  const parents = getCategoryParents(categories, category)
  const path = [...parents, category].map((cat) => cat.title)
  return path.join(separator)
}
