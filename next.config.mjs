import createNextIntlPlugin from 'next-intl/plugin'
import path from 'node:path'
import { withPayload } from '@payloadcms/next/withPayload'
import { routes } from './src/constants.js'
import { booleanEnvVariable } from './src/utils/booleanEnvVariable.js'

const url = process.env.NEXT_PUBLIC_URL
const doEndpoint = process.env.DO_S3_ENDPOINT
const doCdnEndpoint = process.env.DO_CDN_ENDPOINT

const policies = {
  'default-src': ["'self'", url],
  'script-src': [
    "'self'",
    url,
    "'unsafe-inline'",
    "'unsafe-eval'",
    'https://maps.googleapis.com',
    'https://vercel.live',
    'https://cdn.jsdelivr.net',
    'https://*.stripe.com',
    'https://fonts.googleapis.com'
  ],
  'child-src': ["'self'", url],
  'style-src': [
    "'self'",
    url,
    "'unsafe-inline'",
    'https://fonts.googleapis.com',
    'https://cdn.jsdelivr.net',
    'https://*.stripe.com',
    'https://fonts.googleapis.com'
  ],
  'img-src': [
    "'self'",
    url,
    'https://raw.githubusercontent.com',
    doEndpoint,
    doCdnEndpoint,
    'blob:',
    'data:'
  ],
  'font-src': ["'self'", url, 'https://fonts.gstatic.com'],
  'frame-src': ["'self'", url, 'https://vercel.live', 'https://*.stripe.com'],
  'connect-src': [
    "'self'",
    url,
    'https://maps.googleapis.com',
    doEndpoint,
    doCdnEndpoint,
    'data:',
    'https://api.stripe.com',
    'https://js.stripe.com',
    'https://hooks.stripe.com',
    'https://fonts.googleapis.com',
    'https://*.stripecdn.com'
  ]
}

const csp = Object.entries(policies)
  .map(([key, value]) => {
    if (Array.isArray(value)) {
      return `${key} ${value.join(' ')}`
    }
    return ''
  })
  .join('; ')

const redirects = async () => [
  {
    source: '/',
    destination: routes.dashboard,
    permanent: true
  }
]

const headers = async () => {
  const headers = []

  // Prevent search engines from indexing the site if it is not live
  // To allow robots to crawl the site, use the `NEXT_PUBLIC_IS_LIVE` env variable
  if (!booleanEnvVariable(process.env.NEXT_PUBLIC_IS_LIVE)) {
    headers.push({
      headers: [
        {
          key: 'X-Robots-Tag',
          value: 'noindex'
        }
      ],
      source: '/:path*'
    })
  }

  headers.push({
    source: '/(.*)',
    headers: [
      {
        key: 'Content-Security-Policy',
        value: csp
      }
    ]
  })

  return headers
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  headers,
  redirects,
  logging: {
    fetches: {
      fullUrl: true
    }
  },
  webpack: (config, { isServer, webpack }) => {
    config.plugins.push(
      new webpack.DefinePlugin({
        IN_BROWSER: !isServer
      })
    )

    // Avoid importing raw CSS on the server from node_modules (e.g., react-image-crop)
    // The client will still import the real CSS; the server gets a no-op placeholder
    if (isServer) {
      config.resolve = config.resolve || {}
      config.resolve.alias = config.resolve.alias || {}
      config.resolve.alias['react-image-crop/dist/ReactCrop.css'] = path.resolve(
        process.cwd(),
        'src/styles/react-crop-placeholder.css'
      )
    }
    return config
  },
  images: {
    remotePatterns: [new URL(`${process.env.NEXT_PUBLIC_URL}/**`)]
  }
}

const withNextIntl = createNextIntlPlugin()

export default withNextIntl(withPayload(nextConfig))
