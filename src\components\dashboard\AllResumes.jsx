import { getTranslations } from 'next-intl/server'
import { EmptyResumeCard, ResumeCard, Text } from '@components'
import { getUserResumes } from '@data'

export async function AllResumes() {
  const tc = await getTranslations('Common')
  const { resumes, count } = await getUserResumes()

  return (
    <>
      {resumes?.length > 0 && (
        <section className='pt-6 pb-11'>
          <div className='inner-container'>
            <Text
              as='h2'
              variant='base'
              weight='medium'
              className='flex items-center gap-1 mb-5 text-slate-700'>
              {tc('all')}{' '}
              <Text
                as='span'
                variant='xs'
                className='min-w-5 min-h-5 flex items-center justify-center border rounded-full border-slate-200 leading-none'>
                {count}
              </Text>
            </Text>
            <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10'>
              <EmptyResumeCard imageClass='border-t border-x border-blue-200 rounded-t-xl overflow-hidden' />
              {resumes.map((resume) => (
                <ResumeCard key={resume.id} resume={resume} />
              ))}
            </div>
          </div>
        </section>
      )}
    </>
  )
}
