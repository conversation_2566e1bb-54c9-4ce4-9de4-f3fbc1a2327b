'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>er,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ogo,
  Text
} from '@components'
import { useAuth, useSubscription } from '@hooks'
import { useMediaQuery } from '@raddix/use-media-query'
import { DownloadPdf } from './DownloadPdf'
import { VerifyInput } from './VerifyInput'
import { VerifyOtp } from './VerifyOtp'

export const VerifyModal = ({ open }) => {
  const t = useTranslations('Common')
  const { updateSubscriptionState } = useSubscription()
  const { currentUser } = useAuth()
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  const initialStep = currentUser?.role === 'anon' ? 'need-to-verify' : 'download'
  const [currentStep, setCurrentStep] = useState(initialStep)

  const renderVerificationStep = () => {
    switch (currentStep) {
      case 'need-to-verify': {
        return <VerifyInput setCurrentStep={setCurrentStep} />
      }

      case 'otp': {
        return <VerifyOtp setCurrentStep={setCurrentStep} />
      }
      case 'download': {
        return <DownloadPdf />
      }

      default: {
        return null
      }
    }
  }

  const renderContent = () => {
    return (
      <div className='p-6'>
        <SwiftLogo />
        {renderVerificationStep()}
      </div>
    )
  }

  const handleOpenChange = () => {
    updateSubscriptionState({ type: 'updateState', payload: { onboarding: false } })
  }

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent
          className='max-w-[750px] p-4'
          closeButtonClassName='bg-white rounded-full size-5 flex justify-center items-center top-3 right-3'>
          <DialogHeader className='sr-only'>
            <DialogTitle>Verify Account</DialogTitle>
          </DialogHeader>
          <div className='flex items-center gap-6'>
            <div className='flex-1'>{renderContent()}</div>
            <div className='flex-1 relative h-full flex flex-col items-center justify-center'>
              <div className='p-2'>
                <Image
                  src='/images/bg-onboarding.svg'
                  alt='Verify Account'
                  width={347}
                  height={468}
                  className='absolute inset-0 rounded-xl -z-10 object-cover size-full'
                />
                <Image
                  src='/images/resume-cards.png'
                  alt='Verify Account'
                  width={347}
                  height={468}
                />
                <Text className='text-center' weight='medium'>
                  {t('readyToDownload')}
                </Text>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer open={open} onOpenChange={handleOpenChange}>
      <DrawerContent>
        <DrawerHeader className='sr-only'>
          <DrawerTitle>Verify Account</DrawerTitle>
        </DrawerHeader>
        {renderContent()}
      </DrawerContent>
    </Drawer>
  )
}
