export const formatSlug = (value) => {
  if (typeof value !== 'string') return value

  return value
    .trim()
    .toLowerCase()
    .replaceAll(/\s+/g, '-') // Replace one or more spaces with a single hyphen
    .replaceAll(/[^\w-]+/g, '') // Remove all characters except word characters and hyphens
    .replaceAll(/-+/g, '-') // Replace multiple consecutive hyphens with a single hyphen
    .replaceAll(/^-+|-+$/g, '') // Remove leading and trailing hyphens
}

export const formatSlugHook =
  (fallback) =>
  ({ data, operation, value }) => {
    if (typeof value === 'string') {
      return formatSlug(value)
    }

    if (operation === 'create' || !data?.slug) {
      const fallbackData = data?.[fallback]

      if (fallbackData && typeof fallbackData === 'string') {
        return formatSlug(fallbackData)
      }
    }

    return value
  }
