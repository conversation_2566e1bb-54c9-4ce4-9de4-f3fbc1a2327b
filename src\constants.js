import { z } from 'zod'

export const global = Object.freeze({
  appRoute: 'app',
  apiRoute: 'api'
})

export const allowedImageFileTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/jpg'
]
export const allowedPdfFileTypes = ['application/pdf']

export const collections = Object.freeze({
  admins: {
    slug: 'admins'
  },
  users: {
    slug: 'users',
    roles: {
      member: 'member',
      anon: 'anon',
      editor: 'editor'
    }
  },
  resumes: {
    slug: 'resumes',
    defaultName: 'Untitled resume'
  },
  templates: {
    slug: 'templates'
  },
  resumeExamples: {
    slug: 'resume-examples'
  },
  products: {
    slug: 'products'
  },
  subscriptions: {
    slug: 'subscriptions'
  },
  pricingplans: {
    slug: 'pricingplans'
  },
  otp: {
    slug: 'otp'
  },
  media: {
    slug: 'media',
    fileTypes: [...allowedImageFileTypes, ...allowedPdfFileTypes],
    maxFileSize: 5 * 1024 * 1024, // 5MB,
    maxFileSizeMb: '5MB',
    uploadDir: 'uploads/media'
  },
  shared: {
    minLength: 1,
    maxLength: 60,
    passwordMinLength: 8
  }
})

export const editor = Object.freeze({
  updateDatabaseDelayMs: 3000,
  updateUndoStackDelayMs: 50,
  undoMaxStackSize: 35,
  savingTimerDelay: 750
})

export const routes = Object.freeze({
  editor: (resumeId) => {
    if (resumeId) {
      return `/editor/${resumeId}`
    }
    return `/editor`
  },
  signIn: `/sign-in`,
  signUp: `/sign-up`,
  dashboard: `/dashboard`,
  termsOfService: `/terms-of-service`,
  privacyPolicy: `/privacy-policy`,
  forgotPassword: `/forgot-password`,
  resetPassword: `/reset-password`,
  billing: `/dashboard/billing`,
  account: `/dashboard/account`,
  resumes: `/dashboard/resumes`,
  coverLetters: `/dashboard/cover-letters`,
  socialCallback: (provider) =>
    `/${global.apiRoute}/${collections.users.slug}/oauth/${provider}/callback`,
  resumetoimage: `/to-image`
})

export const payload = Object.freeze({
  authTokenId: 'payload-token',
  emailExistsError: 'A user with the given email is already registered.',
  credentialsError: 'The email or password provided is incorrect.',
  userLocked: 'This user is locked due to having too many failed login attempts.',
  notFound: 'Not Found',
  tokenExpired: 'Token is either invalid or has expired.'
})

export const resumes = Object.freeze({
  errors: {
    createError: {
      digest: 'createError'
    },
    getError: {
      digest: 'getError'
    },
    updateError: {
      digest: 'updateError'
    },
    notFound: {
      digest: 'notFound'
    },
    oneResumePerAnonUser: {
      digest: 'oneResumePerAnonUser'
    },
    deleteError: {
      digest: 'deleteError'
    }
  }
})

export const auth = Object.freeze({
  hasAccountCookie: 'has-account',
  anonUserPassword: 'ooYCp@2n_AZ6antyedg8',
  social: {
    providers: {
      LINKEDIN: 'linkedin',
      GOOGLE: 'google'
    },
    urls: {
      linkedin: {
        authorizationURL: 'https://www.linkedin.com/oauth/v2/authorization',
        tokenURL: 'https://www.linkedin.com/oauth/v2/accessToken',
        userInfoURL: 'https://api.linkedin.com/v2/userinfo',
        scope: 'openid profile email'
      }
    }
  },
  errors: {
    currentUserError: {
      digest: 'currentUserError'
    },
    noEmail: {
      digest: 'noEmail'
    },
    noPassword: {
      digest: 'noPassword'
    },
    invalidEmail: {
      digest: 'invalidEmail'
    },
    invalidPassword: {
      digest: 'invalidPassword'
    },
    createUserError: {
      digest: 'createUserError'
    },
    userAlreadyExists: {
      digest: 'userAlreadyExists'
    },
    authFailed: {
      digest: 'authFailed'
    },
    signOutError: {
      digest: 'signOutError'
    },
    badCredentials: {
      digest: 'badCredentials'
    },
    userLocked: {
      digest: 'userLocked'
    },
    userNotFound: {
      digest: 'userNotFound'
    },
    noToken: {
      digest: 'noToken'
    },
    resetPasswordError: {
      digest: 'resetPasswordError'
    },
    tokenExpired: {
      digest: 'invalidResetToken'
    },
    emailSendError: {
      digest: 'emailSendError'
    },
    invalidState: {
      digest: 'invalidState'
    },
    invalidCode: {
      digest: 'invalidCode'
    },
    providerError: {
      digest: 'providerError'
    },
    socialAuthFailed: {
      digest: 'socialAuthFailed'
    },
    invalidProvider: {
      digest: 'invalidProvider'
    },
    updateUserError: {
      digest: 'updateUserError'
    },
    missmatchError: {
      digest: 'missmatchError'
    }
  },
  types: {
    public: 'public',
    authenticated: 'authenticated',
    anonEnabled: 'anon-enabled'
  }
})

export const media = Object.freeze({
  errors: {
    uploadError: {
      digest: 'uploadError'
    },
    deleteMediaError: {
      digest: 'deleteMediaError'
    },
    invalidFileType: {
      digest: 'invalidFileType'
    },
    fileTooLarge: {
      digest: 'fileTooLarge'
    }
  }
})

export const otp = Object.freeze({
  errors: {
    requestOtpError: {
      digest: 'requestOtpError'
    },
    verifyOtpError: {
      digest: 'verifyOtpError'
    },
    invalidOtp: {
      digest: 'invalidOtp'
    },
    otpExpired: {
      digest: 'otpExpired'
    }
  }
})

export const subscriptions = Object.freeze({
  errors: {
    createError: {
      digest: 'createError'
    },
    getError: {
      digest: 'getError'
    },
    updateError: {
      digest: 'updateError'
    },
    notFound: {
      digest: 'notFound'
    },
    deleteError: {
      digest: 'deleteError'
    }
  }
})

export const validationSchemas = {
  email: z.string().min(1, { message: 'noEmail' }).email({ message: 'invalidEmail' }),
  password: z
    .string()
    .min(1, { message: 'noPassword' })
    .min(collections.shared.passwordMinLength, {
      message: `invalidPassword`
    }),
  firstName: z.string().min(1, { message: 'noFirstName' }),
  lastName: z.string(),
  confirmPassword: z.string().min(1, { message: 'noConfirmPassword' }),
  file: z
    .instanceof(File)
    .refine((file) => file.size <= collections.media.maxFileSize, {
      message: 'fileTooLarge'
    })
    .refine(
      (file) => collections.media.fileTypes.some((type) => type.includes(file.type)),
      {
        message: 'invalidFileType'
      }
    )
}

export const emailTheme = {
  theme: {
    extend: {
      colors: {
        primary: '#2563eb',
        secondary: '#94a3b8',
        slate: '#334155'
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif']
      }
    }
  }
}

export const emailTypes = Object.freeze({
  WELCOME: 'WELCOME',
  OTP: 'OTP'
})

export const pageSizes = Object.freeze({
  A4: {
    width: 595,
    height: 842
  },
  TARGET_RATIO: 0.75,
  MAX_WIDTH: 1000
})

export const countryToLocaleMap = {
  US: 'en',
  GB: 'en-GB',
  CA: 'en',
  AU: 'en',
  DE: 'de',
  FR: 'fr',
  ES: 'es',
  MX: 'es-419',
  BR: 'pt-BR',
  IT: 'it',
  NL: 'nl',
  JP: 'ja',
  CN: 'zh',
  KR: 'ko',
  RU: 'ru',
  IN: 'en'
}
