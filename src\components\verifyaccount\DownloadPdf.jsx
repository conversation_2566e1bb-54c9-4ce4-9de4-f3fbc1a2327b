'use client'

import { useState } from 'react'
import { Download, ExternalLink } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button, Text } from '@components'
import { useResume } from '@hooks'
import { downloadResume } from '@utils'

export const DownloadPdf = () => {
  const t = useTranslations('DownloadPDF')
  const tCheck = useTranslations('CheckoutPage')
  const tc = useTranslations('Common')
  const [isLoading, setIsLoading] = useState(false)
  const { resume } = useResume()
  const { font } = resume.design

  const handleDownload = async (action) => {
    setIsLoading(true)
    try {
      await downloadResume(font, action)
    } catch (error) {
      console.error('Error downloading resume:', error)
    }
    setIsLoading(false)
  }

  return (
    <>
      <Text as='h2' variant='3xl' weight='bold' className='text-neutral-900 mt-12'>
        {t('title')}
      </Text>
      <Text variant='xs' className='text-slate-500 mt-2'>
        {tCheck('description')}
      </Text>
      <div className='space-y-2 mt-12'>
        <Button
          isLoading={isLoading}
          size='lg'
          className='w-full h-10 rounded-xl'
          onClick={handleDownload}>
          <Download size={16} />
          {tc('downloadPdf')}
        </Button>
        <Button
          isLoading={isLoading}
          onClick={() => handleDownload('open')}
          variant='social'
          size='lg'
          className='w-full h-10 rounded-xl'>
          <ExternalLink size={16} />
          {tc('openInNewTab')}
        </Button>
      </div>
    </>
  )
}
