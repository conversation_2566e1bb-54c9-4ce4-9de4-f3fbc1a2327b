import { useEffect, useRef, useState } from 'react'
import { useIsMounted } from './useIsMounted'

const initialSize = {
  width: undefined,
  height: undefined
}

export function useResizeObserver(options) {
  const { ref, box = 'content-box' } = options
  const [{ width, height }, setSize] = useState(initialSize)
  const isMounted = useIsMounted()
  const previousSize = useRef({ ...initialSize })
  const onResize = useRef(options.onResize)

  useEffect(() => {
    if (!ref.current) return
    if (typeof window === 'undefined' || !('ResizeObserver' in window)) return

    const observer = new ResizeObserver(([entry]) => {
      const boxProperty =
        box === 'border-box'
          ? 'borderBoxSize'
          : box === 'device-pixel-content-box'
            ? 'devicePixelContentBoxSize'
            : 'contentBoxSize'

      const newWidth = extractSize(entry, boxProperty, 'inlineSize')
      const newHeight = extractSize(entry, boxProperty, 'blockSize')

      const hasChanged =
        previousSize.current.width !== newWidth ||
        previousSize.current.height !== newHeight

      if (hasChanged) {
        const newSize = { width: newWidth, height: newHeight }
        previousSize.current = newSize

        if (onResize.current) {
          onResize.current(newSize)
        } else {
          if (isMounted()) {
            setSize(newSize)
          }
        }
      }
    })

    observer.observe(ref.current, { box })

    return () => observer.disconnect()
  }, [box, ref, isMounted])

  return { width, height }
}

function extractSize(entry, boxProperty, sizeType) {
  if (!entry[boxProperty]) {
    if (boxProperty === 'contentBoxSize') {
      return entry.contentRect[sizeType === 'inlineSize' ? 'width' : 'height']
    }
    return
  }

  return Array.isArray(entry[boxProperty])
    ? entry[boxProperty][0][sizeType]
    : entry[boxProperty]?.[sizeType]
}
