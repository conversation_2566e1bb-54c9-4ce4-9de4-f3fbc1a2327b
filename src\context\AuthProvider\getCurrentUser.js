import { api } from '@api'
import { auth, collections } from '@constants'
import { SwiftError } from '@error'

export const getCurrentUser = async () => {
  // Get current user directly
  const { data, error } = await api.currentUser({
    query: {
      depth: 1
    }
  })

  if (error) {
    console.error('Error getting current user', error)
    throw new SwiftError(auth.errors.currentUserError)
  }

  // Check if user data is actually valid
  if (!data.user || !data.user.id) {
    throw new SwiftError(auth.errors.currentUserError)
  }

  return { ...data.user, collection: collections.users.slug }
}
