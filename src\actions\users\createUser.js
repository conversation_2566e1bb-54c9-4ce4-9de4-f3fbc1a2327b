'use server'

import { api } from '@api'
import { isValidEmail, isValidPassword } from '@collections/validations'
import { auth, collections, payload } from '@constants'
import { SwiftError } from '@error'
import { matchPayloadErrorMessage } from '@utils'

const {
  noEmail,
  invalidEmail,
  noPassword,
  invalidPassword,
  createUserError,
  userAlreadyExists
} = auth.errors

export const createUser = async (userData, role = collections.users.roles.member) => {
  const { email, password, firstName, lastName } = userData
  if (!email) {
    throw new SwiftError(noEmail)
  }

  if (!isValidEmail(email)) {
    throw new SwiftError(invalidEmail)
  }

  if (!password) {
    throw new SwiftError(noPassword)
  }

  if (!isValidPassword(password)) {
    throw new SwiftError(invalidPassword)
  }

  const body = {
    email,
    password,
    firstName,
    lastName,
    role
  }

  const { error } = await api.createUser({ body })

  if (error) {
    const alreadyExists = matchPayloadErrorMessage(error, payload.emailExistsError)

    if (alreadyExists) {
      throw new SwiftError(userAlreadyExists)
    }

    console.error('Error creating user', error)
    throw new SwiftError(createUserError)
  }

  return email
}
