{"openapi": "3.0.0", "info": {"title": "Swift Resume API", "version": "1.0.0", "description": "Auto-generated OpenAPI document for the Swift Resume API."}, "paths": {"/api/users": {"get": {"summary": "List all users", "operationId": "findUsers", "tags": ["users"], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new user", "operationId": "createUser", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created user", "content": {"application/json": {}}}}}, "patch": {"summary": "Update users", "operationId": "updateUsers", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated users", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete users", "operationId": "deleteUsers", "tags": ["users"], "responses": {"204": {"description": "users deleted successfully"}}}}, "/api/users/{id}": {"get": {"summary": "Retrieve a user by ID", "operationId": "findUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single user", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a user by ID", "operationId": "updateUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated user", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a user by ID", "operationId": "deleteUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "user deleted"}}}}, "/api/users/count": {"get": {"summary": "Count of users", "operationId": "countUsers", "tags": ["users"], "responses": {"200": {"description": "Count of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/users/login": {"post": {"summary": "<PERSON><PERSON>", "operationId": "login", "tags": ["auth"], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/logout": {"post": {"summary": "Logout", "operationId": "logout", "tags": ["auth"], "responses": {"200": {"description": "Logout", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/unlock": {"post": {"summary": "Unlock", "operationId": "unlock", "tags": ["auth"], "responses": {"200": {"description": "Unlock", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": "string"}}}}}}}, "/api/users/refresh-token": {"post": {"summary": "Refresh token", "operationId": "refreshToken", "tags": ["auth"], "responses": {"200": {"description": "Refresh token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/me": {"get": {"summary": "Current user", "operationId": "currentUser", "tags": ["auth"], "responses": {"200": {"description": "Current user", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/forgot-password": {"post": {"summary": "Forgot password", "operationId": "forgotPassword", "tags": ["auth"], "responses": {"200": {"description": "Forgot password", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": "string"}}}}}}}, "/api/users/reset-password": {"post": {"summary": "Reset password", "operationId": "resetPassword", "tags": ["auth"], "responses": {"200": {"description": "Reset password", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"token": "string", "password": "string"}}}}}}}, "/api/users/verify/{token}": {"post": {"summary": "Verify token", "operationId": "verifyToken", "tags": ["auth"], "responses": {"200": {"description": "Verify token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/categories": {"get": {"summary": "List all categories", "operationId": "findCategories", "tags": ["categories"], "responses": {"200": {"description": "A list of categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new category", "operationId": "createCategory", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created category", "content": {"application/json": {}}}}}, "patch": {"summary": "Update categories", "operationId": "updateCategories", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated categories", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete categories", "operationId": "deleteCategories", "tags": ["categories"], "responses": {"204": {"description": "categories deleted successfully"}}}}, "/api/categories/{id}": {"get": {"summary": "Retrieve a category by ID", "operationId": "findCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single category", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a category by ID", "operationId": "updateCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated category", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a category by ID", "operationId": "deleteCategoryById", "tags": ["categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "category deleted"}}}}, "/api/categories/count": {"get": {"summary": "Count of categories", "operationId": "countCategories", "tags": ["categories"], "responses": {"200": {"description": "Count of categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/pages": {"get": {"summary": "List all pages", "operationId": "findPages", "tags": ["pages"], "responses": {"200": {"description": "A list of pages", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new page", "operationId": "createPage", "tags": ["pages"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created page", "content": {"application/json": {}}}}}, "patch": {"summary": "Update pages", "operationId": "updatePages", "tags": ["pages"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated pages", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete pages", "operationId": "deletePages", "tags": ["pages"], "responses": {"204": {"description": "pages deleted successfully"}}}}, "/api/pages/{id}": {"get": {"summary": "Retrieve a page by ID", "operationId": "findPageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single page", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a page by ID", "operationId": "updatePageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated page", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a page by ID", "operationId": "deletePageById", "tags": ["pages"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "page deleted"}}}}, "/api/pages/count": {"get": {"summary": "Count of pages", "operationId": "countPages", "tags": ["pages"], "responses": {"200": {"description": "Count of pages", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/posts": {"get": {"summary": "List all posts", "operationId": "findPosts", "tags": ["posts"], "responses": {"200": {"description": "A list of posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new post", "operationId": "createPost", "tags": ["posts"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created post", "content": {"application/json": {}}}}}, "patch": {"summary": "Update posts", "operationId": "updatePosts", "tags": ["posts"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated posts", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete posts", "operationId": "deletePosts", "tags": ["posts"], "responses": {"204": {"description": "posts deleted successfully"}}}}, "/api/posts/{id}": {"get": {"summary": "Retrieve a post by ID", "operationId": "findPostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single post", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a post by ID", "operationId": "updatePostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated post", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a post by ID", "operationId": "deletePostById", "tags": ["posts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "post deleted"}}}}, "/api/posts/count": {"get": {"summary": "Count of posts", "operationId": "countPosts", "tags": ["posts"], "responses": {"200": {"description": "Count of posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/media": {"get": {"summary": "List all media", "operationId": "findMedia", "tags": ["media"], "responses": {"200": {"description": "A list of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new media", "operationId": "createMedia", "tags": ["media"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created media", "content": {"application/json": {}}}}}, "patch": {"summary": "Update media", "operationId": "updateMedia", "tags": ["media"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete media", "operationId": "deleteMedia", "tags": ["media"], "responses": {"204": {"description": "media deleted successfully"}}}}, "/api/media/{id}": {"get": {"summary": "Retrieve a media by ID", "operationId": "findMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single media", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a media by ID", "operationId": "updateMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a media by ID", "operationId": "deleteMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "media deleted"}}}}, "/api/media/count": {"get": {"summary": "Count of media", "operationId": "countMedia", "tags": ["media"], "responses": {"200": {"description": "Count of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/templates": {"get": {"summary": "List all templates", "operationId": "findTemplates", "tags": ["templates"], "responses": {"200": {"description": "A list of templates", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new template", "operationId": "createTemplate", "tags": ["templates"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created template", "content": {"application/json": {}}}}}, "patch": {"summary": "Update templates", "operationId": "updateTemplates", "tags": ["templates"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated templates", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete templates", "operationId": "deleteTemplates", "tags": ["templates"], "responses": {"204": {"description": "templates deleted successfully"}}}}, "/api/templates/{id}": {"get": {"summary": "Retrieve a template by ID", "operationId": "findTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single template", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a template by ID", "operationId": "updateTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated template", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a template by ID", "operationId": "deleteTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "template deleted"}}}}, "/api/templates/count": {"get": {"summary": "Count of templates", "operationId": "countTemplates", "tags": ["templates"], "responses": {"200": {"description": "Count of templates", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/resumes": {"get": {"summary": "List all resumes", "operationId": "findResumes", "tags": ["resumes"], "responses": {"200": {"description": "A list of resumes", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new resume", "operationId": "createResume", "tags": ["resumes"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created resume", "content": {"application/json": {}}}}}, "patch": {"summary": "Update resumes", "operationId": "updateResumes", "tags": ["resumes"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resumes", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete resumes", "operationId": "deleteResumes", "tags": ["resumes"], "responses": {"204": {"description": "resumes deleted successfully"}}}}, "/api/resumes/{id}": {"get": {"summary": "Retrieve a resume by ID", "operationId": "findResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single resume", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a resume by ID", "operationId": "updateResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a resume by ID", "operationId": "deleteResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "resume deleted"}}}}, "/api/resumes/count": {"get": {"summary": "Count of resumes", "operationId": "countResumes", "tags": ["resumes"], "responses": {"200": {"description": "Count of resumes", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/resume-examples": {"get": {"summary": "List all resume examples", "operationId": "findResumeExamples", "tags": ["resume-examples"], "responses": {"200": {"description": "A list of resume examples", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new resume example", "operationId": "createResumeExample", "tags": ["resume-examples"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created resume example", "content": {"application/json": {}}}}}, "patch": {"summary": "Update resume examples", "operationId": "updateResumeExamples", "tags": ["resume-examples"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume examples", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete resume examples", "operationId": "deleteResumeExamples", "tags": ["resume-examples"], "responses": {"204": {"description": "resume examples deleted successfully"}}}}, "/api/resume-examples/{id}": {"get": {"summary": "Retrieve a resume example by ID", "operationId": "findResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single resume example", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a resume example by ID", "operationId": "updateResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume example", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a resume example by ID", "operationId": "deleteResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "resume example deleted"}}}}, "/api/resume-examples/count": {"get": {"summary": "Count of resume examples", "operationId": "countResumeExamples", "tags": ["resume-examples"], "responses": {"200": {"description": "Count of resume examples", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/products": {"get": {"summary": "List all products", "operationId": "findProducts", "tags": ["products"], "responses": {"200": {"description": "A list of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new product", "operationId": "createProduct", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created product", "content": {"application/json": {}}}}}, "patch": {"summary": "Update products", "operationId": "updateProducts", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated products", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete products", "operationId": "deleteProducts", "tags": ["products"], "responses": {"204": {"description": "products deleted successfully"}}}}, "/api/products/{id}": {"get": {"summary": "Retrieve a product by ID", "operationId": "findProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single product", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a product by ID", "operationId": "updateProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated product", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a product by ID", "operationId": "deleteProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "product deleted"}}}}, "/api/products/count": {"get": {"summary": "Count of products", "operationId": "countProducts", "tags": ["products"], "responses": {"200": {"description": "Count of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/subscriptions": {"get": {"summary": "List all subscriptions", "operationId": "findSubscriptions", "tags": ["subscriptions"], "responses": {"200": {"description": "A list of subscriptions", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new subscription", "operationId": "createSubscription", "tags": ["subscriptions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created subscription", "content": {"application/json": {}}}}}, "patch": {"summary": "Update subscriptions", "operationId": "updateSubscriptions", "tags": ["subscriptions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated subscriptions", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete subscriptions", "operationId": "deleteSubscriptions", "tags": ["subscriptions"], "responses": {"204": {"description": "subscriptions deleted successfully"}}}}, "/api/subscriptions/{id}": {"get": {"summary": "Retrieve a subscription by ID", "operationId": "findSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single subscription", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a subscription by ID", "operationId": "updateSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated subscription", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a subscription by ID", "operationId": "deleteSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "subscription deleted"}}}}, "/api/subscriptions/count": {"get": {"summary": "Count of subscriptions", "operationId": "countSubscriptions", "tags": ["subscriptions"], "responses": {"200": {"description": "Count of subscriptions", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/redirects": {"get": {"summary": "List all redirects", "operationId": "findRedirects", "tags": ["redirects"], "responses": {"200": {"description": "A list of redirects", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new redirect", "operationId": "createRedirect", "tags": ["redirects"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created redirect", "content": {"application/json": {}}}}}, "patch": {"summary": "Update redirects", "operationId": "updateRedirects", "tags": ["redirects"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated redirects", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete redirects", "operationId": "deleteRedirects", "tags": ["redirects"], "responses": {"204": {"description": "redirects deleted successfully"}}}}, "/api/redirects/{id}": {"get": {"summary": "Retrieve a redirect by ID", "operationId": "findRedirectById", "tags": ["redirects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single redirect", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a redirect by ID", "operationId": "updateRedirectById", "tags": ["redirects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated redirect", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a redirect by ID", "operationId": "deleteRedirectById", "tags": ["redirects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "redirect deleted"}}}}, "/api/redirects/count": {"get": {"summary": "Count of redirects", "operationId": "countRedirects", "tags": ["redirects"], "responses": {"200": {"description": "Count of redirects", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/forms": {"get": {"summary": "List all forms", "operationId": "findForms", "tags": ["forms"], "responses": {"200": {"description": "A list of forms", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new form", "operationId": "createForm", "tags": ["forms"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created form", "content": {"application/json": {}}}}}, "patch": {"summary": "Update forms", "operationId": "updateForms", "tags": ["forms"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated forms", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete forms", "operationId": "deleteForms", "tags": ["forms"], "responses": {"204": {"description": "forms deleted successfully"}}}}, "/api/forms/{id}": {"get": {"summary": "Retrieve a form by ID", "operationId": "findFormById", "tags": ["forms"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single form", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a form by ID", "operationId": "updateFormById", "tags": ["forms"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated form", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a form by ID", "operationId": "deleteFormById", "tags": ["forms"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "form deleted"}}}}, "/api/forms/count": {"get": {"summary": "Count of forms", "operationId": "countForms", "tags": ["forms"], "responses": {"200": {"description": "Count of forms", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/form-submissions": {"get": {"summary": "List all form submissions", "operationId": "findFormSubmissions", "tags": ["form-submissions"], "responses": {"200": {"description": "A list of form submissions", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new form submission", "operationId": "createFormSubmission", "tags": ["form-submissions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created form submission", "content": {"application/json": {}}}}}, "patch": {"summary": "Update form submissions", "operationId": "updateFormSubmissions", "tags": ["form-submissions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated form submissions", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete form submissions", "operationId": "deleteFormSubmissions", "tags": ["form-submissions"], "responses": {"204": {"description": "form submissions deleted successfully"}}}}, "/api/form-submissions/{id}": {"get": {"summary": "Retrieve a form submission by ID", "operationId": "findFormSubmissionById", "tags": ["form-submissions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single form submission", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a form submission by ID", "operationId": "updateFormSubmissionById", "tags": ["form-submissions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated form submission", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a form submission by ID", "operationId": "deleteFormSubmissionById", "tags": ["form-submissions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "form submission deleted"}}}}, "/api/form-submissions/count": {"get": {"summary": "Count of form submissions", "operationId": "countFormSubmissions", "tags": ["form-submissions"], "responses": {"200": {"description": "Count of form submissions", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/search": {"get": {"summary": "List all search results", "operationId": "findSearchResults", "tags": ["search"], "responses": {"200": {"description": "A list of search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new search result", "operationId": "createSearchResult", "tags": ["search"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created search result", "content": {"application/json": {}}}}}, "patch": {"summary": "Update search results", "operationId": "updateSearchResults", "tags": ["search"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated search results", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete search results", "operationId": "deleteSearchResults", "tags": ["search"], "responses": {"204": {"description": "search results deleted successfully"}}}}, "/api/search/{id}": {"get": {"summary": "Retrieve a search result by ID", "operationId": "findSearchResultById", "tags": ["search"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single search result", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a search result by ID", "operationId": "updateSearchResultById", "tags": ["search"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated search result", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a search result by ID", "operationId": "deleteSearchResultById", "tags": ["search"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "search result deleted"}}}}, "/api/search/count": {"get": {"summary": "Count of search results", "operationId": "countSearchResults", "tags": ["search"], "responses": {"200": {"description": "Count of search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/payload-jobs": {"get": {"summary": "List all payload jobs", "operationId": "findPayloadJobs", "tags": ["payload-jobs"], "responses": {"200": {"description": "A list of payload jobs", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new payload job", "operationId": "createPayloadJob", "tags": ["payload-jobs"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created payload job", "content": {"application/json": {}}}}}, "patch": {"summary": "Update payload jobs", "operationId": "updatePayloadJobs", "tags": ["payload-jobs"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated payload jobs", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete payload jobs", "operationId": "deletePayloadJobs", "tags": ["payload-jobs"], "responses": {"204": {"description": "payload jobs deleted successfully"}}}}, "/api/payload-jobs/{id}": {"get": {"summary": "Retrieve a payload job by ID", "operationId": "findPayloadJobById", "tags": ["payload-jobs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single payload job", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a payload job by ID", "operationId": "updatePayloadJobById", "tags": ["payload-jobs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated payload job", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a payload job by ID", "operationId": "deletePayloadJobById", "tags": ["payload-jobs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "payload job deleted"}}}}, "/api/payload-jobs/count": {"get": {"summary": "Count of payload jobs", "operationId": "countPayloadJobs", "tags": ["payload-jobs"], "responses": {"200": {"description": "Count of payload jobs", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/otp/request-otp": {"post": {"summary": "Request OTP", "operationId": "requestOtp", "tags": ["otp"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "description": "Email address to send OTP to"}}}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP sent"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "object", "description": "Error details"}}}}}}}}}, "/api/otp/verify-otp": {"post": {"summary": "Verify OTP", "operationId": "verifyOtp", "tags": ["otp"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "otp"], "properties": {"email": {"type": "string", "format": "email", "description": "Email address associated with the OTP"}, "otp": {"type": "string", "pattern": "^[0-9]{6}$", "description": "6-digit OTP code"}}}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP verified"}}}}}}, "400": {"description": "Invalid OTP or user not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "enum": ["Invalid OTP", "User not found"]}}}}}}}}}, "/api/products/country-based-products-list": {"get": {"summary": "Get country-based products list", "operationId": "getCountryBasedProductsList", "tags": ["products"], "parameters": [{"name": "country", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Country code (e.g., US, GB, DE)"}, {"name": "currency", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Currency code (e.g., usd, eur, gbp)"}], "responses": {"200": {"description": "Country-based products list", "content": {"application/json": {"schema": {"type": "object", "properties": {"country": {"type": "string"}, "currency": {"type": "string"}, "symbol": {"type": "string"}, "products": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "priceJSON": {"type": "object"}, "trialpriceJSON": {"type": "object"}, "stripeProductID": {"type": "string"}, "trialStripeProductID": {"type": "string"}, "isPopular": {"type": "boolean"}, "trialPeriod": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "total": {"type": "number"}}}}}}, "400": {"description": "Invalid country or currency", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}}}