'use client'

import { createContext, useEffect, useMemo, useReducer } from 'react'
import { useSearchParams } from 'next/navigation'
import { CheckoutModal, VerifyModal } from '@components'
import { getProducts } from '@data'
import { useQuery } from '@tanstack/react-query'

export const SubscriptionContext = createContext()

const countryCurrencyMap = {
  US: 'usd',
  NL: 'eur', // Netherlands
  SE: 'sek', // Sweden
  DK: 'dkk', // Denmark
  CZ: 'czk', // Czechia
  DE: 'eur', // Germany
  FI: 'eur', // Finland
  ES: 'eur', // Spain
  GB: 'gbp' // United Kingdom
}

const currencySymbolMap = {
  usd: '$',
  eur: '€',
  sek: 'kr',
  dkk: 'kr',
  czk: 'Kč',
  gbp: '£'
}

const initialState = {
  isSubscribed: false,
  isProcessing: false,
  isCancelled: false,
  pricingModalOpen: false,
  selectedPlan: null,
  country: 'US',
  countryCurrencyMap: countryCurrencyMap,
  currencySymbolMap: currencySymbolMap,
  currency: 'usd',
  paymentMessage: '',
  onboarding: false,
  userEmail: '',
  currencySymbol: '$'
}

function reducer(state, action) {
  const { type, payload } = action

  switch (type) {
    case 'updateState': {
      return { ...state, ...payload }
    }
    default: {
      return state
    }
  }
}

export const SubscriptionProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState)
  const searchParams = useSearchParams()
  const payment_intent = searchParams.get('payment_intent')

  const getProductsQuery = useQuery({
    queryKey: ['getPricingPlans'],
    queryFn: getProducts,
    staleTime: 0
  })

  // const getCountryQuery = useQuery({
  //   queryKey: ['getCountry'],
  //   queryFn: async () => {
  //     const response = await fetch('/api/geoLocation')
  //     const data = await response.json()
  //     return data?.country || null
  //   },
  //   staleTime: 0
  // })

  useEffect(() => {
    if (getProductsQuery.data) {
      const products = getProductsQuery.data.products.sort((a, b) =>
        a.title.localeCompare(b.title)
      )
      dispatch({
        type: 'updateState',
        payload: {
          selectedPlan: products[0],
          products: products,
          country: getProductsQuery.data?.userCountry,
          currency: getProductsQuery.data?.userCurrency || 'usd',
          currencySymbol: getProductsQuery.data?.currencySymbol || '$'
        }
      })
    }
  }, [getProductsQuery.data])

  // useEffect(() => {
  //   if (getCountryQuery.data) {
  //     console.log('test', getCountryQuery.data)
  //     dispatch({
  //       type: 'updateState',
  //       payload: {
  //         country: getCountryQuery.data,
  //         currency: countryCurrencyMap[getCountryQuery.data] || 'usd'
  //       }
  //     })
  //   }
  // }, [getCountryQuery.data])

  const products = getProductsQuery.data

  const value = useMemo(
    () => ({ subscriptionState: state, updateSubscriptionState: dispatch, products }),
    [state, dispatch, products]
  )

  useEffect(() => {
    if (payment_intent && products) {
      dispatch({
        type: 'updateState',
        payload: {
          pricingModalOpen: true
        }
      })
    }
  }, [payment_intent, products])

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
      <CheckoutModal open={state.pricingModalOpen} />
      <VerifyModal open={state.onboarding} />
    </SubscriptionContext.Provider>
  )
}
