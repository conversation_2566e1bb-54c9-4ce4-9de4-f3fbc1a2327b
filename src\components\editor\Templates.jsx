'use client'

import { useState } from 'react'
import { Check, SwatchBook } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  <PERSON>er<PERSON><PERSON>le,
  DrawerTrigger,
  Text
} from '@components'

const mockTemplates = [
  {
    id: 'callidus',
    name: 'Callid<PERSON>',
    tags: ['Two column', 'Professional', 'Skills', 'Tech'],
    preview: '/images/resume.svg'
  },
  {
    id: 'recentior',
    name: 'Recentior',
    tags: ['One column', 'Creative', 'Skills', 'Design'],
    preview: '/images/resume.svg'
  }
]

const handleTemplateSelect = (template) => {
  console.log('Selected template:', template)
}

export const Templates = () => {
  const tc = useTranslations('Common')
  const [selectedTemplate, setSelectedTemplate] = useState('callidus')

  const handleTemplateClick = (template) => {
    setSelectedTemplate(template.id)
    handleTemplateSelect(template)
  }

  return (
    <Drawer>
      <DrawerTrigger asChild>
        <Button variant='subtle' size='link' className='gap-1 text-slate-700 p-0'>
          <SwatchBook size={16} className='text-slate-500' />
          <span className='hidden lg:block'>{tc('templates')}</span>
        </Button>
      </DrawerTrigger>

      <DrawerContent className='max-h-[90vh]'>
        <DrawerHeader className='sr-only'>
          <DrawerTitle className='text-xl font-semibold'>Choose a Template</DrawerTitle>
        </DrawerHeader>

        <div className='px-6 pb-6 overflow-y-auto max-w-[960px] w-full mx-auto'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-12 my-6'>
            {mockTemplates.map((template) => (
              <div
                key={template.id}
                className='group relative cursor-pointer'
                onClick={() => handleTemplateClick(template)}
                onKeyDown={(event) => {
                  if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault()
                    handleTemplateClick(template)
                  }
                }}
                role='button'
                tabIndex={0}
                aria-label={`Select ${template.name} template`}>
                <div className='relative bg-white border border-gray-200 rounded-lg shadow-sm'>
                  <Image
                    src={template.preview}
                    width={465}
                    height={645}
                    alt={`${template.name} template preview`}
                    className='w-full h-full object-cover'
                  />

                  {selectedTemplate === template.id && (
                    <div className='absolute -top-3 -right-3 size-9 bg-green-500 rounded-full flex items-center justify-center z-10'>
                      <Check size={24} className='text-white' />
                    </div>
                  )}

                  <div className='absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/5 bg-opacity-20'>
                    <Button
                      onClick={(event) => {
                        event.stopPropagation()
                        handleTemplateClick(template)
                      }}
                      className='rounded-xl'
                      size='lg'>
                      Switch to this template
                    </Button>
                  </div>
                </div>

                <div className='mt-4'>
                  <Text variant='base' weight='semibold' className='text-slate-700 mb-2'>
                    {template.name}
                  </Text>
                  <div className='flex flex-wrap gap-2 text-xs'>
                    {template.tags.map((tag, index) => (
                      <Badge key={index} variant='outline' className='text-xs px-2 py-1'>
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
