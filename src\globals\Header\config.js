import { link } from '@/fields/link.js'
import { revalidateHeader } from './hooks/revalidateHeader'

export const Header = {
  slug: 'header',
  access: {
    read: () => true
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true
      }
    }
  ],
  hooks: {
    afterChange: [revalidateHeader]
  }
}
