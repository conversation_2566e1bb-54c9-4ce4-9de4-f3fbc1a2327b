import { revalidateRedirects } from '@/hooks/revalidateRedirects'
import { beforeSyncWithSearch } from '@/search/beforeSync'
import { searchFields } from '@/search/fieldOverrides'
import { collections } from '@constants'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import { nestedDocsPlugin } from '@payloadcms/plugin-nested-docs'
import { redirectsPlugin } from '@payloadcms/plugin-redirects'
import { searchPlugin } from '@payloadcms/plugin-search'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { stripePlugin } from '@payloadcms/plugin-stripe'
import {
  FixedToolbarFeature,
  HeadingFeature,
  lexicalEditor
} from '@payloadcms/richtext-lexical'
import { s3Storage } from '@payloadcms/storage-s3'
import {
  customerCreated,
  customerDeleted,
  priceUpdated,
  productUpdated,
  subscriptionCreated,
  subscriptionDeleted,
  subscriptionUpdated
} from '@stripe/webhooks'

// import { webhooks } from './hooks'

export const plugins = [
  redirectsPlugin({
    collections: ['pages', 'posts'],
    overrides: {
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'from') {
            return {
              ...field,
              admin: {
                description:
                  'You will need to rebuild the website when changing this field.'
              }
            }
          }
          return field
        })
      },
      hooks: {
        afterChange: [revalidateRedirects]
      }
    }
  }),
  nestedDocsPlugin({
    collections: ['categories'],
    generateURL: (documents) =>
      documents.reduce((url, document_) => `${url}/${document_.slug}`, '')
  }),
  seoPlugin({
    collections: [],
    generateTitle: ({ doc }) => `${doc?.title} | Swift`,
    generateURL: ({ doc }) => `/posts/${doc?.slug}`
  }),
  formBuilderPlugin({
    fields: {
      payment: false
    },
    formOverrides: {
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'confirmationMessage') {
            return {
              ...field,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] })
                  ]
                }
              })
            }
          }
          return field
        })
      }
    }
  }),
  searchPlugin({
    collections: ['posts'],
    beforeSync: beforeSyncWithSearch,
    searchOverrides: {
      fields: ({ defaultFields }) => {
        return [...defaultFields, ...searchFields]
      }
    }
  }),
  s3Storage({
    collections: {
      media: {
        disableLocalStorage: true,
        disablePayloadAccessControl: true,
        prefix: collections.media.uploadDir
      }
    },
    config: {
      forcePathStyle: false,
      credentials: {
        accessKeyId: process.env.DO_S3_ACCESS_KEY,
        secretAccessKey: process.env.DO_S3_SECRET_KEY
      },
      region: process.env.DO_S3_REGION,
      endpoint: process.env.DO_S3_ENDPOINT
    },
    bucket: process.env.DO_S3_BUCKET,
    acl: 'public-read'
  }),
  stripePlugin({
    stripeSecretKey: process.env.STRIPE_SECRET_KEY,
    stripeWebhooksEndpointSecret: process.env.STRIPE_WEBHOOK_SECRET,
    rest: true,
    webhooks: {
      'product.created': productUpdated,
      'product.updated': productUpdated,
      'price.updated': priceUpdated,
      'customer.created': customerCreated,
      'customer.deleted': customerDeleted,
      'customer.subscription.created': subscriptionCreated,
      'customer.subscription.updated': subscriptionUpdated,
      'customer.subscription.deleted': subscriptionDeleted
    },
    logs: true
  })
]
