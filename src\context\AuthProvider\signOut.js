import { api } from '@api'
import { auth } from '@constants'
import { SwiftError } from '@error'

const { signOutError } = auth.errors

export const signOut = async () => {
  // Use regular user logout (consistent with our simplified auth flow)
  const { error } = await api.logout()

  if (error) {
    console.error('Error signing out', error)
    throw new SwiftError(signOutError)
  }

  return 'Sign out successful'
}
