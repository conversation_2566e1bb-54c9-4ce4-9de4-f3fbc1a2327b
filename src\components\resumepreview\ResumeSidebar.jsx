import { resumeHasSidebar } from '@utils'
import { ContactDetails } from './ContactDetails'
import { SectionComponent } from './SectionComponent'
import { SectionsContainer, SidebarInnerContainer, SidebarLayout } from './ui'

export const ResumeSidebar = ({ resumeData, tc = {}, mt = {} }) => {
  const { design } = resumeData
  const { layout, sections, sidebarStyle, palette, spacing } = design

  const hasSidebar = resumeHasSidebar(layout)

  if (!hasSidebar || !sections?.length) {
    return <div style={{ display: 'flex' }}></div>
  }

  const sidebarSections = sections.filter(
    (section) => section.isVisible && section.showInSidebar
  )

  return (
    <SidebarLayout sidebarStyle={sidebarStyle}>
      <SidebarInnerContainer sidebarStyle={sidebarStyle} palette={palette}>
        <SectionsContainer spacing={spacing}>
          <ContactDetails resumeData={resumeData} tc={tc} location='sidebar' />
          {sidebarSections.map((section) => {
            return (
              <SectionComponent
                key={section.id}
                sectionKey={section.sectionKey}
                resumeData={resumeData}
                tc={tc}
                mt={mt}
                location='sidebar'
              />
            )
          })}
        </SectionsContainer>
      </SidebarInnerContainer>
    </SidebarLayout>
  )
}
