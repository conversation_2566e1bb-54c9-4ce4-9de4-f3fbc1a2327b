import { addDataAndFileToRequest } from 'payload'
import { emailTypes } from '../../../constants'
import { sendEmail } from '../../../utils'
import { encrypt } from '../../../utils/encrypt.js'

export const requestOtp = {
  path: '/request-otp',
  method: 'post',
  summary: 'Request OTP',
  handler: async (request) => {
    await addDataAndFileToRequest(request)
    const { email, firstName } = request.data
    const rawOtp = Math.floor(100_000 + Math.random() * 900_000).toString()
    const otp = encrypt({ payload: request.payload, value: rawOtp })
    const now = Date.now()
    const expiresAt = new Date(now + 1000 * 60 * 5).toISOString()

    console.log('email', email)
    console.log('expiresAt', expiresAt)

    try {
      const findExistingOtp = await request.payload.find({
        collection: 'otp',
        where: {
          email: {
            equals: email
          }
        }
      })
      if (findExistingOtp?.docs?.length) {
        const existingOtp = findExistingOtp.docs[0]

        try {
          await request.payload.update({
            collection: 'otp',
            id: existingOtp.id,
            data: {
              email,
              otp,
              expiresAt
            }
          })

          sendEmail({
            to: email,
            type: emailTypes?.OTP || 'OTP',
            data: { otp: rawOtp, email: email, firstName: firstName }
          })

          return Response.json({ message: 'OTP sent' })
        } catch (error) {
          console.error('Error updating OTP', error)
          return Response.json({ error }, { status: 500 })
        }
      }
    } catch (error) {
      console.error('Error sending email', error)
      return Response.json({ error }, { status: 500 })
    }

    try {
      await request.payload.create({
        collection: 'otp',
        data: {
          email,
          otp,
          expiresAt
        }
      })
      sendEmail({
        to: email,
        type: emailTypes?.OTP || 'OTP',
        data: { otp: rawOtp, email: email, firstName: firstName }
      })
    } catch (error) {
      console.error('Error creating OTP', error)
      return Response.json({ error }, { status: 500 })
    }

    // return Response.json({ message: 'OTP sent', otp })
    return Response.json({ message: 'OTP sent' })
  }
}
