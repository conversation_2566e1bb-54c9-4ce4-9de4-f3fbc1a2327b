export const subscriptionUpdated = async (arguments_) => {
  const { event, payload } = arguments_

  const { id: subscriptionId, metadata } = event.data.object
  const userId = metadata?.userId
  const logs = true

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return {
      error: 'No user ID found in metadata',
      id: subscriptionId,
      status: event.data.object.status,
      userId: userId
    }
  }

  if (logs) {
    payload.logger.info(
      `Syncing Stripe subscription with ID: ${subscriptionId} to Payload...`
    )
  }

  try {
    const result = await payload.find({
      collection: 'subscriptions',
      where: {
        subscriptionId: {
          equals: subscriptionId
        }
      },
      depth: 0,
      limit: 1,
      pagination: false
    })

    if (result?.docs?.length === 0) {
      if (logs) {
        payload.logger.info(`- No subscription found with ID: ${subscriptionId}`)
      }
      return {
        error: 'No subscription found',
        id: subscriptionId,
        status: event.data.object.status,
        userId: userId
      }
    }

    const subscription = result.docs[0]

    if (subscription.subscriptionId !== subscriptionId) {
      if (logs) {
        payload.logger.info(
          `- Subscription ID mismatch, skipping...: ${(subscription.subscriptionId, subscriptionId)}`
        )
      }
      return {
        error: 'Subscription ID mismatch',
        id: subscriptionId,
        status: event.data.object.status,
        userId: userId
      }
    }

    await payload.update({
      collection: 'subscriptions',
      id: subscription.id,
      data: {
        status: event.data.object.status
      }
    })

    if (logs) {
      payload.logger.info(`✅ Successfully updated subscription.`)
    }
  } catch (error) {
    payload.logger.info(`- Error updating subscription: ${error}`)
    return {
      error: 'Error updating subscription',
      id: subscriptionId,
      status: event.data.object.status,
      userId: userId
    }
  }
}
