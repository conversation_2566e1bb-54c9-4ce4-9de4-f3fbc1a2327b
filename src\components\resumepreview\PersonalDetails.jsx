import { resumeHasSidebar, resumeStyles } from '@utils'
import { ContactDetails } from './ContactDetails'
import { UserProfile } from './UserProfile'
import { ProfileHeader } from './ui'

export const PersonalDetails = ({ resumeData, tc, location }) => {
  const { design } = resumeData
  const { layout, accent } = design

  const hasSidebar = resumeHasSidebar(layout)
  const personalInfoContainerStyle = resumeStyles.personalInfoContainer()

  return (
    <div style={personalInfoContainerStyle}>
      <ProfileHeader accent={accent} hasSidebar={hasSidebar}>
        <UserProfile resumeData={resumeData} location={location} />
        {!hasSidebar && (
          <ContactDetails resumeData={resumeData} tc={tc} location={location} />
        )}
      </ProfileHeader>
    </div>
  )
}
