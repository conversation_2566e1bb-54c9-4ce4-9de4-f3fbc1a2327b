'use client'

import { useState } from 'react'
import isEqual from 'lodash/isEqual'
import { Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Button,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle
} from '@components'
import { useMediaQuery } from '@raddix/use-media-query'
import { cn } from '@utils'

export const DeleteSectionItem = ({ orignalValue, currentValue, deleteItemHandler }) => {
  const tc = useTranslations('Common')
  const [open, setOpen] = useState(false)
  const isDesktop = useMediaQuery('(min-width: 1024px)')

  const deleteHanlder = () => {
    if (isEqual(orignalValue, currentValue)) {
      deleteItemHandler()
      return
    }
    setOpen(true)
  }

  return (
    <>
      <Button
        type='button'
        onClick={deleteHanlder}
        variant='subtle'
        size='link'
        className={cn('text-slate-500')}>
        <Trash2 />
      </Button>
      {isDesktop ? (
        <AlertDialog open={open} onOpenChange={setOpen}>
          <AlertDialogContent className='max-w-sm'>
            <AlertDialogHeader>
              <AlertDialogTitle>{tc('deleteResumeSection')}</AlertDialogTitle>
              <AlertDialogDescription>
                {tc('deleteResumeSectionAlert')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{tc('cancel')}</AlertDialogCancel>
              <AlertDialogAction variant='destructive' onClick={deleteItemHandler}>
                {tc('delete')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ) : (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>{tc('deleteResumeSection')}</DrawerTitle>
              <DrawerDescription>{tc('deleteResumeSectionAlert')}</DrawerDescription>
            </DrawerHeader>
            <DrawerFooter>
              <DrawerClose asChild>
                <Button variant='secondary'>{tc('cancel')}</Button>
              </DrawerClose>
              <Button variant='destructive' onClick={deleteItemHandler}>
                {tc('delete')}
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      )}
    </>
  )
}
