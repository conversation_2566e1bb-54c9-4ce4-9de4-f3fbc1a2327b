import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button, Separator } from '@components'

export const UndoRedoControls = () => {
  return (
    <div className='flex items-center gap-1'>
      <Button variant='subtle' size='link' className='text-slate-400'>
        <ChevronLeft />
      </Button>
      <Button variant='subtle' size='link' className='text-slate-400'>
        <ChevronRight size={16} />
      </Button>
      <Separator
        orientation='vertical'
        className='data-[orientation=vertical]:h-5 ml-2 bg-zinc-200'
      />
    </div>
  )
}
