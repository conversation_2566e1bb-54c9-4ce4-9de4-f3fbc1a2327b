@tailwind base;
@tailwind components;
@tailwind utilities;

@import './resume.css';

html,
body {
  @apply min-h-dvh text-slate-700;
}

.inner-container {
  @apply max-w-screen-cn w-full mx-auto px-3 md:px-6 lg:px-10;
}

.thin-scrollbar::-webkit-scrollbar {
  @apply w-1 h-2 bg-transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

@-moz-document url-prefix() {
  .thin-scrollbar {
    scrollbar-color: #d1d5db #f3f4f6;
    scrollbar-width: thin;
  }
}

.scrolled {
  .editor-header {
    @apply px-1.5 border-slate-200 bg-white shadow-md;
  }
}

.will-change-transform {
  will-change: transform;
}

.scrollbar-stable {
  scrollbar-gutter: stable;
}
