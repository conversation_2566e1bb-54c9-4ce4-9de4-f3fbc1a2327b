import React from 'react'
import { RichText } from '@/components/RichText'

const colsSpanClasses = {
  full: '12',
  half: '6',
  oneThird: '4',
  twoThirds: '8'
}

export const ContentBlock = ({ columns, disableInnerContainer = false }) => {
  const content = (
    <div className='grid grid-cols-4 lg:grid-cols-12 gap-y-8 gap-x-16'>
      {columns &&
        columns.length > 0 &&
        columns.map((col, index) => {
          const { enableLink, link, richText, size } = col

          return (
            <div
              className={`col-span-4 lg:col-span-${colsSpanClasses[size] || '12'} ${
                size === 'full' ? '' : 'md:col-span-2'
              }`}
              key={index}>
              {richText && <RichText data={richText} enableGutter={false} />}

              {enableLink && link && (
                <a
                  href={link.url || `/${link.reference?.slug}`}
                  className='text-blue-600 hover:underline'
                  target={link.newTab ? '_blank' : '_self'}>
                  {link.label || 'Learn More'}
                </a>
              )}
            </div>
          )
        })}
    </div>
  )

  if (disableInnerContainer) {
    return content
  }

  return <div className='container my-16'>{content}</div>
}
