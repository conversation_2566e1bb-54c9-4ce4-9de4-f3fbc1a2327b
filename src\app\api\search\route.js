import { NextResponse } from 'next/server'
import { getPayloadClient } from '@/getPayload'

export async function GET(request) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')

  if (!query) {
    return NextResponse.json({ results: [] })
  }

  try {
    const payload = await getPayloadClient()

    const { docs: results } = await payload.find({
      collection: 'search-results',
      where: {
        or: [
          {
            title: {
              contains: query
            }
          },
          {
            content: {
              contains: query
            }
          }
        ]
      },
      limit: 10,
      depth: 1
    })

    return NextResponse.json({ results })
  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json({ results: [] })
  }
}
