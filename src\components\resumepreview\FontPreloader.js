'use client'

import { useEffect } from 'react'
import { getFontUrl, isValidFontValue } from '@utils'

export const FontPreloader = ({ selectedFont }) => {
  useEffect(() => {
    if (!selectedFont || !isValidFontValue(selectedFont)) {
      return
    }

    const fontUrl = getFontUrl(selectedFont)
    if (!fontUrl) {
      return
    }

    const fontId = `font-${selectedFont}`
    const existingLink = document.querySelector(`#${fontId}`)
    if (existingLink) {
      return
    }

    const fontLink = document.createElement('link')
    fontLink.id = fontId
    fontLink.rel = 'stylesheet'
    fontLink.href = fontUrl
    document.head.append(fontLink)
  }, [selectedFont])

  return null
}
