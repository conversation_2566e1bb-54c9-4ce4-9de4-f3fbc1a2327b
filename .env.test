# Port. Don't change unless you know what you're doing. Referenced in nginx
PORT=4000

# Database connection string
DATABASE_URI=mongodb://localhost:27400 # mongodb-memory-server

# Used to encrypt JWT tokens (should be a 24 character random string)
PAYLOAD_SECRET=fafb2de7cd6f4676a6f2f0e0

# URLs
NEXT_PUBLIC_URL=https://swift.local

# Allow robots to index the site (optional), set to true in prod
NEXT_PUBLIC_IS_LIVE=0

# Don't sent data to Vercel
VERCEL_TELEMETRY_DISABLED=1
