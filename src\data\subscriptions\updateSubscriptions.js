import { stringify } from 'qs-esm'
import { subscriptions } from '@constants'
import { SwiftError } from '@error'

export const updateSubscriptions = async (subscriptionId, update) => {
  const whereQuery = {
    subscriptionId: {
      equals: subscriptionId
    }
  }

  const stringifiedQuery = stringify(
    {
      where: whereQuery
    },
    { addQueryPrefix: true }
  )

  try {
    const response = await fetch(
      process.env.NEXT_PUBLIC_URL + '/api/subscriptions' + stringifiedQuery,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(update)
      }
    )

    const data = await response.json()

    if (!response.ok) {
      console.error('Error updating subscription', data)
      throw new SwiftError(subscriptions.errors.updateError)
    }

    console.log('Updated subscription', data)

    return data
  } catch (error) {
    console.error('Error updating subscription', error)
    throw new SwiftError(subscriptions.errors.updateError)
  }
}
