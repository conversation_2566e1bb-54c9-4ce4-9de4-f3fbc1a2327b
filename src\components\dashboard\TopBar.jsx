import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { CreateResumeButton, Tabs, TabsList, TabsTrigger } from '@components'
import { routes } from '@constants'

export async function TopBar() {
  const tc = await getTranslations('Common')

  return (
    <div className='flex-shrink-0 px-4.5 flex gap-4 border-b border-slate-300 justify-between'>
      <Tabs defaultValue='resumes'>
        <TabsList variant='underline'>
          <TabsTrigger asChild value='resumes'>
            <Link className='h-14' href={routes.dashboard}>
              {tc('resumes')}
            </Link>
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <div className='flex items-center'>
        <CreateResumeButton />
      </div>
    </div>
  )
}
