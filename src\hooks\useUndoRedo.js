import { useCallback, useRef, useState } from 'react'
import { editor } from '@constants'

export const useUndoRedo = () => {
  const [undoStack, setUndoStack] = useState([])
  const [redoStack, setRedoStack] = useState([])
  const debounceTimer = useRef(null)
  const cachedState = useRef(null)

  const pushToUndoStack = useCallback((state) => {
    // The debounce ensures that we don't capture undo states on every keystroke
    if (!cachedState.current) {
      cachedState.current = state
    }

    clearTimeout(debounceTimer.current)

    debounceTimer.current = setTimeout(() => {
      if (cachedState.current) {
        const current = cachedState.current

        setUndoStack((stack) => {
          const newStack = [...stack, current]
          return newStack.length > editor.undoMaxStackSize
            ? newStack.slice(newStack.length - editor.undoMaxStackSize)
            : newStack
        })
        setRedoStack([])

        cachedState.current = null
      }
    }, editor.updateUndoStackDelayMs)
  }, [])

  const pushToRedoStack = useCallback((state) => {
    setRedoStack((stack) => {
      const newStack = [...stack, state]

      return newStack.length > editor.undoMaxStackSize
        ? newStack.slice(newStack.length - editor.undoMaxStackSize)
        : newStack
    })
  }, [])

  const undo = useCallback(
    (currentState) => {
      if (undoStack.length === 0) {
        return
      }

      const lastState = undoStack.at(-1)
      setUndoStack((stack) => stack.slice(0, -1))
      pushToRedoStack(currentState)

      return lastState
    },
    [undoStack, pushToRedoStack]
  )

  const redo = useCallback(
    (currentState) => {
      if (redoStack.length === 0) {
        return
      }

      const nextState = redoStack.at(-1)
      setRedoStack((stack) => stack.slice(0, -1))
      setUndoStack((stack) => [...stack, currentState])

      return nextState
    },
    [redoStack]
  )

  return {
    undoStack,
    redoStack,
    pushToUndoStack,
    pushToRedoStack,
    undo,
    redo
  }
}
