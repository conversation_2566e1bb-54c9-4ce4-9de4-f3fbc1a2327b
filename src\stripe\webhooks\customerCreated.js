export const customerCreated = async (arguments_) => {
  const { event, payload } = arguments_

  const { id: stripeCustomerID, metadata, email } = event.data.object
  const userId = metadata?.userId
  const logs = true

  payload.logger.info(
    `Customer Created: ${stripeCustomerID}.  User ID: ${userId}. Email: ${email}`
  )

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return
  }

  if (logs) {
    payload.logger.info(
      `Syncing Stripe customer with ID: ${stripeCustomerID} to Payload...`
    )
  }

  if (!userId) {
    if (logs) {
      payload.logger.info(`- No user ID found in metadata, skipping...`)
    }
    return
  }

  try {
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 0
    })

    if (!user && user.id !== userId) {
      if (logs) {
        payload.logger.info(`- No user found with ID: ${userId}`)
      }
      return
    }

    if (logs) {
      payload.logger.info(`- Found user with ID: ${userId}`)
    }

    const data = {
      stripeCustomerId: stripeCustomerID
    }

    if (user.role === 'anon') {
      if (logs) {
        payload.logger.info(`- Found anon user, updating email to: ${email}`)
      }
      data.email = email
    }

    await payload.update({
      collection: 'users',
      id: userId,
      data: data
    })

    if (logs) {
      payload.logger.info(`✅ Successfully updated user with Stripe customer ID.`)
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    payload.logger.info(`- Error updating user: ${message}`)
  }
}
