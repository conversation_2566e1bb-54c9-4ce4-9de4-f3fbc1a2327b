export const booleanEnvVariable = (envVariable) => {
  const truthyValues = ['1', 'true']
  const falsyValues = ['0', 'false']

  if (envVariable === undefined || envVariable === null) {
    return false
  }

  const value = String(envVariable).toLowerCase().trim()

  if (truthyValues.includes(value)) {
    return true
  }

  if (falsyValues.includes(value)) {
    return false
  }

  throw new Error(
    `Expected env var "${envVariable}" to be a boolean value (0, 1, false, true)`
  )
}
