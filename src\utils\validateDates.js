export const validateDates = (data) => {
  const { startMonth, startYear, endMonth, endYear, isPresent } = data

  if (isPresent) {
    return true
  }

  // If any date field is empty, skip validation (return true)
  if (!startMonth || !startYear || !endMonth || !endYear) {
    return true
  }

  const startDate = new Date(`${startYear}-${startMonth}-01`)
  const endDate = new Date(`${endYear}-${endMonth}-01`)

  return endDate >= startDate
}
