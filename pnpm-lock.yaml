lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

overrides:
  peek-readable: 5.3.1

importers:
  .:
    dependencies:
      '@hey-api/client-fetch':
        specifier: ^0.4.2
        version: 0.4.4
      '@hookform/resolvers':
        specifier: ^3.9.1
        version: 3.10.0(react-hook-form@7.57.0(react@19.0.0))
      '@lexical/code':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/history':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/html':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/link':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/list':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/markdown':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/react':
        specifier: 0.28.0
        version: 0.28.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(yjs@13.6.23)
      '@lexical/rich-text':
        specifier: 0.28.0
        version: 0.28.0
      '@lexical/utils':
        specifier: 0.28.0
        version: 0.28.0
      '@payloadcms/db-mongodb':
        specifier: 3.50.0
        version: 3.50.0(@aws-sdk/credential-providers@3.738.0)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      '@payloadcms/email-resend':
        specifier: 3.50.0
        version: 3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      '@payloadcms/live-preview-react':
        specifier: ^3.50.0
        version: 3.50.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@payloadcms/next':
        specifier: 3.50.0
        version: 3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/payload-cloud':
        specifier: 3.50.0
        version: 3.50.0(encoding@0.1.13)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      '@payloadcms/plugin-form-builder':
        specifier: 3.50.0
        version: 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/plugin-nested-docs':
        specifier: 3.50.0
        version: 3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      '@payloadcms/plugin-redirects':
        specifier: 3.50.0
        version: 3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      '@payloadcms/plugin-search':
        specifier: 3.50.0
        version: 3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/plugin-seo':
        specifier: 3.50.0
        version: 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/plugin-stripe':
        specifier: 3.50.0
        version: 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/richtext-lexical':
        specifier: 3.50.0
        version: 3.50.0(@faceless-ui/modal@3.0.0-beta.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@faceless-ui/scroll-info@2.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@payloadcms/next@3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3))(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)(yjs@13.6.23)
      '@payloadcms/storage-s3':
        specifier: 3.50.0
        version: 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/ui':
        specifier: ^3.50.0
        version: 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@raddix/use-media-query':
        specifier: ^0.1.3
        version: 0.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@radix-ui/react-accordion':
        specifier: ^1.2.3
        version: 1.2.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-alert-dialog':
        specifier: ^1.1.5
        version: 1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-avatar':
        specifier: ^1.1.2
        version: 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-checkbox':
        specifier: ^1.1.3
        version: 1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dialog':
        specifier: ^1.1.5
        version: 1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dropdown-menu':
        specifier: ^2.1.4
        version: 2.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-label':
        specifier: ^2.1.1
        version: 2.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-radio-group':
        specifier: ^1.2.3
        version: 1.2.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-select':
        specifier: ^2.1.6
        version: 2.1.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-separator':
        specifier: ^1.1.1
        version: 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot':
        specifier: ^1.1.1
        version: 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-switch':
        specifier: ^1.1.2
        version: 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-tabs':
        specifier: ^1.1.3
        version: 1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@react-email/components':
        specifier: 0.0.31
        version: 0.0.31(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@react-email/render':
        specifier: 1.0.3
        version: 1.0.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@react-pdf/renderer':
        specifier: ^4.3.0
        version: 4.3.0(react@19.0.0)
      '@resvg/resvg-wasm':
        specifier: ^2.6.2
        version: 2.6.2
      '@stripe/react-stripe-js':
        specifier: ^3.7.0
        version: 3.7.0(@stripe/stripe-js@7.5.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@stripe/stripe-js':
        specifier: ^7.5.0
        version: 7.5.0
      '@tanstack/react-query':
        specifier: ^5.59.20
        version: 5.65.1(react@19.0.0)
      '@tanstack/react-query-devtools':
        specifier: ^5.59.20
        version: 5.65.1(@tanstack/react-query@5.65.1(react@19.0.0))(react@19.0.0)
      blob-stream:
        specifier: ^0.1.3
        version: 0.1.3
      body-scroll-lock:
        specifier: 4.0.0-beta.0
        version: 4.0.0-beta.0
      class-variance-authority:
        specifier: ^0.7.0
        version: 0.7.1
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      cross-env:
        specifier: ^7.0.3
        version: 7.0.3
      html-react-parser:
        specifier: ^5.2.5
        version: 5.2.5(react@19.0.0)(types-react@19.0.0-rc.1)
      htmlparser2:
        specifier: ^10.0.0
        version: 10.0.0
      input-otp:
        specifier: ^1.4.2
        version: 1.4.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      install:
        specifier: ^0.13.0
        version: 0.13.0
      jose:
        specifier: ^5.9.6
        version: 5.9.6
      lexical:
        specifier: 0.28.0
        version: 0.28.0
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      lucide-react:
        specifier: ^0.461.0
        version: 0.461.0(react@19.0.0)
      motion:
        specifier: ^12.5.0
        version: 12.5.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      nanoid:
        specifier: ^5.1.5
        version: 5.1.5
      next:
        specifier: 15.3.4
        version: 15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4)
      next-intl:
        specifier: ^3.26.3
        version: 3.26.3(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(react@19.0.0)
      next-logger:
        specifier: ^5.0.1
        version: 5.0.1(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(pino@9.6.0)
      next-themes:
        specifier: ^0.4.4
        version: 0.4.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      pagedjs:
        specifier: ^0.4.3
        version: 0.4.3
      payload:
        specifier: 3.50.0
        version: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      pdfkit:
        specifier: ^0.17.1
        version: 0.17.1
      peek-readable:
        specifier: 5.3.1
        version: 5.3.1
      pino:
        specifier: ^9.5.0
        version: 9.6.0
      qs-esm:
        specifier: ^7.0.2
        version: 7.0.2
      react:
        specifier: 19.0.0
        version: 19.0.0
      react-dom:
        specifier: 19.0.0
        version: 19.0.0(react@19.0.0)
      react-hook-form:
        specifier: 7.57.0
        version: 7.57.0(react@19.0.0)
      react-hotkeys-hook:
        specifier: ^4.6.1
        version: 4.6.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      resend:
        specifier: ^4.0.1
        version: 4.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      satori:
        specifier: ^0.15.2
        version: 0.15.2
      sharp:
        specifier: ^0.33.5
        version: 0.33.5
      sonner:
        specifier: ^1.7.4
        version: 1.7.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      stripe:
        specifier: ^18.3.0
        version: 18.3.0(@types/node@22.12.0)
      tailwind-merge:
        specifier: ^2.5.5
        version: 2.6.0
      tailwindcss-animate:
        specifier: ^1.0.7
        version: 1.0.7(tailwindcss@3.4.17)
      tiny-cookie:
        specifier: ^2.5.1
        version: 2.5.1
      validator:
        specifier: ^13.12.0
        version: 13.12.0
      vaul:
        specifier: ^1.1.2
        version: 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      zod:
        specifier: ^3.24.1
        version: 3.24.1
    devDependencies:
      '@hey-api/openapi-ts':
        specifier: ^0.54.1
        version: 0.54.4(typescript@5.6.3)
      '@tanstack/eslint-plugin-query':
        specifier: ^5.60.1
        version: 5.65.0(eslint@8.57.1)(typescript@5.6.3)
      '@trivago/prettier-plugin-sort-imports':
        specifier: ^4.3.0
        version: 4.3.0(prettier@3.4.2)
      '@types/node':
        specifier: ^22.5.4
        version: 22.12.0
      '@types/react':
        specifier: npm:types-react@19.0.0-rc.1
        version: types-react@19.0.0-rc.1
      '@types/react-dom':
        specifier: npm:types-react-dom@19.0.0-rc.1
        version: types-react-dom@19.0.0-rc.1
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.20(postcss@8.5.1)
      camelcase:
        specifier: ^8.0.0
        version: 8.0.0
      eslint:
        specifier: ^8
        version: 8.57.1
      eslint-config-next:
        specifier: 15.0.0
        version: 15.0.0(eslint@8.57.1)(typescript@5.6.3)
      eslint-config-prettier:
        specifier: ^9.1.0
        version: 9.1.0(eslint@8.57.1)
      eslint-plugin-disable:
        specifier: ^2.0.3
        version: 2.0.3(eslint@8.57.1)
      eslint-plugin-import:
        specifier: ^2.31.0
        version: 2.31.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1)
      eslint-plugin-jsx-a11y:
        specifier: ^6.10.2
        version: 6.10.2(eslint@8.57.1)
      eslint-plugin-unicorn:
        specifier: ^56.0.1
        version: 56.0.1(eslint@8.57.1)
      husky:
        specifier: ^9.1.6
        version: 9.1.7
      lint-staged:
        specifier: ^15.2.10
        version: 15.4.3
      pino-pretty:
        specifier: ^13.0.0
        version: 13.0.0
      postcss:
        specifier: ^8.4.49
        version: 8.5.1
      semver:
        specifier: ^7.6.3
        version: 7.7.0
      tailwindcss:
        specifier: ^3.4.15
        version: 3.4.17
      typescript:
        specifier: 5.6.3
        version: 5.6.3

packages:
  '@alloc/quick-lru@5.2.0':
    resolution:
      {
        integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==
      }
    engines: { node: '>=10' }

  '@apidevtools/json-schema-ref-parser@11.7.2':
    resolution:
      {
        integrity: sha512-4gY54eEGEstClvEkGnwVkTkrx0sqwemEFG5OSRRn3tD91XH0+Q8XIkYIfo7IwEWPpJZwILb9GUXeShtplRc/eA==
      }
    engines: { node: '>= 16' }

  '@apidevtools/json-schema-ref-parser@11.9.0':
    resolution:
      {
        integrity: sha512-8Q/r5mXLa8Rfyh6r4SgEEFJgISVN5cDNFlcfSWLgFn3odzQhTfHAqzI3hMGdcROViL+8NrDNVVFQtEUrYOksDg==
      }
    engines: { node: '>= 16' }

  '@aws-crypto/crc32@5.2.0':
    resolution:
      {
        integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==
      }
    engines: { node: '>=16.0.0' }

  '@aws-crypto/crc32c@5.2.0':
    resolution:
      {
        integrity: sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==
      }

  '@aws-crypto/sha1-browser@5.2.0':
    resolution:
      {
        integrity: sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==
      }

  '@aws-crypto/sha256-browser@5.2.0':
    resolution:
      {
        integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==
      }

  '@aws-crypto/sha256-js@1.2.2':
    resolution:
      {
        integrity: sha512-Nr1QJIbW/afYYGzYvrF70LtaHrIRtd4TNAglX8BvlfxJLZ45SAmueIKYl5tWoNBPzp65ymXGFK0Bb1vZUpuc9g==
      }

  '@aws-crypto/sha256-js@5.2.0':
    resolution:
      {
        integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==
      }
    engines: { node: '>=16.0.0' }

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution:
      {
        integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==
      }

  '@aws-crypto/util@1.2.2':
    resolution:
      {
        integrity: sha512-H8PjG5WJ4wz0UXAFXeJjWCW1vkvIJ3qUUD+rGRwJ2/hj+xT58Qle2MTql/2MGzkU+1JLAFuR6aJpLAjHwhmwwg==
      }

  '@aws-crypto/util@5.2.0':
    resolution:
      {
        integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==
      }

  '@aws-sdk/client-cognito-identity@3.738.0':
    resolution:
      {
        integrity: sha512-TjPpLZ2qkh+2jQIYtUbNh5D6jv4U0DQIUiLLZOKalUqSK2L9OzTc1463kX076QCpYlAZJNt3FvPyiMab0W8zBg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/client-s3@3.738.0':
    resolution:
      {
        integrity: sha512-1Im/p5yfoV15ydVY+QlffsWQkQm7iGVI+3V9tCHEUT6SdmukYEpN3G8Y+lWofRBidxzUE2Xd+MbChCXfzLAoAg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/client-sso@3.734.0':
    resolution:
      {
        integrity: sha512-oerepp0mut9VlgTwnG5Ds/lb0C0b2/rQ+hL/rF6q+HGKPfGsCuPvFx1GtwGKCXd49ase88/jVgrhcA9OQbz3kg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/core@3.734.0':
    resolution:
      {
        integrity: sha512-SxnDqf3vobdm50OLyAKfqZetv6zzwnSqwIwd3jrbopxxHKqNIM/I0xcYjD6Tn+mPig+u7iRKb9q3QnEooFTlmg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/core@3.846.0':
    resolution:
      {
        integrity: sha512-7CX0pM906r4WSS68fCTNMTtBCSkTtf3Wggssmx13gD40gcWEZXsU00KzPp1bYheNRyPlAq3rE22xt4wLPXbuxA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-cognito-identity@3.738.0':
    resolution:
      {
        integrity: sha512-zh8ATHUjy9CyVrq7qa7ICh4t5OF7mps0A22NY2NyLpSYWOErNnze+FvBi2uh+Jp+VIoojH4R2d9/IHTxENhq7g==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-env@3.734.0':
    resolution:
      {
        integrity: sha512-gtRkzYTGafnm1FPpiNO8VBmJrYMoxhDlGPYDVcijzx3DlF8dhWnowuSBCxLSi+MJMx5hvwrX2A+e/q0QAeHqmw==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-http@3.734.0':
    resolution:
      {
        integrity: sha512-JFSL6xhONsq+hKM8xroIPhM5/FOhiQ1cov0lZxhzZWj6Ai3UAjucy3zyIFDr9MgP1KfCYNdvyaUq9/o+HWvEDg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-ini@3.734.0':
    resolution:
      {
        integrity: sha512-HEyaM/hWI7dNmb4NhdlcDLcgJvrilk8G4DQX6qz0i4pBZGC2l4iffuqP8K6ZQjUfz5/6894PzeFuhTORAMd+cg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-node@3.738.0':
    resolution:
      {
        integrity: sha512-3MuREsazwBxghKb2sQQHvie+uuK4dX4/ckFYiSoffzJQd0YHxaGxf8cr4NOSCQCUesWu8D3Y0SzlnHGboVSkpA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-process@3.734.0':
    resolution:
      {
        integrity: sha512-zvjsUo+bkYn2vjT+EtLWu3eD6me+uun+Hws1IyWej/fKFAqiBPwyeyCgU7qjkiPQSXqk1U9+/HG9IQ6Iiz+eBw==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-sso@3.734.0':
    resolution:
      {
        integrity: sha512-cCwwcgUBJOsV/ddyh1OGb4gKYWEaTeTsqaAK19hiNINfYV/DO9r4RMlnWAo84sSBfJuj9shUNsxzyoe6K7R92Q==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-provider-web-identity@3.734.0':
    resolution:
      {
        integrity: sha512-t4OSOerc+ppK541/Iyn1AS40+2vT/qE+MFMotFkhCgCJbApeRF2ozEdnDN6tGmnl4ybcUuxnp9JWLjwDVlR/4g==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/credential-providers@3.738.0':
    resolution:
      {
        integrity: sha512-Ff+7NMLmK9oadO1uHiMCS/V3Pmp3WnY7Ijy4ySx2HLUZQq7EKFZyFB0qslkeawdY0PGWqyj25anh8I/bhxqWoQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/lib-storage@3.738.0':
    resolution:
      {
        integrity: sha512-YUBGp3k5Dg8RqHrllS89PjRiqpyIR3eKcQsCTM0bLzf3uRCjiCeSzlnl/co5W7Kxgc+eCnq0IitGYXR/mYFKeA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      '@aws-sdk/client-s3': ^3.738.0

  '@aws-sdk/middleware-bucket-endpoint@3.734.0':
    resolution:
      {
        integrity: sha512-etC7G18aF7KdZguW27GE/wpbrNmYLVT755EsFc8kXpZj8D6AFKxc7OuveinJmiy0bYXAMspJUWsF6CrGpOw6CQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-expect-continue@3.734.0':
    resolution:
      {
        integrity: sha512-P38/v1l6HjuB2aFUewt7ueAW5IvKkFcv5dalPtbMGRhLeyivBOHwbCyuRKgVs7z7ClTpu9EaViEGki2jEQqEsQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-flexible-checksums@3.735.0':
    resolution:
      {
        integrity: sha512-Tx7lYTPwQFRe/wQEHMR6Drh/S+X0ToAEq1Ava9QyxV1riwtepzRLojpNDELFb3YQVVYbX7FEiBMCJLMkmIIY+A==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-host-header@3.734.0':
    resolution:
      {
        integrity: sha512-LW7RRgSOHHBzWZnigNsDIzu3AiwtjeI2X66v+Wn1P1u+eXssy1+up4ZY/h+t2sU4LU36UvEf+jrZti9c6vRnFw==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-location-constraint@3.734.0':
    resolution:
      {
        integrity: sha512-EJEIXwCQhto/cBfHdm3ZOeLxd2NlJD+X2F+ZTOxzokuhBtY0IONfC/91hOo5tWQweerojwshSMHRCKzRv1tlwg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-logger@3.734.0':
    resolution:
      {
        integrity: sha512-mUMFITpJUW3LcKvFok176eI5zXAUomVtahb9IQBwLzkqFYOrMJvWAvoV4yuxrJ8TlQBG8gyEnkb9SnhZvjg67w==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-recursion-detection@3.734.0':
    resolution:
      {
        integrity: sha512-CUat2d9ITsFc2XsmeiRQO96iWpxSKYFjxvj27Hc7vo87YUHRnfMfnc8jw1EpxEwMcvBD7LsRa6vDNky6AjcrFA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-sdk-s3@3.734.0':
    resolution:
      {
        integrity: sha512-zeZPenDhkP/RXYMFG3exhNOe2Qukg2l2KpIjxq9o66meELiTULoIXjCmgPoWcM8zzrue06SBdTsaJDHfDl2vdA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-sdk-s3@3.846.0':
    resolution:
      {
        integrity: sha512-jP9x+2Q87J5l8FOP+jlAd7vGLn0cC6G9QGmf386e5OslBPqxXKcl3RjqGLIOKKos2mVItY3ApP5xdXQx7jGTVA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-ssec@3.734.0':
    resolution:
      {
        integrity: sha512-d4yd1RrPW/sspEXizq2NSOUivnheac6LPeLSLnaeTbBG9g1KqIqvCzP1TfXEqv2CrWfHEsWtJpX7oyjySSPvDQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/middleware-user-agent@3.734.0':
    resolution:
      {
        integrity: sha512-MFVzLWRkfFz02GqGPjqSOteLe5kPfElUrXZft1eElnqulqs6RJfVSpOV7mO90gu293tNAeggMWAVSGRPKIYVMg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/nested-clients@3.734.0':
    resolution:
      {
        integrity: sha512-iph2XUy8UzIfdJFWo1r0Zng9uWj3253yvW9gljhtu+y/LNmNvSnJxQk1f3D2BC5WmcoPZqTS3UsycT3mLPSzWA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/region-config-resolver@3.734.0':
    resolution:
      {
        integrity: sha512-Lvj1kPRC5IuJBr9DyJ9T9/plkh+EfKLy+12s/mykOy1JaKHDpvj+XGy2YO6YgYVOb8JFtaqloid+5COtje4JTQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/s3-request-presigner@3.848.0':
    resolution:
      {
        integrity: sha512-fm75L4M98UOPjFORyLdIgsPOuSXdbfpfOl3bNWcWFmeG8tDhqY+lGOyALYEZw/0O1MzEnyeK7wEGwAOf2PO5ZQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/signature-v4-multi-region@3.734.0':
    resolution:
      {
        integrity: sha512-GSRP8UH30RIYkcpPILV4pWrKFjRmmNjtUd41HTKWde5GbjJvNYpxqFXw2aIJHjKTw/js3XEtGSNeTaQMVVt3CQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/signature-v4-multi-region@3.846.0':
    resolution:
      {
        integrity: sha512-ZMfIMxUljqZzPJGOcraC6erwq/z1puNMU35cO1a/WdhB+LdYknMn1lr7SJuH754QwNzzIlZbEgg4hoHw50+DpQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/token-providers@3.734.0':
    resolution:
      {
        integrity: sha512-2U6yWKrjWjZO8Y5SHQxkFvMVWHQWbS0ufqfAIBROqmIZNubOL7jXCiVdEFekz6MZ9LF2tvYGnOW4jX8OKDGfIw==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/types@3.734.0':
    resolution:
      {
        integrity: sha512-o11tSPTT70nAkGV1fN9wm/hAIiLPyWX6SuGf+9JyTp7S/rC2cFWhR26MvA69nplcjNaXVzB0f+QFrLXXjOqCrg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/types@3.840.0':
    resolution:
      {
        integrity: sha512-xliuHaUFZxEx1NSXeLLZ9Dyu6+EJVQKEoD+yM+zqUo3YDZ7medKJWY6fIOKiPX/N7XbLdBYwajb15Q7IL8KkeA==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-arn-parser@3.723.0':
    resolution:
      {
        integrity: sha512-ZhEfvUwNliOQROcAk34WJWVYTlTa4694kSVhDSjW6lE1bMataPnIN8A0ycukEzBXmd8ZSoBcQLn6lKGl7XIJ5w==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-arn-parser@3.804.0':
    resolution:
      {
        integrity: sha512-wmBJqn1DRXnZu3b4EkE6CWnoWMo1ZMvlfkqU5zPz67xx1GMaXlDCchFvKAXMjk4jn/L1O3tKnoFDNsoLV1kgNQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-endpoints@3.734.0':
    resolution:
      {
        integrity: sha512-w2+/E88NUbqql6uCVAsmMxDQKu7vsKV0KqhlQb0lL+RCq4zy07yXYptVNs13qrnuTfyX7uPXkXrlugvK9R1Ucg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-format-url@3.840.0':
    resolution:
      {
        integrity: sha512-VB1PWyI1TQPiPvg4w7tgUGGQER1xxXPNUqfh3baxUSFi1Oh8wHrDnFywkxLm3NMmgDmnLnSZ5Q326qAoyqKLSg==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-locate-window@3.723.0':
    resolution:
      {
        integrity: sha512-Yf2CS10BqK688DRsrKI/EO6B8ff5J86NXe4C+VCysK7UOgN0l1zOTeTukZ3H8Q9tYYX3oaF1961o8vRkFm7Nmw==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/util-user-agent-browser@3.734.0':
    resolution:
      {
        integrity: sha512-xQTCus6Q9LwUuALW+S76OL0jcWtMOVu14q+GoLnWPUM7QeUw963oQcLhF7oq0CtaLLKyl4GOUfcwc773Zmwwng==
      }

  '@aws-sdk/util-user-agent-node@3.734.0':
    resolution:
      {
        integrity: sha512-c6Iinh+RVQKs6jYUFQ64htOU2HUXFQ3TVx+8Tu3EDF19+9vzWi9UukhIMH9rqyyEXIAkk9XL7avt8y2Uyw2dGA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/util-utf8-browser@3.259.0':
    resolution:
      {
        integrity: sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==
      }

  '@aws-sdk/xml-builder@3.734.0':
    resolution:
      {
        integrity: sha512-Zrjxi5qwGEcUsJ0ru7fRtW74WcTS0rbLcehoFB+rN1GRi2hbLcFaYs4PwVA5diLeAJH0gszv3x4Hr/S87MfbKQ==
      }
    engines: { node: '>=18.0.0' }

  '@aws-sdk/xml-builder@3.821.0':
    resolution:
      {
        integrity: sha512-DIIotRnefVL6DiaHtO6/21DhJ4JZnnIwdNbpwiAhdt/AVbttcE4yw925gsjur0OGv5BTYXQXU3YnANBYnZjuQA==
      }
    engines: { node: '>=18.0.0' }

  '@babel/code-frame@7.26.2':
    resolution:
      {
        integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
      }
    engines: { node: '>=6.9.0' }

  '@babel/generator@7.17.7':
    resolution:
      {
        integrity: sha512-oLcVCTeIFadUoArDTwpluncplrYBmTCCZZgXCbgNGvOBBiSDDK3eWO4b/+eOTli5tKv1lg+a5/NAXg+nTcei1w==
      }
    engines: { node: '>=6.9.0' }

  '@babel/generator@7.26.5':
    resolution:
      {
        integrity: sha512-2caSP6fN9I7HOe6nqhtft7V4g7/V/gfDsC3Ag4W7kEzzvRGKqiv0pu0HogPiZ3KaVSoNDhUws6IJjDjpfmYIXw==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-environment-visitor@7.24.7':
    resolution:
      {
        integrity: sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-function-name@7.24.7':
    resolution:
      {
        integrity: sha512-FyoJTsj/PEUWu1/TYRiXTIHc8lbw+TDYkZuoE43opPS5TrI7MyONBE1oNvfguEXAD9yhQRrVBnXdXzSLQl9XnA==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-hoist-variables@7.24.7':
    resolution:
      {
        integrity: sha512-MJJwhkoGy5c4ehfoRyrJ/owKeMl19U54h27YYftT0o2teQ3FJ3nQUf/I3LlJsX4l3qlw7WRXUmiyajvHXoTubQ==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-module-imports@7.25.9':
    resolution:
      {
        integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-split-export-declaration@7.24.7':
    resolution:
      {
        integrity: sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-string-parser@7.25.9':
    resolution:
      {
        integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-validator-identifier@7.25.9':
    resolution:
      {
        integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==
      }
    engines: { node: '>=6.9.0' }

  '@babel/parser@7.26.7':
    resolution:
      {
        integrity: sha512-kEvgGGgEjRUutvdVvZhbn/BxVt+5VSpwXz1j3WYXQbXDo8KzFOPNG2GQbdAiNq8g6wn1yKk7C/qrke03a84V+w==
      }
    engines: { node: '>=6.0.0' }
    hasBin: true

  '@babel/polyfill@7.12.1':
    resolution:
      {
        integrity: sha512-X0pi0V6gxLi6lFZpGmeNa4zxtwEmCs42isWLNjZZDE0Y8yVfgu0T2OAHlzBbdYlqbW/YXVvoBHpATEM+goCj8g==
      }
    deprecated: 🚨 This package has been deprecated in favor of separate inclusion of a polyfill and regenerator-runtime (when needed). See the @babel/polyfill docs (https://babeljs.io/docs/en/babel-polyfill) for more information.

  '@babel/runtime@7.26.7':
    resolution:
      {
        integrity: sha512-AOPI3D+a8dXnja+iwsUqGRjr1BbZIe771sXdapOtYI531gSqpi92vXivKcq2asu/DFpdl1ceFAKZyRzK2PCVcQ==
      }
    engines: { node: '>=6.9.0' }

  '@babel/template@7.25.9':
    resolution:
      {
        integrity: sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==
      }
    engines: { node: '>=6.9.0' }

  '@babel/traverse@7.23.2':
    resolution:
      {
        integrity: sha512-azpe59SQ48qG6nu2CzcMLbxUudtN+dOM9kDbUqGq3HXUJRlo7i8fvPoxQUzYgLZ4cMVmuZgm8vvBpNeRhd6XSw==
      }
    engines: { node: '>=6.9.0' }

  '@babel/traverse@7.26.7':
    resolution:
      {
        integrity: sha512-1x1sgeyRLC3r5fQOM0/xtQKsYjyxmFjaOrLJNtZ81inNjyJHGIolTULPiSc/2qe1/qfpFLisLQYFnnZl7QoedA==
      }
    engines: { node: '>=6.9.0' }

  '@babel/types@7.17.0':
    resolution:
      {
        integrity: sha512-TmKSNO4D5rzhL5bjWFcVHHLETzfQ/AmbKpKPOSjlP0WoHZ6L911fgoOKY4Alp/emzG4cHJdyN49zpgkbXFEHHw==
      }
    engines: { node: '>=6.9.0' }

  '@babel/types@7.26.7':
    resolution:
      {
        integrity: sha512-t8kDRGrKXyp6+tjUh7hw2RLyclsW4TRoRvRHtSyAX9Bb5ldlFh+90YAYY6awRXrlB4G5G2izNeGySpATlFzmOg==
      }
    engines: { node: '>=6.9.0' }

  '@date-fns/tz@1.2.0':
    resolution:
      {
        integrity: sha512-LBrd7MiJZ9McsOgxqWX7AaxrDjcFVjWH/tIKJd7pnR7McaslGYOP1QmmiBXdJH/H/yLCT+rcQ7FaPBUxRGUtrg==
      }

  '@dnd-kit/accessibility@3.1.1':
    resolution:
      {
        integrity: sha512-2P+YgaXF+gRsIihwwY1gCsQSYnu9Zyj2py8kY5fFvUM1qm2WA2u639R6YNVfU4GWr+ZM5mqEsfHZZLoRONbemw==
      }
    peerDependencies:
      react: '>=16.8.0'

  '@dnd-kit/core@6.0.8':
    resolution:
      {
        integrity: sha512-lYaoP8yHTQSLlZe6Rr9qogouGUz9oRUj4AHhDQGQzq/hqaJRpFo65X+JKsdHf8oUFBzx5A+SJPUvxAwTF2OabA==
      }
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@dnd-kit/sortable@7.0.2':
    resolution:
      {
        integrity: sha512-wDkBHHf9iCi1veM834Gbk1429bd4lHX4RpAwT0y2cHLf246GAvU2sVw/oxWNpPKQNQRQaeGXhAVgrOl1IT+iyA==
      }
    peerDependencies:
      '@dnd-kit/core': ^6.0.7
      react: '>=16.8.0'

  '@dnd-kit/utilities@3.2.2':
    resolution:
      {
        integrity: sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==
      }
    peerDependencies:
      react: '>=16.8.0'

  '@emnapi/runtime@1.3.1':
    resolution:
      {
        integrity: sha512-kEBmG8KyqtxJZv+ygbEim+KCGtIq1fC22Ms3S4ziXmYKm8uyoLX0MHONVKwp+9opg390VaKRNt4a7A9NwmpNhw==
      }

  '@emnapi/runtime@1.4.3':
    resolution:
      {
        integrity: sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==
      }

  '@emotion/babel-plugin@11.13.5':
    resolution:
      {
        integrity: sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==
      }

  '@emotion/cache@11.14.0':
    resolution:
      {
        integrity: sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==
      }

  '@emotion/hash@0.9.2':
    resolution:
      {
        integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==
      }

  '@emotion/memoize@0.9.0':
    resolution:
      {
        integrity: sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==
      }

  '@emotion/react@11.14.0':
    resolution:
      {
        integrity: sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==
      }
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/serialize@1.3.3':
    resolution:
      {
        integrity: sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==
      }

  '@emotion/sheet@1.4.0':
    resolution:
      {
        integrity: sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==
      }

  '@emotion/unitless@0.10.0':
    resolution:
      {
        integrity: sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==
      }

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0':
    resolution:
      {
        integrity: sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==
      }
    peerDependencies:
      react: '>=16.8.0'

  '@emotion/utils@1.4.2':
    resolution:
      {
        integrity: sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==
      }

  '@emotion/weak-memoize@0.4.0':
    resolution:
      {
        integrity: sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==
      }

  '@esbuild/aix-ppc64@0.23.1':
    resolution:
      {
        integrity: sha512-6VhYk1diRqrhBAqpJEdjASR/+WVRtfjpqKuNw11cLiaWpAT/Uu+nokB+UJnevzy/P9C/ty6AOe0dwueMrGh/iQ==
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [aix]

  '@esbuild/aix-ppc64@0.25.8':
    resolution:
      {
        integrity: sha512-urAvrUedIqEiFR3FYSLTWQgLu5tb+m0qZw0NBEasUeo6wuqatkMDaRT+1uABiGXEu5vqgPd7FGE1BhsAIy9QVA==
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-xw50ipykXcLstLeWH7WRdQuysJqejuAGPd30vd1i5zSyKK3WE+ijzHmLKxdiCMtH1pHz78rOg0BKSYOSB/2Khw==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-OD3p7LYzWpLhZEyATcTSJ67qB5D+20vbtr6vHlHWSQYhKtzUYrETuWThmzFpZtFsBIxRvhO07+UgVA9m0i/O1w==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.23.1':
    resolution:
      {
        integrity: sha512-uz6/tEy2IFm9RYOyvKl88zdzZfwEfKZmnX9Cj1BHjeSGNuGLuMD1kR8y5bteYmwqKm1tj8m4cb/aKEorr6fHWQ==
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.25.8':
    resolution:
      {
        integrity: sha512-RONsAvGCz5oWyePVnLdZY/HHwA++nxYWIX1atInlaW6SEkwq6XkP3+cb825EUcRs5Vss/lGh/2YxAb5xqc07Uw==
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.23.1':
    resolution:
      {
        integrity: sha512-nlN9B69St9BwUoB+jkyU090bru8L0NA3yFvAd7k8dNsVH8bi9a8cUAUSEcEEgTp2z3dbEDGJGfP6VUnkQnlReg==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.25.8':
    resolution:
      {
        integrity: sha512-yJAVPklM5+4+9dTeKwHOaA+LQkmrKFX96BM0A/2zQrbS6ENCmxc4OVoBs5dPkCCak2roAD+jKCdnmOqKszPkjA==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-YsS2e3Wtgnw7Wq53XXBLcV6JhRsEq8hkfg91ESVadIrzr9wO6jJDMZnCQbHm1Guc5t/CdDiFSSfWP58FNuvT3Q==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-Jw0mxgIaYX6R8ODrdkLLPwBqHTtYHJSmzzd+QeytSugzQ0Vg4c5rDky5VgkoowbZQahCbsv1rT1KW72MPIkevw==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.23.1':
    resolution:
      {
        integrity: sha512-aClqdgTDVPSEGgoCS8QDG37Gu8yc9lTHNAQlsztQ6ENetKEO//b8y31MMu2ZaPbn4kVsIABzVLXYLhCGekGDqw==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.8':
    resolution:
      {
        integrity: sha512-Vh2gLxxHnuoQ+GjPNvDSDRpoBCUzY4Pu0kBqMBDlK4fuWbKgGtmDIeEC081xi26PPjn+1tct+Bh8FjyLlw1Zlg==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-h1k6yS8/pN/NHlMl5+v4XPfikhJulk4G+tKGFIOwURBSFzE8bixw1ebjluLOjfwtLqY0kewfjLSrO6tN2MgIhA==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-YPJ7hDQ9DnNe5vxOm6jaie9QsTwcKedPvizTVlqWG9GBSq+BuyWEDazlGaDTC5NGU4QJd666V0yqCBL2oWKPfA==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.23.1':
    resolution:
      {
        integrity: sha512-lK1eJeyk1ZX8UklqFd/3A60UuZ/6UVfGT2LuGo3Wp4/z7eRTRYY+0xOu2kpClP+vMTi9wKOfXi2vjUpO1Ro76g==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.8':
    resolution:
      {
        integrity: sha512-MmaEXxQRdXNFsRN/KcIimLnSJrk2r5H8v+WVafRWz5xdSVmWLoITZQXcgehI2ZE6gioE6HirAEToM/RvFBeuhw==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-/93bf2yxencYDnItMYV/v116zff6UyTjo4EtEQjUBeGiVpMmffDNUyD9UN2zV+V3LRV3/on4xdZ26NKzn6754g==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-WIgg00ARWv/uYLU7lsuDK00d/hHSfES5BzdWAdAig1ioV5kaFNrtK8EqGcUBJhYqotlUByUKz5Qo6u8tt7iD/w==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.23.1':
    resolution:
      {
        integrity: sha512-CXXkzgn+dXAPs3WBwE+Kvnrf4WECwBdfjfeYHpMeVxWE0EceB6vhWGShs6wi0IYEqMSIzdOF1XjQ/Mkm5d7ZdQ==
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.25.8':
    resolution:
      {
        integrity: sha512-FuzEP9BixzZohl1kLf76KEVOsxtIBFwCaLupVuk4eFVnOZfU+Wsn+x5Ryam7nILV2pkq2TqQM9EZPsOBuMC+kg==
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.23.1':
    resolution:
      {
        integrity: sha512-VTN4EuOHwXEkXzX5nTvVY4s7E/Krz7COC8xkftbbKRYAl96vPiUssGkeMELQMOnLOJ8k3BY1+ZY52tttZnHcXQ==
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.25.8':
    resolution:
      {
        integrity: sha512-A1D9YzRX1i+1AJZuFFUMP1E9fMaYY+GnSQil9Tlw05utlE86EKTUA7RjwHDkEitmLYiFsRd9HwKBPEftNdBfjg==
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.23.1':
    resolution:
      {
        integrity: sha512-Vx09LzEoBa5zDnieH8LSMRToj7ir/Jeq0Gu6qJ/1GcBq9GkfoEAoXvLiW1U9J1qE/Y/Oyaq33w5p2ZWrNNHNEw==
      }
    engines: { node: '>=18' }
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.25.8':
    resolution:
      {
        integrity: sha512-O7k1J/dwHkY1RMVvglFHl1HzutGEFFZ3kNiDMSOyUrB7WcoHGf96Sh+64nTRT26l3GMbCW01Ekh/ThKM5iI7hQ==
      }
    engines: { node: '>=18' }
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.23.1':
    resolution:
      {
        integrity: sha512-nrFzzMQ7W4WRLNUOU5dlWAqa6yVeI0P78WKGUo7lg2HShq/yx+UYkeNSE0SSfSure0SqgnsxPvmAUu/vu0E+3Q==
      }
    engines: { node: '>=18' }
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.8':
    resolution:
      {
        integrity: sha512-uv+dqfRazte3BzfMp8PAQXmdGHQt2oC/y2ovwpTteqrMx2lwaksiFZ/bdkXJC19ttTvNXBuWH53zy/aTj1FgGw==
      }
    engines: { node: '>=18' }
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.23.1':
    resolution:
      {
        integrity: sha512-dKN8fgVqd0vUIjxuJI6P/9SSSe/mB9rvA98CSH2sJnlZ/OCZWO1DJvxj8jvKTfYUdGfcq2dDxoKaC6bHuTlgcw==
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.8':
    resolution:
      {
        integrity: sha512-GyG0KcMi1GBavP5JgAkkstMGyMholMDybAf8wF5A70CALlDM2p/f7YFE7H92eDeH/VBtFJA5MT4nRPDGg4JuzQ==
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.23.1':
    resolution:
      {
        integrity: sha512-5AV4Pzp80fhHL83JM6LoA6pTQVWgB1HovMBsLQ9OZWLDqVY8MVobBXNSmAJi//Csh6tcY7e7Lny2Hg1tElMjIA==
      }
    engines: { node: '>=18' }
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.8':
    resolution:
      {
        integrity: sha512-rAqDYFv3yzMrq7GIcen3XP7TUEG/4LK86LUPMIz6RT8A6pRIDn0sDcvjudVZBiiTcZCY9y2SgYX2lgK3AF+1eg==
      }
    engines: { node: '>=18' }
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.23.1':
    resolution:
      {
        integrity: sha512-9ygs73tuFCe6f6m/Tb+9LtYxWR4c9yg7zjt2cYkjDbDpV/xVn+68cQxMXCjUpYwEkze2RcU/rMnfIXNRFmSoDw==
      }
    engines: { node: '>=18' }
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.25.8':
    resolution:
      {
        integrity: sha512-Xutvh6VjlbcHpsIIbwY8GVRbwoviWT19tFhgdA7DlenLGC/mbc3lBoVb7jxj9Z+eyGqvcnSyIltYUrkKzWqSvg==
      }
    engines: { node: '>=18' }
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.23.1':
    resolution:
      {
        integrity: sha512-EV6+ovTsEXCPAp58g2dD68LxoP/wK5pRvgy0J/HxPGB009omFPv3Yet0HiaqvrIrgPTBuC6wCH1LTOY91EO5hQ==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.25.8':
    resolution:
      {
        integrity: sha512-ASFQhgY4ElXh3nDcOMTkQero4b1lgubskNlhIfJrsH5OKZXDpUAKBlNS0Kx81jwOBp+HCeZqmoJuihTv57/jvQ==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-d1KfruIeohqAi6SA+gENMuObDbEjn22olAR7egqnkCD9DGBG0wsEARotkLgXDu6c4ncgWTZJtN5vcgxzWRMzcw==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.23.1':
    resolution:
      {
        integrity: sha512-aevEkCNu7KlPRpYLjwmdcuNz6bDFiE7Z8XC4CPqExjTvrHugh28QzUXVOZtiYghciKUacNktqxdpymplil1beA==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.8':
    resolution:
      {
        integrity: sha512-nVDCkrvx2ua+XQNyfrujIG38+YGyuy2Ru9kKVNyh5jAys6n+l44tTtToqHjino2My8VAY6Lw9H7RI73XFi66Cg==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-3x37szhLexNA4bXhLrCC/LImN/YtWis6WXr1VESlfVtVeoFJBRINPJ3f0a/6LV8zpikqoUg4hyXw0sFBt5Cr+Q==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-j8HgrDuSJFAujkivSMSfPQSAa5Fxbvk4rgNAS5i3K+r8s1X0p1uOO2Hl2xNsGFppOeHOLAVgYwDVlmxhq5h+SQ==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.23.1':
    resolution:
      {
        integrity: sha512-aY2gMmKmPhxfU+0EdnN+XNtGbjfQgwZj43k8G3fyrDM/UdZww6xrWxmDkuz2eCZchqVeABjV5BpildOrUbBTqA==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.8':
    resolution:
      {
        integrity: sha512-1h8MUAwa0VhNCDp6Af0HToI2TJFAn1uqT9Al6DJVzdIBAd21m/G0Yfc77KDM3uF3T/YaOgQq3qTJHPbTOInaIQ==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-r2nVa5SIK9tSWd0kJd9HCffnDHKchTGikb//9c7HX+r+wHYCpQrSgxhlY6KWV1nFo1l4KFbsMlHk+L6fekLsUg==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.23.1':
    resolution:
      {
        integrity: sha512-RBRT2gqEl0IKQABT4XTj78tpk9v7ehp+mazn2HbUeZl1YMdaGAQqhapjGTCe7uw7y0frDi4gS0uHzhvpFuI1sA==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.25.8':
    resolution:
      {
        integrity: sha512-zUlaP2S12YhQ2UzUfcCuMDHQFJyKABkAjvO5YSndMiIkMimPmxA+BYSBikWgsRpvyxuRnow4nS5NPnf9fpv41w==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.23.1':
    resolution:
      {
        integrity: sha512-4O+gPR5rEBe2FpKOVyiJ7wNDPA8nGzDuJ6gN4okSA1gEOYZ67N8JPk58tkWtdtPeLz7lBnY6I5L3jdsr3S+A6A==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.25.8':
    resolution:
      {
        integrity: sha512-YEGFFWESlPva8hGL+zvj2z/SaK+pH0SwOM0Nc/d+rVnW7GSTFlLBGzZkuSU9kFIGIo8q9X3ucpZhu8PDN5A2sQ==
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.23.1':
    resolution:
      {
        integrity: sha512-BcaL0Vn6QwCwre3Y717nVHZbAa4UBEigzFm6VdsVdT/MbZ38xoj1X9HPkZhbmaBGUD1W8vxAfffbDe8bA6AKnQ==
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.25.8':
    resolution:
      {
        integrity: sha512-hiGgGC6KZ5LZz58OL/+qVVoZiuZlUYlYHNAmczOm7bs2oE1XriPFi5ZHHrS8ACpV5EjySrnoCKmcbQMN+ojnHg==
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.23.1':
    resolution:
      {
        integrity: sha512-BHpFFeslkWrXWyUPnbKm+xYYVYruCinGcftSBaa8zoF9hZO4BcSCFUvHVTtzpIY6YzUnYtuEhZ+C9iEXjxnasg==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.25.8':
    resolution:
      {
        integrity: sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw==
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.4.1':
    resolution:
      {
        integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution:
      {
        integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==
      }
    engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

  '@eslint/eslintrc@2.1.4':
    resolution:
      {
        integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  '@eslint/js@8.57.1':
    resolution:
      {
        integrity: sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  '@faceless-ui/modal@3.0.0-beta.2':
    resolution:
      {
        integrity: sha512-UmXvz7Iw3KMO4Pm3llZczU4uc5pPQDb6rdqwoBvYDFgWvkraOAHKx0HxSZgwqQvqOhn8joEFBfFp6/Do2562ow==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc.0

  '@faceless-ui/scroll-info@2.0.0':
    resolution:
      {
        integrity: sha512-BkyJ9OQ4bzpKjE3UhI8BhcG36ZgfB4run8TmlaR4oMFUbl59dfyarNfjveyimrxIso9RhFEja/AJ5nQmbcR9hw==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@faceless-ui/window-info@3.0.1':
    resolution:
      {
        integrity: sha512-uPjdJYE/j7hqVNelE9CRUNOeXuXDdPxR4DMe+oz3xwyZi2Y4CxsfpfdPTqqwmNAZa1P33O+ZiCyIkBEeNed0kw==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@floating-ui/core@1.6.9':
    resolution:
      {
        integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==
      }

  '@floating-ui/dom@1.6.13':
    resolution:
      {
        integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==
      }

  '@floating-ui/react-dom@2.1.2':
    resolution:
      {
        integrity: sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==
      }
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/react@0.27.3':
    resolution:
      {
        integrity: sha512-CLHnes3ixIFFKVQDdICjel8muhFLOBdQH7fgtHNPY8UbCNqbeKZ262G7K66lGQOUQWWnYocf7ZbUsLJgGfsLHg==
      }
    peerDependencies:
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@floating-ui/utils@0.2.9':
    resolution:
      {
        integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==
      }

  '@formatjs/ecma402-abstract@2.3.2':
    resolution:
      {
        integrity: sha512-6sE5nyvDloULiyOMbOTJEEgWL32w+VHkZQs8S02Lnn8Y/O5aQhjOEXwWzvR7SsBE/exxlSpY2EsWZgqHbtLatg==
      }

  '@formatjs/fast-memoize@2.2.6':
    resolution:
      {
        integrity: sha512-luIXeE2LJbQnnzotY1f2U2m7xuQNj2DA8Vq4ce1BY9ebRZaoPB1+8eZ6nXpLzsxuW5spQxr7LdCg+CApZwkqkw==
      }

  '@formatjs/icu-messageformat-parser@2.11.0':
    resolution:
      {
        integrity: sha512-Hp81uTjjdTk3FLh/dggU5NK7EIsVWc5/ZDWrIldmf2rBuPejuZ13CZ/wpVE2SToyi4EiroPTQ1XJcJuZFIxTtw==
      }

  '@formatjs/icu-skeleton-parser@1.8.12':
    resolution:
      {
        integrity: sha512-QRAY2jC1BomFQHYDMcZtClqHR55EEnB96V7Xbk/UiBodsuFc5kujybzt87+qj1KqmJozFhk6n4KiT1HKwAkcfg==
      }

  '@formatjs/intl-localematcher@0.5.10':
    resolution:
      {
        integrity: sha512-af3qATX+m4Rnd9+wHcjJ4w2ijq+rAVP3CCinJQvFv1kgSu1W6jypUmvleJxcewdxmutM8dmIRZFxO/IQBZmP2Q==
      }

  '@hey-api/client-fetch@0.4.4':
    resolution:
      {
        integrity: sha512-ebh1JjUdMAqes/Rg8OvbjDqGWGNhgHgmPtHlkIOUtj3y2mUXqX2g9sVoI/rSKW/FdADPng/90k5AL7bwT8W2lA==
      }

  '@hey-api/openapi-ts@0.54.4':
    resolution:
      {
        integrity: sha512-Xt5hhzRhixaaeTDV64w94q99cZywBe8aUvT15I+bV7kI/e/RjmUKu//4m0U8y7t9e6FEPCv0xVUnEaJuGuzG5w==
      }
    engines: { node: ^18.0.0 || >=20.0.0 }
    hasBin: true
    peerDependencies:
      typescript: ^5.x

  '@hookform/resolvers@3.10.0':
    resolution:
      {
        integrity: sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==
      }
    peerDependencies:
      react-hook-form: ^7.0.0

  '@humanwhocodes/config-array@0.13.0':
    resolution:
      {
        integrity: sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==
      }
    engines: { node: '>=10.10.0' }
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/module-importer@1.0.1':
    resolution:
      {
        integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==
      }
    engines: { node: '>=12.22' }

  '@humanwhocodes/object-schema@2.0.3':
    resolution:
      {
        integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==
      }
    deprecated: Use @eslint/object-schema instead

  '@img/sharp-darwin-arm64@0.33.5':
    resolution:
      {
        integrity: sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-darwin-arm64@0.34.2':
    resolution:
      {
        integrity: sha512-OfXHZPppddivUJnqyKoi5YVeHRkkNE2zUFT2gbpKxp/JZCFYEYubnMg+gOp6lWfasPrTS+KPosKqdI+ELYVDtg==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-darwin-x64@0.33.5':
    resolution:
      {
        integrity: sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [darwin]

  '@img/sharp-darwin-x64@0.34.2':
    resolution:
      {
        integrity: sha512-dYvWqmjU9VxqXmjEtjmvHnGqF8GrVjM2Epj9rJ6BUIXvk8slvNDJbhGFvIoXzkDhrJC2jUxNLz/GUjjvSzfw+g==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-darwin-arm64@1.0.4':
    resolution:
      {
        integrity: sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==
      }
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    resolution:
      {
        integrity: sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==
      }
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-libvips-darwin-x64@1.0.4':
    resolution:
      {
        integrity: sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==
      }
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-darwin-x64@1.1.0':
    resolution:
      {
        integrity: sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==
      }
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-linux-arm64@1.0.4':
    resolution:
      {
        integrity: sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==
      }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linux-arm64@1.1.0':
    resolution:
      {
        integrity: sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==
      }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linux-arm@1.0.5':
    resolution:
      {
        integrity: sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==
      }
    cpu: [arm]
    os: [linux]

  '@img/sharp-libvips-linux-arm@1.1.0':
    resolution:
      {
        integrity: sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==
      }
    cpu: [arm]
    os: [linux]

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    resolution:
      {
        integrity: sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==
      }
    cpu: [ppc64]
    os: [linux]

  '@img/sharp-libvips-linux-s390x@1.0.4':
    resolution:
      {
        integrity: sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==
      }
    cpu: [s390x]
    os: [linux]

  '@img/sharp-libvips-linux-s390x@1.1.0':
    resolution:
      {
        integrity: sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==
      }
    cpu: [s390x]
    os: [linux]

  '@img/sharp-libvips-linux-x64@1.0.4':
    resolution:
      {
        integrity: sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==
      }
    cpu: [x64]
    os: [linux]

  '@img/sharp-libvips-linux-x64@1.1.0':
    resolution:
      {
        integrity: sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==
      }
    cpu: [x64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    resolution:
      {
        integrity: sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==
      }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    resolution:
      {
        integrity: sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==
      }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    resolution:
      {
        integrity: sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==
      }
    cpu: [x64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    resolution:
      {
        integrity: sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==
      }
    cpu: [x64]
    os: [linux]

  '@img/sharp-linux-arm64@0.33.5':
    resolution:
      {
        integrity: sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linux-arm64@0.34.2':
    resolution:
      {
        integrity: sha512-D8n8wgWmPDakc83LORcfJepdOSN6MvWNzzz2ux0MnIbOqdieRZwVYY32zxVx+IFUT8er5KPcyU3XXsn+GzG/0Q==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linux-arm@0.33.5':
    resolution:
      {
        integrity: sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm]
    os: [linux]

  '@img/sharp-linux-arm@0.34.2':
    resolution:
      {
        integrity: sha512-0DZzkvuEOqQUP9mo2kjjKNok5AmnOr1jB2XYjkaoNRwpAYMDzRmAqUIa1nRi58S2WswqSfPOWLNOr0FDT3H5RQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm]
    os: [linux]

  '@img/sharp-linux-s390x@0.33.5':
    resolution:
      {
        integrity: sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [s390x]
    os: [linux]

  '@img/sharp-linux-s390x@0.34.2':
    resolution:
      {
        integrity: sha512-EGZ1xwhBI7dNISwxjChqBGELCWMGDvmxZXKjQRuqMrakhO8QoMgqCrdjnAqJq/CScxfRn+Bb7suXBElKQpPDiw==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [s390x]
    os: [linux]

  '@img/sharp-linux-x64@0.33.5':
    resolution:
      {
        integrity: sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  '@img/sharp-linux-x64@0.34.2':
    resolution:
      {
        integrity: sha512-sD7J+h5nFLMMmOXYH4DD9UtSNBD05tWSSdWAcEyzqW8Cn5UxXvsHAxmxSesYUsTOBmUnjtxghKDl15EvfqLFbQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  '@img/sharp-linuxmusl-arm64@0.33.5':
    resolution:
      {
        integrity: sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linuxmusl-arm64@0.34.2':
    resolution:
      {
        integrity: sha512-NEE2vQ6wcxYav1/A22OOxoSOGiKnNmDzCYFOZ949xFmrWZOVII1Bp3NqVVpvj+3UeHMFyN5eP/V5hzViQ5CZNA==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linuxmusl-x64@0.33.5':
    resolution:
      {
        integrity: sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  '@img/sharp-linuxmusl-x64@0.34.2':
    resolution:
      {
        integrity: sha512-DOYMrDm5E6/8bm/yQLCWyuDJwUnlevR8xtF8bs+gjZ7cyUNYXiSf/E8Kp0Ss5xasIaXSHzb888V1BE4i1hFhAA==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  '@img/sharp-wasm32@0.33.5':
    resolution:
      {
        integrity: sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [wasm32]

  '@img/sharp-wasm32@0.34.2':
    resolution:
      {
        integrity: sha512-/VI4mdlJ9zkaq53MbIG6rZY+QRN3MLbR6usYlgITEzi4Rpx5S6LFKsycOQjkOGmqTNmkIdLjEvooFKwww6OpdQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [wasm32]

  '@img/sharp-win32-arm64@0.34.2':
    resolution:
      {
        integrity: sha512-cfP/r9FdS63VA5k0xiqaNaEoGxBg9k7uE+RQGzuK9fHt7jib4zAVVseR9LsE4gJcNWgT6APKMNnCcnyOtmSEUQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [win32]

  '@img/sharp-win32-ia32@0.33.5':
    resolution:
      {
        integrity: sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [ia32]
    os: [win32]

  '@img/sharp-win32-ia32@0.34.2':
    resolution:
      {
        integrity: sha512-QLjGGvAbj0X/FXl8n1WbtQ6iVBpWU7JO94u/P2M4a8CFYsvQi4GW2mRy/JqkRx0qpBzaOdKJKw8uc930EX2AHw==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [ia32]
    os: [win32]

  '@img/sharp-win32-x64@0.33.5':
    resolution:
      {
        integrity: sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [win32]

  '@img/sharp-win32-x64@0.34.2':
    resolution:
      {
        integrity: sha512-aUdT6zEYtDKCaxkofmmJDJYGCf0+pJg3eU9/oBuqvEeoB9dKI6ZLc/1iLJCTuJQDO4ptntAlkUmHgGjyuobZbw==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [win32]

  '@isaacs/cliui@8.0.2':
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
      }
    engines: { node: '>=12' }

  '@jridgewell/gen-mapping@0.3.8':
    resolution:
      {
        integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/resolve-uri@3.1.2':
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/set-array@1.2.1':
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution:
      {
        integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==
      }

  '@jridgewell/trace-mapping@0.3.25':
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
      }

  '@jsdevtools/ono@7.1.3':
    resolution:
      {
        integrity: sha512-4JQNk+3mVzK3xh2rqd6RB4J46qUR19azEHBneZyTZM+c456qOrbbM/5xcR8huNCCcbVt7+UmizG6GuUvPvKUYg==
      }

  '@lexical/clipboard@0.28.0':
    resolution:
      {
        integrity: sha512-LYqion+kAwFQJStA37JAEMxTL/m1WlZbotDfM/2WuONmlO0yWxiyRDI18oeCwhBD6LQQd9c3Ccxp9HFwUG1AVw==
      }

  '@lexical/code@0.28.0':
    resolution:
      {
        integrity: sha512-9LOKSWdRhxqAKRq5yveNC21XKtW4h2rmFNTucwMWZ9vLu9xteOHEwZdO1Qv82PFUmgCpAhg6EntmnZu9xD3K7Q==
      }

  '@lexical/devtools-core@0.28.0':
    resolution:
      {
        integrity: sha512-Fk4itAjZ+MqTYXN84aE5RDf+wQX67N5nyo3JVxQTFZGAghx7Ux1xLWHB25zzD0YfjMtJ0NQROAbE3xdecZzxcQ==
      }
    peerDependencies:
      react: '>=17.x'
      react-dom: '>=17.x'

  '@lexical/dragon@0.28.0':
    resolution:
      {
        integrity: sha512-T6T8YaHnhU863ruuqmRHTLUYa8sfg/ArYcrnNGZGfpvvFTfFjpWb/ELOvOWo8N6Y/4fnSLjQ20aXexVW1KcTBQ==
      }

  '@lexical/hashtag@0.28.0':
    resolution:
      {
        integrity: sha512-zcqX9Qna4lj96bAUfwSQSVEhYQ0O5erSjrIhOVqEgeQ5ubz0EvqnnMbbwNHIb2n6jzSwAvpD/3UZJZtolh+zVg==
      }

  '@lexical/headless@0.28.0':
    resolution:
      {
        integrity: sha512-btcaTfw9I/xQ/XYom6iKWgsPecmRawGd/5jOhP7QDtLUp7gxgM7/kiCZFYa8jDJO6j20rXuWTkc81ynVpKvjow==
      }

  '@lexical/history@0.28.0':
    resolution:
      {
        integrity: sha512-CHzDxaGDn6qCFFhU0YKP1B8sgEb++0Ksqsj6BfDL/6TMxoLNQwRQhP3BUNNXl1kvUhxTQZgk3b9MjJZRaFKG9Q==
      }

  '@lexical/html@0.28.0':
    resolution:
      {
        integrity: sha512-ayb0FPxr55Ko99/d9ewbfrApul4L0z+KpU2ZG03im7EvUPVLyIGLx4S0QguMDvQh0Vu+eJ7/EESuonDs5BCe3A==
      }

  '@lexical/link@0.28.0':
    resolution:
      {
        integrity: sha512-T5VKxpOnML5DcXv2lW3Le0vjNlcbdohZjS9f6PAvm6eX8EzBKDpLQCopr1/0KGdlLd1QrzQsykQrdU7ieC4LRg==
      }

  '@lexical/list@0.28.0':
    resolution:
      {
        integrity: sha512-3a8QcZ75n2TLxP+xkSPJ2V15jsysMLMe0YoObG+ew/sioVelIU8GciYsWBo5GgQmwSzJNQJeK5cJ9p1b71z2cg==
      }

  '@lexical/mark@0.28.0':
    resolution:
      {
        integrity: sha512-v5PzmTACsJrw3GvNZy2rgPxrNn9InLvLFoKqrSlNhhyvYNIAcuC4KVy00LKLja43Gw/fuB3QwKohYfAtM3yR3g==
      }

  '@lexical/markdown@0.28.0':
    resolution:
      {
        integrity: sha512-F3JXClqN4cjmXYLDK0IztxkbZuqkqS/AVbxnhGvnDYHQ9Gp8l7BonczhOiPwmJCDubJrAACP0L9LCqyt0jDRFw==
      }

  '@lexical/offset@0.28.0':
    resolution:
      {
        integrity: sha512-/SMDQgBPeWM936t04mtH6UAn3xAjP/meu9q136bcT3S7p7V8ew9JfNp9aznTPTx+2W3brJORAvUow7Xn1fSHmw==
      }

  '@lexical/overflow@0.28.0':
    resolution:
      {
        integrity: sha512-ppmhHXEZVicBm05w9EVflzwFavTVNAe4q0bkabWUeW0IoCT3Vg2A3JT7PC9ypmp+mboUD195foFEr1BBSv1Y8Q==
      }

  '@lexical/plain-text@0.28.0':
    resolution:
      {
        integrity: sha512-Jj2dCMDEfRuVetfDKcUes8J5jvAfZrLnILFlHxnu7y+lC+7R/NR403DYb3NJ8H7+lNiH1K15+U2K7ewbjxS6KQ==
      }

  '@lexical/react@0.28.0':
    resolution:
      {
        integrity: sha512-dWPnxrKrbQFjNqExqnaAsV0UEUgw/5M1ZYRWd5FGBGjHqVTCaX2jNHlKLMA68Od0VPIoOX2Zy1TYZ8ZKtsj5Dg==
      }
    peerDependencies:
      react: '>=17.x'
      react-dom: '>=17.x'

  '@lexical/rich-text@0.28.0':
    resolution:
      {
        integrity: sha512-y+vUWI+9uFupIb9UvssKU/DKcT9dFUZuQBu7utFkLadxCNyXQHeRjxzjzmvFiM3DBV0guPUDGu5VS5TPnIA+OA==
      }

  '@lexical/selection@0.28.0':
    resolution:
      {
        integrity: sha512-AJDi67Nsexyejzp4dEQSVoPov4P+FJ0t1v6DxUU+YmcvV56QyJQi6ue0i/xd8unr75ZufzLsAC0cDJJCEI7QDA==
      }

  '@lexical/table@0.28.0':
    resolution:
      {
        integrity: sha512-HMPCwXdj0sRWdlDzsHcNWRgbeKbEhn3L8LPhFnTq7q61gZ4YW2umdmuvQFKnIBcKq49drTH8cUwZoIwI8+AEEw==
      }

  '@lexical/text@0.28.0':
    resolution:
      {
        integrity: sha512-PT/A2RZv+ktn7SG/tJkOpGlYE6zjOND59VtRHnV/xciZ+jEJVaqAHtWjhbWibAIZQAkv/O7UouuDqzDaNTSGAA==
      }

  '@lexical/utils@0.28.0':
    resolution:
      {
        integrity: sha512-Qw00DjkS1nRK7DLSgqJpJ77Ti2AuiOQ6m5eM38YojoWXkVmoxqKAUMaIbVNVKqjFgrQvKFF46sXxIJPbUQkB0w==
      }

  '@lexical/yjs@0.28.0':
    resolution:
      {
        integrity: sha512-rKHpUEd3nrvMY7ghmOC0AeGSYT7YIviba+JViaOzrCX4/Wtv5C/3Sl7Io12Z9k+s1BKmy7C28bOdQHvRWaD7vQ==
      }
    peerDependencies:
      yjs: '>=13.5.22'

  '@monaco-editor/loader@1.5.0':
    resolution:
      {
        integrity: sha512-hKoGSM+7aAc7eRTRjpqAZucPmoNOC4UUbknb/VNoTkEIkCPhqV8LfbsgM1webRM7S/z21eHEx9Fkwx8Z/C/+Xw==
      }

  '@monaco-editor/react@4.7.0':
    resolution:
      {
        integrity: sha512-cyzXQCtO47ydzxpQtCGSQGOC8Gk3ZUeBXFAxD+CWXYFo5OqZyZUonFl0DwUlTyAfRHntBfw2p3w4s9R6oe1eCA==
      }
    peerDependencies:
      monaco-editor: '>= 0.25.0 < 1'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@mongodb-js/saslprep@1.1.9':
    resolution:
      {
        integrity: sha512-tVkljjeEaAhCqTzajSdgbQ6gE6f3oneVwa3iXR6csiEwXXOFsiC6Uh9iAjAhXPtqa/XMDHWjjeNH/77m/Yq2dw==
      }

  '@next/env@15.3.4':
    resolution:
      {
        integrity: sha512-ZkdYzBseS6UjYzz6ylVKPOK+//zLWvD6Ta+vpoye8cW11AjiQjGYVibF0xuvT4L0iJfAPfZLFidaEzAOywyOAQ==
      }

  '@next/eslint-plugin-next@15.0.0':
    resolution:
      {
        integrity: sha512-UG/Gnsq6Sc4wRhO9qk+vc/2v4OfRXH7GEH6/TGlNF5eU/vI9PIO7q+kgd65X2DxJ+qIpHWpzWwlPLmqMi1FE9A==
      }

  '@next/swc-darwin-arm64@15.3.4':
    resolution:
      {
        integrity: sha512-z0qIYTONmPRbwHWvpyrFXJd5F9YWLCsw3Sjrzj2ZvMYy9NPQMPZ1NjOJh4ojr4oQzcGYwgJKfidzehaNa1BpEg==
      }
    engines: { node: '>= 10' }
    cpu: [arm64]
    os: [darwin]

  '@next/swc-darwin-x64@15.3.4':
    resolution:
      {
        integrity: sha512-Z0FYJM8lritw5Wq+vpHYuCIzIlEMjewG2aRkc3Hi2rcbULknYL/xqfpBL23jQnCSrDUGAo/AEv0Z+s2bff9Zkw==
      }
    engines: { node: '>= 10' }
    cpu: [x64]
    os: [darwin]

  '@next/swc-linux-arm64-gnu@15.3.4':
    resolution:
      {
        integrity: sha512-l8ZQOCCg7adwmsnFm8m5q9eIPAHdaB2F3cxhufYtVo84pymwKuWfpYTKcUiFcutJdp9xGHC+F1Uq3xnFU1B/7g==
      }
    engines: { node: '>= 10' }
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-arm64-musl@15.3.4':
    resolution:
      {
        integrity: sha512-wFyZ7X470YJQtpKot4xCY3gpdn8lE9nTlldG07/kJYexCUpX1piX+MBfZdvulo+t1yADFVEuzFfVHfklfEx8kw==
      }
    engines: { node: '>= 10' }
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-x64-gnu@15.3.4':
    resolution:
      {
        integrity: sha512-gEbH9rv9o7I12qPyvZNVTyP/PWKqOp8clvnoYZQiX800KkqsaJZuOXkWgMa7ANCCh/oEN2ZQheh3yH8/kWPSEg==
      }
    engines: { node: '>= 10' }
    cpu: [x64]
    os: [linux]

  '@next/swc-linux-x64-musl@15.3.4':
    resolution:
      {
        integrity: sha512-Cf8sr0ufuC/nu/yQ76AnarbSAXcwG/wj+1xFPNbyNo8ltA6kw5d5YqO8kQuwVIxk13SBdtgXrNyom3ZosHAy4A==
      }
    engines: { node: '>= 10' }
    cpu: [x64]
    os: [linux]

  '@next/swc-win32-arm64-msvc@15.3.4':
    resolution:
      {
        integrity: sha512-ay5+qADDN3rwRbRpEhTOreOn1OyJIXS60tg9WMYTWCy3fB6rGoyjLVxc4dR9PYjEdR2iDYsaF5h03NA+XuYPQQ==
      }
    engines: { node: '>= 10' }
    cpu: [arm64]
    os: [win32]

  '@next/swc-win32-x64-msvc@15.3.4':
    resolution:
      {
        integrity: sha512-4kDt31Bc9DGyYs41FTL1/kNpDeHyha2TC0j5sRRoKCyrhNcfZ/nRQkAUlF27mETwm8QyHqIjHJitfcza2Iykfg==
      }
    engines: { node: '>= 10' }
    cpu: [x64]
    os: [win32]

  '@nodelib/fs.scandir@2.1.5':
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
      }
    engines: { node: '>= 8' }

  '@nodelib/fs.stat@2.0.5':
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
      }
    engines: { node: '>= 8' }

  '@nodelib/fs.walk@1.2.8':
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
      }
    engines: { node: '>= 8' }

  '@nolyfill/is-core-module@1.0.39':
    resolution:
      {
        integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==
      }
    engines: { node: '>=12.4.0' }

  '@one-ini/wasm@0.1.1':
    resolution:
      {
        integrity: sha512-XuySG1E38YScSJoMlqovLru4KTUNSjgVTIjyh7qMX6aNN5HY5Ct5LhRJdxO79JtTzKfzV/bnWpz+zquYrISsvw==
      }

  '@payloadcms/db-mongodb@3.50.0':
    resolution:
      {
        integrity: sha512-Uhjr37AU86iG0XCURgLkYK6uKQMNSD4jPci8vbh43/JwTMsc+3CXy1sjeYoCHdrO1wLUOwR+zT02WmWz3+F4Ag==
      }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/email-nodemailer@3.50.0':
    resolution:
      {
        integrity: sha512-/cI57DBLyM5/N1rzxWvbpqLKrrx4hAxODaqjSC1Q5okBvmSc7okJzRxSL9yXqziAk+Xd+tzQ+fLzyr23fvT40A==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/email-resend@3.50.0':
    resolution:
      {
        integrity: sha512-EzEeNfZSxm/c+hBdz/SbXXA9lSHetIMEo0qcpjudJSoBH4mzvj0woNsEx6+D7F8fKDpMfd3K1NB68B86r6lOrQ==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/graphql@3.50.0':
    resolution:
      {
        integrity: sha512-BrhtTaEOmnK2fbBOMbr1sbOh0hTI6pl9fZ0Y+PzlS9/2SjjDJBe1xv2v6YjhujklTaU0GaKLs6xFHA8u+ppoAQ==
      }
    hasBin: true
    peerDependencies:
      graphql: ^16.8.1
      payload: 3.50.0

  '@payloadcms/live-preview-react@3.50.0':
    resolution:
      {
        integrity: sha512-LzXD0AOtzXsCq6IUW3Hkzza2I4XClXwWBnHr9cS36q7Xh05zIS1yN/cgf6h1ilykSHDkv0JZEjQviFeQT5+5Og==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/live-preview@3.50.0':
    resolution:
      {
        integrity: sha512-Twux96/MxleN0zDTr5f1DmarRo5igRN5dxQpcME8N7tP/PiXQTqmaRiMY4ayQ3q32gnZIXvcYi8HknrC+eiiMA==
      }

  '@payloadcms/next@3.50.0':
    resolution:
      {
        integrity: sha512-9piyBDLsVW4huCKgas6eorq5PcKM1VoTZ2spWkaLQ9hbWYBspblGVkb3kcMWVGGYSA6Yw4qJlhomAqIY/sVoFA==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      graphql: ^16.8.1
      next: ^15.2.3
      payload: 3.50.0

  '@payloadcms/payload-cloud@3.50.0':
    resolution:
      {
        integrity: sha512-wH8/kOYA/m5zXVJG5PQdxUNao3fb15aVRQUJbNfF8o6VBAZNXXCm9LJghUjcpyo5NGQai5Kjodycx/y+1PqrjA==
      }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/plugin-cloud-storage@3.50.0':
    resolution:
      {
        integrity: sha512-UJjIh8yKsRuAveFV3eegHGuOYwAzYhiVi8bZR9PNWfzD/WFQqwJmlnBM+fpC19ZiWKsPQf4M6g3PxFW2PkkaZw==
      }
    peerDependencies:
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/plugin-form-builder@3.50.0':
    resolution:
      {
        integrity: sha512-DUrXIX+XAcrZi4kSm23KRnDWHLiQAcY2z/FUFs+HRQQoalAjH7iErOjH6b1uwZTEnCIPWkMiqvaGoE5Z7we5LQ==
      }
    peerDependencies:
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/plugin-nested-docs@3.50.0':
    resolution:
      {
        integrity: sha512-d0tbolBtUgb5WAhXhEGN5XQSDDakj1lKpXExrXqR1JJM5tACNfFvwk9OugNsukcjPpSeDB1VZa4173kaKOOcQg==
      }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/plugin-redirects@3.50.0':
    resolution:
      {
        integrity: sha512-G0tMtozeU+XZtifjZNk4o2DrhiLVhNszDKZXZCWQVLLEPGcFyldWi9OerLbno8dXalU1tCR2NmS9qjgh0hG3ag==
      }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/plugin-search@3.50.0':
    resolution:
      {
        integrity: sha512-UI8MPFYNz/SXyTU+f+sxP+y7AxAIvCZxZ3BSfq3MHIsldCtZWia4wMltoR25zgut/nYlff+MVh10J72Frn+m+g==
      }
    peerDependencies:
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/plugin-seo@3.50.0':
    resolution:
      {
        integrity: sha512-fwAd7yrC486o4RKYUfU8EyX40F1CEaRWSQsdsQxRdSmGj+vdypsD0QI8Gw0QXUAqzy/yX5CjGxIkgfq9skcs3A==
      }
    peerDependencies:
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/plugin-stripe@3.50.0':
    resolution:
      {
        integrity: sha512-qbpfwD7C4FZzhboMlSZoQkIONJQPrjl1/j0EPI5ojkyIVbbxEjhstmpQrIytutPpoyx1zc//1yLfrw/g4Kkeog==
      }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/richtext-lexical@3.50.0':
    resolution:
      {
        integrity: sha512-bW3LdTdVvipcaxZL3GxvHky+WEN2x7w+jOkXIHHnYkd+PzgF3aL3mNjHJve9JDdqJF5cHFLq8mFBi9WQfs80FQ==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      '@faceless-ui/modal': 3.0.0-beta.2
      '@faceless-ui/scroll-info': 2.0.0
      '@payloadcms/next': 3.50.0
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@payloadcms/storage-s3@3.50.0':
    resolution:
      {
        integrity: sha512-b37jENtIRG2TWMymthQEGMUeQSTqwqn9T0exnJZuo10D38z4zws72dYO2CLVHSFArtV6DwhpLoWsHQXAJLpX0w==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      payload: 3.50.0

  '@payloadcms/translations@3.50.0':
    resolution:
      {
        integrity: sha512-Po3dnuY+cchLessSB4+r3Al5MF5joWoh7RFuMnOB/mcpBgkoSCP2bV9Th/U3CTc67CmEBArCnRrDtfSdE7eDrA==
      }

  '@payloadcms/ui@3.50.0':
    resolution:
      {
        integrity: sha512-oC9qCu1sPXeUpSCQ63zCOIWrFz9eY5xFXmCyUqEFrUI0xbeHMyVYfYUpIeFQmCR9MwaDiRdEf6CoKoD0Xk1Qog==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    peerDependencies:
      next: ^15.2.3
      payload: 3.50.0
      react: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020
      react-dom: ^19.0.0 || ^19.0.0-rc-65a56d0e-20241020

  '@pkgjs/parseargs@0.11.0':
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==
      }
    engines: { node: '>=14' }

  '@raddix/use-media-query@0.1.3':
    resolution:
      {
        integrity: sha512-a20jnTs50Nw+EL/U0LhsuK8QhG5adVeXMCqamA8Sy0rrL+F6X/lznKpFTqbnAucXT+MFmwm76gVK04qq6/ehoA==
      }
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@radix-ui/number@1.1.0':
    resolution:
      {
        integrity: sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==
      }

  '@radix-ui/primitive@1.1.1':
    resolution:
      {
        integrity: sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==
      }

  '@radix-ui/react-accordion@1.2.3':
    resolution:
      {
        integrity: sha512-RIQ15mrcvqIkDARJeERSuXSry2N8uYnxkdDetpfmalT/+0ntOXLkFOsh9iwlAsCv+qcmhZjbdJogIm6WBa6c4A==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-alert-dialog@1.1.5':
    resolution:
      {
        integrity: sha512-1Y2sI17QzSZP58RjGtrklfSGIf3AF7U/HkD3aAcAnhOUJrm7+7GG1wRDFaUlSe0nW5B/t4mYd/+7RNbP2Wexug==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.1':
    resolution:
      {
        integrity: sha512-NaVpZfmv8SKeZbn4ijN2V3jlHA9ngBG16VnIIm22nUR0Yk8KUALyBxT3KYEUnNuch9sTE8UTsS3whzBgKOL30w==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.2':
    resolution:
      {
        integrity: sha512-G+KcpzXHq24iH0uGG/pF8LyzpFJYGD4RfLjCIBfGdSLXvjLHST31RUiRVrupIBMvIppMgSzQ6l66iAxl03tdlg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-avatar@1.1.2':
    resolution:
      {
        integrity: sha512-GaC7bXQZ5VgZvVvsJ5mu/AEbjYLnhhkoidOboC50Z6FFlLA03wG2ianUoH+zgDQ31/9gCF59bE4+2bBgTyMiig==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-checkbox@1.1.3':
    resolution:
      {
        integrity: sha512-HD7/ocp8f1B3e6OHygH0n7ZKjONkhciy1Nh0yuBgObqThc3oyx+vuMfFHKAknXRHHWVE9XvXStxJFyjUmB8PIw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collapsible@1.1.3':
    resolution:
      {
        integrity: sha512-jFSerheto1X03MUC0g6R7LedNW9EEGWdg9W1+MlpkMLwGkgkbUXLPBH/KIuWKXUoeYRVY11llqbTBDzuLg7qrw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.1':
    resolution:
      {
        integrity: sha512-LwT3pSho9Dljg+wY2KN2mrrh6y3qELfftINERIzBUO9e0N+t0oMTyn3k9iv+ZqgrwGkRnLpNJrsMv9BZlt2yuA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.2':
    resolution:
      {
        integrity: sha512-9z54IEKRxIa9VityapoEYMuByaG42iSy1ZXlY2KcuLSEtq8x4987/N6m15ppoMffgZX72gER2uHe1D9Y6Unlcw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.1':
    resolution:
      {
        integrity: sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.1':
    resolution:
      {
        integrity: sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.5':
    resolution:
      {
        integrity: sha512-LaO3e5h/NOEL4OfXjxD43k9Dx+vn+8n+PCFt6uhX/BADFflllyv3WJG6rgvvSVBxpTch938Qq/LGc2MMxipXPw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.0':
    resolution:
      {
        integrity: sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.4':
    resolution:
      {
        integrity: sha512-XDUI0IVYVSwjMXxM6P4Dfti7AH+Y4oS/TB+sglZ/EXc7cqLwGAmp1NlMrcUjj7ks6R5WTZuWKv44FBbLpwU3sA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.5':
    resolution:
      {
        integrity: sha512-E4TywXY6UsXNRhFrECa5HAvE5/4BFcGyfTyK36gP+pAW1ed7UTK4vKwdr53gAJYwqbfCWC6ATvJa3J3R/9+Qrg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.5':
    resolution:
      {
        integrity: sha512-50ZmEFL1kOuLalPKHrLWvPFMons2fGx9TqQCWlPwDVpbAnaUJ1g4XNcKqFNMQymYU0kKWR4MDDi+9vUQBGFgcQ==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.1':
    resolution:
      {
        integrity: sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.1':
    resolution:
      {
        integrity: sha512-01omzJAYRxXdG2/he/+xy+c8a8gCydoQ1yOxnWNcRhrrBW5W+RQJ22EK1SaO8tb3WoUsuEw7mJjBozPzihDFjA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-scope@1.1.2':
    resolution:
      {
        integrity: sha512-zxwE80FCU7lcXUGWkdt6XpTTCKPitG1XKOwViTxHVKIJhZl9MvIl2dVHeZENCWD9+EdWv05wlaEkRXUykU27RA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.0':
    resolution:
      {
        integrity: sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-label@2.1.1':
    resolution:
      {
        integrity: sha512-UUw5E4e/2+4kFMH7+YxORXGWggtY6sM8WIwh5RZchhLuUg2H1hc98Py+pr8HMz6rdaYrK2t296ZEjYLOCO5uUw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-menu@2.1.5':
    resolution:
      {
        integrity: sha512-uH+3w5heoMJtqVCgYOtYVMECk1TOrkUn0OG0p5MqXC0W2ppcuVeESbou8PTHoqAjbdTEK19AGXBWcEtR5WpEQg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.1':
    resolution:
      {
        integrity: sha512-3kn5Me69L+jv82EKRuQCXdYyf1DqHwD2U/sxoNgBGCB7K9TRc3bQamQ+5EPM9EvyPdli0W41sROd+ZU1dTCztw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.2':
    resolution:
      {
        integrity: sha512-Rvqc3nOpwseCyj/rgjlJDYAgyfw7OC1tTkKn2ivhaMGcYt8FSBlahHOZak2i3QwkRXUXgGgzeEe2RuqeEHuHgA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.3':
    resolution:
      {
        integrity: sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.4':
    resolution:
      {
        integrity: sha512-sn2O9k1rPFYVyKd5LAJfo96JlSGVFpa1fS6UuBJfrZadudiw5tAmru+n1x7aMRQ84qDM71Zh1+SzK5QwU0tJfA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.2':
    resolution:
      {
        integrity: sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.1':
    resolution:
      {
        integrity: sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.2':
    resolution:
      {
        integrity: sha512-Ec/0d38EIuvDF+GZjcMU/Ze6MxntVJYO/fRlCPhCaVUyPY9WTalHJw54tp9sXeJo3tlShWpy41vQRgLRGOuz+w==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-radio-group@1.2.3':
    resolution:
      {
        integrity: sha512-xtCsqt8Rp09FK50ItqEqTJ7Sxanz8EM8dnkVIhJrc/wkMMomSmXHvYbhv3E7Zx4oXh98aaLt9W679SUYXg4IDA==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.1':
    resolution:
      {
        integrity: sha512-QE1RoxPGJ/Nm8Qmk0PxP8ojmoaS67i0s7hVssS7KuI2FQoc/uzVlZsqKfQvxPE6D8hICCPHJ4D88zNhT3OOmkw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.2':
    resolution:
      {
        integrity: sha512-zgMQWkNO169GtGqRvYrzb0Zf8NhMHS2DuEB/TiEmVnpr5OqPU3i8lfbxaAmC2J/KYuIQxyoQQ6DxepyXp61/xw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-select@2.1.6':
    resolution:
      {
        integrity: sha512-T6ajELxRvTuAMWH0YmRJ1qez+x4/7Nq7QIx7zJ0VK3qaEWdnWpNbEDnmWldG1zBDwqrLy5aLMUWcoGirVj5kMg==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.1':
    resolution:
      {
        integrity: sha512-RRiNRSrD8iUiXriq/Y5n4/3iE8HzqgLHsusUSg5jVpU2+3tqcUFPJXHDymwEypunc2sWxDUS3UC+rkZRlHedsw==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.1.1':
    resolution:
      {
        integrity: sha512-RApLLOcINYJA+dMVbOju7MYv1Mb2EBp2nH4HdDzXTSyaR5optlm6Otrz1euW3HbdOR8UmmFK06TD+A9frYWv+g==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-slot@1.1.2':
    resolution:
      {
        integrity: sha512-YAKxaiGsSQJ38VzKH86/BPRC4rh+b1Jpa+JneA5LRE7skmLPNAyeG8kPJj/oo4STLvlrs8vkf/iYyc3A5stYCQ==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-switch@1.1.2':
    resolution:
      {
        integrity: sha512-zGukiWHjEdBCRyXvKR6iXAQG6qXm2esuAD6kDOi9Cn+1X6ev3ASo4+CsYaD6Fov9r/AQFekqnD/7+V0Cs6/98g==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-tabs@1.1.3':
    resolution:
      {
        integrity: sha512-9mFyI30cuRDImbmFF6O2KUJdgEOsGh9Vmx9x/Dh9tOhL7BngmQPQfwW4aejKm5OHpfWIdmeV6ySyuxoOGjtNng==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.0':
    resolution:
      {
        integrity: sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.1.0':
    resolution:
      {
        integrity: sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.0':
    resolution:
      {
        integrity: sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.0':
    resolution:
      {
        integrity: sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-previous@1.1.0':
    resolution:
      {
        integrity: sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.0':
    resolution:
      {
        integrity: sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.0':
    resolution:
      {
        integrity: sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.1.2':
    resolution:
      {
        integrity: sha512-1SzA4ns2M1aRlvxErqhLHsBHoS5eI5UUcI2awAMgGUp4LoaoWOKYmvqDY2s/tltuPkh3Yk77YF/r3IRj+Amx4Q==
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.0':
    resolution:
      {
        integrity: sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==
      }

  '@react-email/body@0.0.11':
    resolution:
      {
        integrity: sha512-ZSD2SxVSgUjHGrB0Wi+4tu3MEpB4fYSbezsFNEJk2xCWDBkFiOeEsjTmR5dvi+CxTK691hQTQlHv0XWuP7ENTg==
      }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/button@0.0.19':
    resolution:
      {
        integrity: sha512-HYHrhyVGt7rdM/ls6FuuD6XE7fa7bjZTJqB2byn6/oGsfiEZaogY77OtoLL/mrQHjHjZiJadtAMSik9XLcm7+A==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/code-block@0.0.11':
    resolution:
      {
        integrity: sha512-4D43p+LIMjDzm66gTDrZch0Flkip5je91mAT7iGs6+SbPyalHgIA+lFQoQwhz/VzHHLxuD0LV6gwmU/WUQ2WEg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/code-inline@0.0.5':
    resolution:
      {
        integrity: sha512-MmAsOzdJpzsnY2cZoPHFPk6uDO/Ncpb4Kh1hAt9UZc1xOW3fIzpe1Pi9y9p6wwUmpaeeDalJxAxH6/fnTquinA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/column@0.0.13':
    resolution:
      {
        integrity: sha512-Lqq17l7ShzJG/d3b1w/+lVO+gp2FM05ZUo/nW0rjxB8xBICXOVv6PqjDnn3FXKssvhO5qAV20lHM6S+spRhEwQ==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/components@0.0.31':
    resolution:
      {
        integrity: sha512-rQsTY9ajobncix9raexhBjC7O6cXUMc87eNez2gnB1FwtkUO8DqWZcktbtwOJi7GKmuAPTx0o/IOFtiBNXziKA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/container@0.0.15':
    resolution:
      {
        integrity: sha512-Qo2IQo0ru2kZq47REmHW3iXjAQaKu4tpeq/M8m1zHIVwKduL2vYOBQWbC2oDnMtWPmkBjej6XxgtZByxM6cCFg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/font@0.0.9':
    resolution:
      {
        integrity: sha512-4zjq23oT9APXkerqeslPH3OZWuh5X4crHK6nx82mVHV2SrLba8+8dPEnWbaACWTNjOCbcLIzaC9unk7Wq2MIXw==
      }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/head@0.0.12':
    resolution:
      {
        integrity: sha512-X2Ii6dDFMF+D4niNwMAHbTkeCjlYYnMsd7edXOsi0JByxt9wNyZ9EnhFiBoQdqkE+SMDcu8TlNNttMrf5sJeMA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/heading@0.0.15':
    resolution:
      {
        integrity: sha512-xF2GqsvBrp/HbRHWEfOgSfRFX+Q8I5KBEIG5+Lv3Vb2R/NYr0s8A5JhHHGf2pWBMJdbP4B2WHgj/VUrhy8dkIg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/hr@0.0.11':
    resolution:
      {
        integrity: sha512-S1gZHVhwOsd1Iad5IFhpfICwNPMGPJidG/Uysy1AwmspyoAP5a4Iw3OWEpINFdgh9MHladbxcLKO2AJO+cA9Lw==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/html@0.0.11':
    resolution:
      {
        integrity: sha512-qJhbOQy5VW5qzU74AimjAR9FRFQfrMa7dn4gkEXKMB/S9xZN8e1yC1uA9C15jkXI/PzmJ0muDIWmFwatm5/+VA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/img@0.0.11':
    resolution:
      {
        integrity: sha512-aGc8Y6U5C3igoMaqAJKsCpkbm1XjguQ09Acd+YcTKwjnC2+0w3yGUJkjWB2vTx4tN8dCqQCXO8FmdJpMfOA9EQ==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/link@0.0.12':
    resolution:
      {
        integrity: sha512-vF+xxQk2fGS1CN7UPQDbzvcBGfffr+GjTPNiWM38fhBfsLv6A/YUfaqxWlmL7zLzVmo0K2cvvV9wxlSyNba1aQ==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/markdown@0.0.14':
    resolution:
      {
        integrity: sha512-5IsobCyPkb4XwnQO8uFfGcNOxnsg3311GRXhJ3uKv51P7Jxme4ycC/MITnwIZ10w2zx7HIyTiqVzTj4XbuIHbg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/preview@0.0.12':
    resolution:
      {
        integrity: sha512-g/H5fa9PQPDK6WUEG7iTlC19sAktI23qyoiJtMLqQiXFCfWeQMhqjLGKeLSKkfzszqmfJCjZtpSiKtBoOdxp3Q==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/render@1.0.1':
    resolution:
      {
        integrity: sha512-W3gTrcmLOVYnG80QuUp22ReIT/xfLsVJ+n7ghSlG2BITB8evNABn1AO2rGQoXuK84zKtDAlxCdm3hRyIpZdGSA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/render@1.0.3':
    resolution:
      {
        integrity: sha512-VQ8g4SuIq/jWdfBTdTjb7B8Np0jj+OoD7VebfdHhLTZzVQKesR2aigpYqE/ZXmwj4juVxDm8T2b6WIIu48rPCg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/row@0.0.12':
    resolution:
      {
        integrity: sha512-HkCdnEjvK3o+n0y0tZKXYhIXUNPDx+2vq1dJTmqappVHXS5tXS6W5JOPZr5j+eoZ8gY3PShI2LWj5rWF7ZEtIQ==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/section@0.0.16':
    resolution:
      {
        integrity: sha512-FjqF9xQ8FoeUZYKSdt8sMIKvoT9XF8BrzhT3xiFKdEMwYNbsDflcjfErJe3jb7Wj/es/lKTbV5QR1dnLzGpL3w==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/tailwind@1.0.4':
    resolution:
      {
        integrity: sha512-tJdcusncdqgvTUYZIuhNC6LYTfL9vNTSQpwWdTCQhQ1lsrNCEE4OKCSdzSV3S9F32pi0i0xQ+YPJHKIzGjdTSA==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-email/text@0.0.11':
    resolution:
      {
        integrity: sha512-a7nl/2KLpRHOYx75YbYZpWspUbX1DFY7JIZbOv5x0QU8SvwDbJt+Hm01vG34PffFyYvHEXrc6Qnip2RTjljNjg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc

  '@react-pdf/fns@3.1.2':
    resolution:
      {
        integrity: sha512-qTKGUf0iAMGg2+OsUcp9ffKnKi41RukM/zYIWMDJ4hRVYSr89Q7e3wSDW/Koqx3ea3Uy/z3h2y3wPX6Bdfxk6g==
      }

  '@react-pdf/font@4.0.2':
    resolution:
      {
        integrity: sha512-/dAWu7Y2RD1RxarDZ9SkYPHgBYOhmcDnet4W/qN/m8k+A2Hr3ja54GymSR7GGxWBtxjKtNauVKrTa9LS1n8WUw==
      }

  '@react-pdf/image@3.0.3':
    resolution:
      {
        integrity: sha512-lvP5ryzYM3wpbO9bvqLZYwEr5XBDX9jcaRICvtnoRqdJOo7PRrMnmB4MMScyb+Xw10mGeIubZAAomNAG5ONQZQ==
      }

  '@react-pdf/layout@4.4.0':
    resolution:
      {
        integrity: sha512-Aq+Cc6JYausWLoks2FvHe3PwK9cTuvksB2uJ0AnkKJEUtQbvCq8eCRb1bjbbwIji9OzFRTTzZij7LzkpKHjIeA==
      }

  '@react-pdf/pdfkit@4.0.3':
    resolution:
      {
        integrity: sha512-k+Lsuq8vTwWsCqTp+CCB4+2N+sOTFrzwGA7aw3H9ix/PDWR9QksbmNg0YkzGbLAPI6CeawmiLHcf4trZ5ecLPQ==
      }

  '@react-pdf/png-js@3.0.0':
    resolution:
      {
        integrity: sha512-eSJnEItZ37WPt6Qv5pncQDxLJRK15eaRwPT+gZoujP548CodenOVp49GST8XJvKMFt9YqIBzGBV/j9AgrOQzVA==
      }

  '@react-pdf/primitives@4.1.1':
    resolution:
      {
        integrity: sha512-IuhxYls1luJb7NUWy6q5avb1XrNaVj9bTNI40U9qGRuS6n7Hje/8H8Qi99Z9UKFV74bBP3DOf3L1wV2qZVgVrQ==
      }

  '@react-pdf/reconciler@1.1.4':
    resolution:
      {
        integrity: sha512-oTQDiR/t4Z/Guxac88IavpU2UgN7eR0RMI9DRKvKnvPz2DUasGjXfChAdMqDNmJJxxV26mMy9xQOUV2UU5/okg==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@react-pdf/render@4.3.0':
    resolution:
      {
        integrity: sha512-MdWfWaqO6d7SZD75TZ2z5L35V+cHpyA43YNRlJNG0RJ7/MeVGDQv12y/BXOJgonZKkeEGdzM3EpAt9/g4E22WA==
      }

  '@react-pdf/renderer@4.3.0':
    resolution:
      {
        integrity: sha512-28gpA69fU9ZQrDzmd5xMJa1bDf8t0PT3ApUKBl2PUpoE/x4JlvCB5X66nMXrfFrgF2EZrA72zWQAkvbg7TE8zw==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@react-pdf/stylesheet@6.1.0':
    resolution:
      {
        integrity: sha512-BGZ2sYNUp38VJUegjva/jsri3iiRGnVNjWI+G9dTwAvLNOmwFvSJzqaCsEnqQ/DW5mrTBk/577FhDY7pv6AidA==
      }

  '@react-pdf/textkit@6.0.0':
    resolution:
      {
        integrity: sha512-fDt19KWaJRK/n2AaFoVm31hgGmpygmTV7LsHGJNGZkgzXcFyLsx+XUl63DTDPH3iqxj3xUX128t104GtOz8tTw==
      }

  '@react-pdf/types@2.9.0':
    resolution:
      {
        integrity: sha512-ckj80vZLlvl9oYrQ4tovEaqKWP3O06Eb1D48/jQWbdwz1Yh7Y9v1cEmwlP8ET+a1Whp8xfdM0xduMexkuPANCQ==
      }

  '@resvg/resvg-wasm@2.6.2':
    resolution:
      {
        integrity: sha512-FqALmHI8D4o6lk/LRWDnhw95z5eO+eAa6ORjVg09YRR7BkcM6oPHU9uyC0gtQG5vpFLvgpeU4+zEAz2H8APHNw==
      }
    engines: { node: '>= 10' }

  '@rtsao/scc@1.1.0':
    resolution:
      {
        integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==
      }

  '@rushstack/eslint-patch@1.10.5':
    resolution:
      {
        integrity: sha512-kkKUDVlII2DQiKy7UstOR1ErJP8kUKAQ4oa+SQtM0K+lPdmmjj0YnnxBgtTVYH7mUKtbsxeFC9y0AmK7Yb78/A==
      }

  '@selderee/plugin-htmlparser2@0.11.0':
    resolution:
      {
        integrity: sha512-P33hHGdldxGabLFjPPpaTxVolMrzrcegejx+0GxjrIb9Zv48D8yAIA/QTDR2dFl7Uz7urX8aX6+5bCZslr+gWQ==
      }

  '@shuding/opentype.js@1.4.0-beta.0':
    resolution:
      {
        integrity: sha512-3NgmNyH3l/Hv6EvsWJbsvpcpUba6R8IREQ83nH83cyakCw7uM1arZKNfHwv1Wz6jgqrF/j4x5ELvR6PnK9nTcA==
      }
    engines: { node: '>= 8.0.0' }
    hasBin: true

  '@smithy/abort-controller@4.0.4':
    resolution:
      {
        integrity: sha512-gJnEjZMvigPDQWHrW3oPrFhQtkrgqBkyjj3pCIdF3A5M6vsZODG93KNlfJprv6bp4245bdT32fsHK4kkH3KYDA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/chunked-blob-reader-native@4.0.0':
    resolution:
      {
        integrity: sha512-R9wM2yPmfEMsUmlMlIgSzOyICs0x9uu7UTHoccMyt7BWw8shcGM8HqB355+BZCPBcySvbTYMs62EgEQkNxz2ig==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/chunked-blob-reader@5.0.0':
    resolution:
      {
        integrity: sha512-+sKqDBQqb036hh4NPaUiEkYFkTUGYzRsn3EuFhyfQfMy6oGHEUJDurLP9Ufb5dasr/XiAmPNMr6wa9afjQB+Gw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/config-resolver@4.0.1':
    resolution:
      {
        integrity: sha512-Igfg8lKu3dRVkTSEm98QpZUvKEOa71jDX4vKRcvJVyRc3UgN3j7vFMf0s7xLQhYmKa8kyJGQgUJDOV5V3neVlQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/core@3.7.1':
    resolution:
      {
        integrity: sha512-ExRCsHnXFtBPnM7MkfKBPcBBdHw1h/QS/cbNw4ho95qnyNHvnpmGbR39MIAv9KggTr5qSPxRSEL+hRXlyGyGQw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/credential-provider-imds@4.0.1':
    resolution:
      {
        integrity: sha512-l/qdInaDq1Zpznpmev/+52QomsJNZ3JkTl5yrTl02V6NBgJOQ4LY0SFw/8zsMwj3tLe8vqiIuwF6nxaEwgf6mg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/eventstream-codec@4.0.1':
    resolution:
      {
        integrity: sha512-Q2bCAAR6zXNVtJgifsU16ZjKGqdw/DyecKNgIgi7dlqw04fqDu0mnq+JmGphqheypVc64CYq3azSuCpAdFk2+A==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/eventstream-serde-browser@4.0.1':
    resolution:
      {
        integrity: sha512-HbIybmz5rhNg+zxKiyVAnvdM3vkzjE6ccrJ620iPL8IXcJEntd3hnBl+ktMwIy12Te/kyrSbUb8UCdnUT4QEdA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/eventstream-serde-config-resolver@4.0.1':
    resolution:
      {
        integrity: sha512-lSipaiq3rmHguHa3QFF4YcCM3VJOrY9oq2sow3qlhFY+nBSTF/nrO82MUQRPrxHQXA58J5G1UnU2WuJfi465BA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/eventstream-serde-node@4.0.1':
    resolution:
      {
        integrity: sha512-o4CoOI6oYGYJ4zXo34U8X9szDe3oGjmHgsMGiZM0j4vtNoT+h80TLnkUcrLZR3+E6HIxqW+G+9WHAVfl0GXK0Q==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/eventstream-serde-universal@4.0.1':
    resolution:
      {
        integrity: sha512-Z94uZp0tGJuxds3iEAZBqGU2QiaBHP4YytLUjwZWx+oUeohCsLyUm33yp4MMBmhkuPqSbQCXq5hDet6JGUgHWA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/fetch-http-handler@5.1.0':
    resolution:
      {
        integrity: sha512-mADw7MS0bYe2OGKkHYMaqarOXuDwRbO6ArD91XhHcl2ynjGCFF+hvqf0LyQcYxkA1zaWjefSkU7Ne9mqgApSgQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/hash-blob-browser@4.0.1':
    resolution:
      {
        integrity: sha512-rkFIrQOKZGS6i1D3gKJ8skJ0RlXqDvb1IyAphksaFOMzkn3v3I1eJ8m7OkLj0jf1McP63rcCEoLlkAn/HjcTRw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/hash-node@4.0.1':
    resolution:
      {
        integrity: sha512-TJ6oZS+3r2Xu4emVse1YPB3Dq3d8RkZDKcPr71Nj/lJsdAP1c7oFzYqEn1IBc915TsgLl2xIJNuxCz+gLbLE0w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/hash-stream-node@4.0.1':
    resolution:
      {
        integrity: sha512-U1rAE1fxmReCIr6D2o/4ROqAQX+GffZpyMt3d7njtGDr2pUNmAKRWa49gsNVhCh2vVAuf3wXzWwNr2YN8PAXIw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/invalid-dependency@4.0.1':
    resolution:
      {
        integrity: sha512-gdudFPf4QRQ5pzj7HEnu6FhKRi61BfH/Gk5Yf6O0KiSbr1LlVhgjThcvjdu658VE6Nve8vaIWB8/fodmS1rBPQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/is-array-buffer@2.2.0':
    resolution:
      {
        integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==
      }
    engines: { node: '>=14.0.0' }

  '@smithy/is-array-buffer@4.0.0':
    resolution:
      {
        integrity: sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/md5-js@4.0.1':
    resolution:
      {
        integrity: sha512-HLZ647L27APi6zXkZlzSFZIjpo8po45YiyjMGJZM3gyDY8n7dPGdmxIIljLm4gPt/7rRvutLTTkYJpZVfG5r+A==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/middleware-content-length@4.0.1':
    resolution:
      {
        integrity: sha512-OGXo7w5EkB5pPiac7KNzVtfCW2vKBTZNuCctn++TTSOMpe6RZO/n6WEC1AxJINn3+vWLKW49uad3lo/u0WJ9oQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/middleware-endpoint@4.1.16':
    resolution:
      {
        integrity: sha512-plpa50PIGLqzMR2ANKAw2yOW5YKS626KYKqae3atwucbz4Ve4uQ9K9BEZxDLIFmCu7hKLcrq2zmj4a+PfmUV5w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/middleware-retry@4.0.4':
    resolution:
      {
        integrity: sha512-wmxyUBGHaYUqul0wZiset4M39SMtDBOtUr2KpDuftKNN74Do9Y36Go6Eqzj9tL0mIPpr31ulB5UUtxcsCeGXsQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/middleware-serde@4.0.8':
    resolution:
      {
        integrity: sha512-iSSl7HJoJaGyMIoNn2B7czghOVwJ9nD7TMvLhMWeSB5vt0TnEYyRRqPJu/TqW76WScaNvYYB8nRoiBHR9S1Ddw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/middleware-stack@4.0.4':
    resolution:
      {
        integrity: sha512-kagK5ggDrBUCCzI93ft6DjteNSfY8Ulr83UtySog/h09lTIOAJ/xUSObutanlPT0nhoHAkpmW9V5K8oPyLh+QA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/node-config-provider@4.1.3':
    resolution:
      {
        integrity: sha512-HGHQr2s59qaU1lrVH6MbLlmOBxadtzTsoO4c+bF5asdgVik3I8o7JIOzoeqWc5MjVa+vD36/LWE0iXKpNqooRw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/node-http-handler@4.1.0':
    resolution:
      {
        integrity: sha512-vqfSiHz2v8b3TTTrdXi03vNz1KLYYS3bhHCDv36FYDqxT7jvTll1mMnCrkD+gOvgwybuunh/2VmvOMqwBegxEg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/property-provider@4.0.4':
    resolution:
      {
        integrity: sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/protocol-http@5.1.2':
    resolution:
      {
        integrity: sha512-rOG5cNLBXovxIrICSBm95dLqzfvxjEmuZx4KK3hWwPFHGdW3lxY0fZNXfv2zebfRO7sJZ5pKJYHScsqopeIWtQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/querystring-builder@4.0.4':
    resolution:
      {
        integrity: sha512-SwREZcDnEYoh9tLNgMbpop+UTGq44Hl9tdj3rf+yeLcfH7+J8OXEBaMc2kDxtyRHu8BhSg9ADEx0gFHvpJgU8w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/querystring-parser@4.0.4':
    resolution:
      {
        integrity: sha512-6yZf53i/qB8gRHH/l2ZwUG5xgkPgQF15/KxH0DdXMDHjesA9MeZje/853ifkSY0x4m5S+dfDZ+c4x439PF0M2w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/service-error-classification@4.0.1':
    resolution:
      {
        integrity: sha512-3JNjBfOWpj/mYfjXJHB4Txc/7E4LVq32bwzE7m28GN79+M1f76XHflUaSUkhOriprPDzev9cX/M+dEB80DNDKA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/shared-ini-file-loader@4.0.4':
    resolution:
      {
        integrity: sha512-63X0260LoFBjrHifPDs+nM9tV0VMkOTl4JRMYNuKh/f5PauSjowTfvF3LogfkWdcPoxsA9UjqEOgjeYIbhb7Nw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/signature-v4@5.1.2':
    resolution:
      {
        integrity: sha512-d3+U/VpX7a60seHziWnVZOHuEgJlclufjkS6zhXvxcJgkJq4UWdH5eOBLzHRMx6gXjsdT9h6lfpmLzbrdupHgQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/smithy-client@4.4.8':
    resolution:
      {
        integrity: sha512-pcW691/lx7V54gE+dDGC26nxz8nrvnvRSCJaIYD6XLPpOInEZeKdV/SpSux+wqeQ4Ine7LJQu8uxMvobTIBK0w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/types@4.3.1':
    resolution:
      {
        integrity: sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/url-parser@4.0.4':
    resolution:
      {
        integrity: sha512-eMkc144MuN7B0TDA4U2fKs+BqczVbk3W+qIvcoCY6D1JY3hnAdCuhCZODC+GAeaxj0p6Jroz4+XMUn3PCxQQeQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-base64@4.0.0':
    resolution:
      {
        integrity: sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-body-length-browser@4.0.0':
    resolution:
      {
        integrity: sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-body-length-node@4.0.0':
    resolution:
      {
        integrity: sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-buffer-from@2.2.0':
    resolution:
      {
        integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==
      }
    engines: { node: '>=14.0.0' }

  '@smithy/util-buffer-from@4.0.0':
    resolution:
      {
        integrity: sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-config-provider@4.0.0':
    resolution:
      {
        integrity: sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-defaults-mode-browser@4.0.4':
    resolution:
      {
        integrity: sha512-Ej1bV5sbrIfH++KnWxjjzFNq9nyP3RIUq2c9Iqq7SmMO/idUR24sqvKH2LUQFTSPy/K7G4sB2m8n7YYlEAfZaw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-defaults-mode-node@4.0.4':
    resolution:
      {
        integrity: sha512-HE1I7gxa6yP7ZgXPCFfZSDmVmMtY7SHqzFF55gM/GPegzZKaQWZZ+nYn9C2Cc3JltCMyWe63VPR3tSFDEvuGjw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-endpoints@3.0.1':
    resolution:
      {
        integrity: sha512-zVdUENQpdtn9jbpD9SCFK4+aSiavRb9BxEtw9ZGUR1TYo6bBHbIoi7VkrFQ0/RwZlzx0wRBaRmPclj8iAoJCLA==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-hex-encoding@4.0.0':
    resolution:
      {
        integrity: sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-middleware@4.0.4':
    resolution:
      {
        integrity: sha512-9MLKmkBmf4PRb0ONJikCbCwORACcil6gUWojwARCClT7RmLzF04hUR4WdRprIXal7XVyrddadYNfp2eF3nrvtQ==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-retry@4.0.1':
    resolution:
      {
        integrity: sha512-WmRHqNVwn3kI3rKk1LsKcVgPBG6iLTBGC1iYOV3GQegwJ3E8yjzHytPt26VNzOWr1qu0xE03nK0Ug8S7T7oufw==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-stream@4.2.3':
    resolution:
      {
        integrity: sha512-cQn412DWHHFNKrQfbHY8vSFI3nTROY1aIKji9N0tpp8gUABRilr7wdf8fqBbSlXresobM+tQFNk6I+0LXK/YZg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-uri-escape@4.0.0':
    resolution:
      {
        integrity: sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-utf8@2.3.0':
    resolution:
      {
        integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==
      }
    engines: { node: '>=14.0.0' }

  '@smithy/util-utf8@4.0.0':
    resolution:
      {
        integrity: sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==
      }
    engines: { node: '>=18.0.0' }

  '@smithy/util-waiter@4.0.2':
    resolution:
      {
        integrity: sha512-piUTHyp2Axx3p/kc2CIJkYSv0BAaheBQmbACZgQSSfWUumWNW+R1lL+H9PDBxKJkvOeEX+hKYEFiwO8xagL8AQ==
      }
    engines: { node: '>=18.0.0' }

  '@stripe/react-stripe-js@3.7.0':
    resolution:
      {
        integrity: sha512-PYls/2S9l0FF+2n0wHaEJsEU8x7CmBagiH7zYOsxbBlLIHEsqUIQ4MlIAbV9Zg6xwT8jlYdlRIyBTHmO3yM7kQ==
      }
    peerDependencies:
      '@stripe/stripe-js': '>=1.44.1 <8.0.0'
      react: '>=16.8.0 <20.0.0'
      react-dom: '>=16.8.0 <20.0.0'

  '@stripe/stripe-js@7.5.0':
    resolution:
      {
        integrity: sha512-Cq3KKe+G1o7PSBMbmrgpT2JgBeyH2THHr3RdIX2MqF7AnBuspIMgtZ3ktcCgP7kZsTMvnmWymr7zZCT1zeWbMw==
      }
    engines: { node: '>=12.16' }

  '@swc/counter@0.1.3':
    resolution:
      {
        integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==
      }

  '@swc/helpers@0.5.13':
    resolution:
      {
        integrity: sha512-UoKGxQ3r5kYI9dALKJapMmuK+1zWM/H17Z1+iwnNmzcJRnfFuevZs375TA5rW31pu4BS4NoSy1fRsexDXfWn5w==
      }

  '@swc/helpers@0.5.15':
    resolution:
      {
        integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
      }

  '@tanstack/eslint-plugin-query@5.65.0':
    resolution:
      {
        integrity: sha512-UpPtXcAZPHcoEJY8KFcBg/iS99DYGRfYwKo7BwTIJP0lfrdGEL5r2B8YyYERdXBYZfZUQuLgRdU++pD7dltx3g==
      }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0

  '@tanstack/query-core@5.65.0':
    resolution:
      {
        integrity: sha512-Bnnq/1axf00r2grRT6gUyIkZRKzhHs+p4DijrCQ3wMlA3D3TTT71gtaSLtqnzGddj73/7X5JDGyjiSLdjvQN4w==
      }

  '@tanstack/query-devtools@5.65.0':
    resolution:
      {
        integrity: sha512-g5y7zc07U9D3esMdqUfTEVu9kMHoIaVBsD0+M3LPdAdD710RpTcLiNvJY1JkYXqkq9+NV+CQoemVNpQPBXVsJg==
      }

  '@tanstack/react-query-devtools@5.65.1':
    resolution:
      {
        integrity: sha512-PKUBz7+FAP3eI1zoWrP5vxNQXs+elPz3u/3cILGhNZl2dufgbU9OJRpbC+BAptLXTsGxTwkAlrWBIZbD/c7CDw==
      }
    peerDependencies:
      '@tanstack/react-query': ^5.65.1
      react: ^18 || ^19

  '@tanstack/react-query@5.65.1':
    resolution:
      {
        integrity: sha512-BSpjo4RQdJ75Mw3pqM1AJYNhanNxJE3ct7RmCZUAv9cUJg/Qmonzc/Xy2kKXeQA1InuKATSuc6pOZciWOF8TYQ==
      }
    peerDependencies:
      react: ^18 || ^19

  '@tokenizer/token@0.3.0':
    resolution:
      {
        integrity: sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==
      }

  '@trivago/prettier-plugin-sort-imports@4.3.0':
    resolution:
      {
        integrity: sha512-r3n0onD3BTOVUNPhR4lhVK4/pABGpbA7bW3eumZnYdKaHkf1qEC+Mag6DPbGNuuh0eG8AaYj+YqmVHSiGslaTQ==
      }
    peerDependencies:
      '@vue/compiler-sfc': 3.x
      prettier: 2.x - 3.x
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true

  '@types/acorn@4.0.6':
    resolution:
      {
        integrity: sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==
      }

  '@types/busboy@1.5.4':
    resolution:
      {
        integrity: sha512-kG7WrUuAKK0NoyxfQHsVE6j1m01s6kMma64E+OZenQABMQyTJop1DumUWcLwAQ2JzpefU7PDYoRDKl8uZosFjw==
      }

  '@types/debug@4.1.12':
    resolution:
      {
        integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==
      }

  '@types/estree-jsx@1.0.5':
    resolution:
      {
        integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==
      }

  '@types/estree@1.0.6':
    resolution:
      {
        integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==
      }

  '@types/hast@3.0.4':
    resolution:
      {
        integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==
      }

  '@types/json-schema@7.0.15':
    resolution:
      {
        integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==
      }

  '@types/json5@0.0.29':
    resolution:
      {
        integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==
      }

  '@types/lodash@4.17.15':
    resolution:
      {
        integrity: sha512-w/P33JFeySuhN6JLkysYUK2gEmy9kHHFN7E8ro0tkfmlDOgxBDzWEZ/J8cWA+fHqFevpswDTFZnDx+R9lbL6xw==
      }

  '@types/mdast@4.0.4':
    resolution:
      {
        integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==
      }

  '@types/ms@2.1.0':
    resolution:
      {
        integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==
      }

  '@types/node@22.12.0':
    resolution:
      {
        integrity: sha512-Fll2FZ1riMjNmlmJOdAyY5pUbkftXslB5DgEzlIuNaiWhXd00FhWxVC/r4yV/4wBb9JfImTu+jiSvXTkJ7F/gA==
      }

  '@types/normalize-package-data@2.4.4':
    resolution:
      {
        integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==
      }

  '@types/parse-json@4.0.2':
    resolution:
      {
        integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==
      }

  '@types/react-transition-group@4.4.12':
    resolution:
      {
        integrity: sha512-8TV6R3h2j7a91c+1DXdJi3Syo69zzIZbz7Lg5tORM5LEJG7X/E6a1V3drRyBRZq7/utz7A+c4OgYLiLcYGHG6w==
      }
    peerDependencies:
      '@types/react': '*'

  '@types/react@19.0.8':
    resolution:
      {
        integrity: sha512-9P/o1IGdfmQxrujGbIMDyYaaCykhLKc0NGCtYcECNUr9UAaDe4gwvV9bR6tvd5Br1SG0j+PBpbKr2UYY8CwqSw==
      }

  '@types/unist@2.0.11':
    resolution:
      {
        integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==
      }

  '@types/unist@3.0.3':
    resolution:
      {
        integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==
      }

  '@types/uuid@10.0.0':
    resolution:
      {
        integrity: sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==
      }

  '@types/webidl-conversions@7.0.3':
    resolution:
      {
        integrity: sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==
      }

  '@types/whatwg-url@11.0.5':
    resolution:
      {
        integrity: sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==
      }

  '@typescript-eslint/eslint-plugin@8.22.0':
    resolution:
      {
        integrity: sha512-4Uta6REnz/xEJMvwf72wdUnC3rr4jAQf5jnTkeRQ9b6soxLxhDEbS/pfMPoJLDfFPNVRdryqWUIV/2GZzDJFZw==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/parser@8.22.0':
    resolution:
      {
        integrity: sha512-MqtmbdNEdoNxTPzpWiWnqNac54h8JDAmkWtJExBVVnSrSmi9z+sZUt0LfKqk9rjqmKOIeRhO4fHHJ1nQIjduIQ==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/scope-manager@8.22.0':
    resolution:
      {
        integrity: sha512-/lwVV0UYgkj7wPSw0o8URy6YI64QmcOdwHuGuxWIYznO6d45ER0wXUbksr9pYdViAofpUCNJx/tAzNukgvaaiQ==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@typescript-eslint/type-utils@8.22.0':
    resolution:
      {
        integrity: sha512-NzE3aB62fDEaGjaAYZE4LH7I1MUwHooQ98Byq0G0y3kkibPJQIXVUspzlFOmOfHhiDLwKzMlWxaNv+/qcZurJA==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/types@8.22.0':
    resolution:
      {
        integrity: sha512-0S4M4baNzp612zwpD4YOieP3VowOARgK2EkN/GBn95hpyF8E2fbMT55sRHWBq+Huaqk3b3XK+rxxlM8sPgGM6A==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@typescript-eslint/typescript-estree@8.22.0':
    resolution:
      {
        integrity: sha512-SJX99NAS2ugGOzpyhMza/tX+zDwjvwAtQFLsBo3GQxiGcvaKlqGBkmZ+Y1IdiSi9h4Q0Lr5ey+Cp9CGWNY/F/w==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/utils@8.22.0':
    resolution:
      {
        integrity: sha512-T8oc1MbF8L+Bk2msAvCUzjxVB2Z2f+vXYfcucE2wOmYs7ZUwco5Ep0fYZw8quNwOiw9K8GYVL+Kgc2pETNTLOg==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/visitor-keys@8.22.0':
    resolution:
      {
        integrity: sha512-AWpYAXnUgvLNabGTy3uBylkgZoosva/miNd1I8Bz3SjotmQPbVqhO4Cczo8AsZ44XVErEBPr/CRSgaj8sG7g0w==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@ungap/structured-clone@1.3.0':
    resolution:
      {
        integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==
      }

  abbrev@2.0.0:
    resolution:
      {
        integrity: sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==
      }
    engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

  abs-svg-path@0.1.1:
    resolution:
      {
        integrity: sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA==
      }

  acorn-jsx@5.3.2:
    resolution:
      {
        integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
      }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.12.1:
    resolution:
      {
        integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==
      }
    engines: { node: '>=0.4.0' }
    hasBin: true

  acorn@8.14.0:
    resolution:
      {
        integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==
      }
    engines: { node: '>=0.4.0' }
    hasBin: true

  ajv@6.12.6:
    resolution:
      {
        integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
      }

  ajv@8.17.1:
    resolution:
      {
        integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==
      }

  amazon-cognito-identity-js@6.3.12:
    resolution:
      {
        integrity: sha512-s7NKDZgx336cp+oDeUtB2ZzT8jWJp/v2LWuYl+LQtMEODe22RF1IJ4nRiDATp+rp1pTffCZcm44Quw4jx2bqNg==
      }

  ansi-escapes@7.0.0:
    resolution:
      {
        integrity: sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==
      }
    engines: { node: '>=18' }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
      }
    engines: { node: '>=8' }

  ansi-regex@6.1.0:
    resolution:
      {
        integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==
      }
    engines: { node: '>=12' }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
      }
    engines: { node: '>=8' }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==
      }
    engines: { node: '>=12' }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
      }
    engines: { node: '>= 8' }

  arg@5.0.2:
    resolution:
      {
        integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==
      }

  argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
      }

  aria-hidden@1.2.4:
    resolution:
      {
        integrity: sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
      }
    engines: { node: '>=10' }

  aria-query@5.3.2:
    resolution:
      {
        integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==
      }
    engines: { node: '>= 0.4' }

  array-buffer-byte-length@1.0.2:
    resolution:
      {
        integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==
      }
    engines: { node: '>= 0.4' }

  array-includes@3.1.8:
    resolution:
      {
        integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
      }
    engines: { node: '>= 0.4' }

  array.prototype.findlast@1.2.5:
    resolution:
      {
        integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
      }
    engines: { node: '>= 0.4' }

  array.prototype.findlastindex@1.2.5:
    resolution:
      {
        integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
      }
    engines: { node: '>= 0.4' }

  array.prototype.flat@1.3.3:
    resolution:
      {
        integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==
      }
    engines: { node: '>= 0.4' }

  array.prototype.flatmap@1.3.3:
    resolution:
      {
        integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==
      }
    engines: { node: '>= 0.4' }

  array.prototype.tosorted@1.1.4:
    resolution:
      {
        integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
      }
    engines: { node: '>= 0.4' }

  arraybuffer.prototype.slice@1.0.4:
    resolution:
      {
        integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==
      }
    engines: { node: '>= 0.4' }

  ast-types-flow@0.0.8:
    resolution:
      {
        integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==
      }

  async-function@1.0.0:
    resolution:
      {
        integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==
      }
    engines: { node: '>= 0.4' }

  atomic-sleep@1.0.0:
    resolution:
      {
        integrity: sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==
      }
    engines: { node: '>=8.0.0' }

  autoprefixer@10.4.20:
    resolution:
      {
        integrity: sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==
      }
    engines: { node: ^10 || ^12 || >=14 }
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution:
      {
        integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
      }
    engines: { node: '>= 0.4' }

  axe-core@4.10.2:
    resolution:
      {
        integrity: sha512-RE3mdQ7P3FRSe7eqCWoeQ/Z9QXrtniSjp1wUjt5nRC3WIpz5rSCve6o3fsZ2aCpJtrZjSZgjwXAoTO5k4tEI0w==
      }
    engines: { node: '>=4' }

  axobject-query@4.1.0:
    resolution:
      {
        integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==
      }
    engines: { node: '>= 0.4' }

  babel-plugin-macros@3.1.0:
    resolution:
      {
        integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
      }
    engines: { node: '>=10', npm: '>=6' }

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
      }

  base64-js@0.0.8:
    resolution:
      {
        integrity: sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==
      }
    engines: { node: '>= 0.4' }

  base64-js@1.5.1:
    resolution:
      {
        integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==
      }

  bidi-js@1.0.3:
    resolution:
      {
        integrity: sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==
      }

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==
      }
    engines: { node: '>=8' }

  blob-stream@0.1.3:
    resolution:
      {
        integrity: sha512-xXwyhgVmPsFVFFvtM5P0syI17/oae+MIjLn5jGhuD86mmSJ61EWMWmbPrV/0+bdcH9jQ2CzIhmTQKNUJL7IPog==
      }

  blob@0.0.4:
    resolution:
      {
        integrity: sha512-YRc9zvVz4wNaxcXmiSgb9LAg7YYwqQ2xd0Sj6osfA7k/PKmIGVlnOYs3wOFdkRC9/JpQu8sGt/zHgJV7xzerfg==
      }

  body-scroll-lock@4.0.0-beta.0:
    resolution:
      {
        integrity: sha512-a7tP5+0Mw3YlUJcGAKUqIBkYYGlYxk2fnCasq/FUph1hadxlTRjF+gAcZksxANnaMnALjxEddmSi/H3OR8ugcQ==
      }

  bowser@2.11.0:
    resolution:
      {
        integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==
      }

  brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
      }

  brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
      }

  braces@3.0.3:
    resolution:
      {
        integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
      }
    engines: { node: '>=8' }

  brotli@1.3.3:
    resolution:
      {
        integrity: sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==
      }

  browserify-zlib@0.2.0:
    resolution:
      {
        integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
      }

  browserslist@4.24.4:
    resolution:
      {
        integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  bson-objectid@2.0.4:
    resolution:
      {
        integrity: sha512-vgnKAUzcDoa+AeyYwXCoHyF2q6u/8H46dxu5JN+4/TZeq/Dlinn0K6GvxsCLb3LHUJl0m/TLiEK31kUwtgocMQ==
      }

  bson@6.10.4:
    resolution:
      {
        integrity: sha512-WIsKqkSC0ABoBJuT1LEX+2HEvNmNKKgnTAyd0fL8qzK4SH2i9NXg+t08YtdZp/V9IZ33cxe3iV4yM0qg8lMQng==
      }
    engines: { node: '>=16.20.1' }

  buffer@4.9.2:
    resolution:
      {
        integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
      }

  buffer@5.6.0:
    resolution:
      {
        integrity: sha512-/gDYp/UtU0eA1ys8bOs9J6a+E/KWIY+DZ+Q2WESNUA0jFRsJOc0SNUO6xJ5SGA1xueg3NL65W6s+NY5l9cunuw==
      }

  builtin-modules@3.3.0:
    resolution:
      {
        integrity: sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==
      }
    engines: { node: '>=6' }

  busboy@1.6.0:
    resolution:
      {
        integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
      }
    engines: { node: '>=10.16.0' }

  c12@2.0.1:
    resolution:
      {
        integrity: sha512-Z4JgsKXHG37C6PYUtIxCfLJZvo6FyhHJoClwwb9ftUkLpPSkuYqn6Tr+vnaN8hymm0kIbcg6Ey3kv/Q71k5w/A==
      }
    peerDependencies:
      magicast: ^0.3.5
    peerDependenciesMeta:
      magicast:
        optional: true

  call-bind-apply-helpers@1.0.1:
    resolution:
      {
        integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==
      }
    engines: { node: '>= 0.4' }

  call-bind@1.0.8:
    resolution:
      {
        integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
      }
    engines: { node: '>= 0.4' }

  call-bound@1.0.3:
    resolution:
      {
        integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==
      }
    engines: { node: '>= 0.4' }

  callsites@3.1.0:
    resolution:
      {
        integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
      }
    engines: { node: '>=6' }

  camelcase-css@2.0.1:
    resolution:
      {
        integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==
      }
    engines: { node: '>= 6' }

  camelcase@8.0.0:
    resolution:
      {
        integrity: sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==
      }
    engines: { node: '>=16' }

  camelize@1.0.1:
    resolution:
      {
        integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==
      }

  caniuse-lite@1.0.30001696:
    resolution:
      {
        integrity: sha512-pDCPkvzfa39ehJtJ+OwGT/2yvT2SbjfHhiIW2LWOAcMQ7BzwxT/XuyUp4OTOd0XFWA6BKw0JalnBHgSi5DGJBQ==
      }

  ccount@2.0.1:
    resolution:
      {
        integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==
      }

  chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
      }
    engines: { node: '>=10' }

  chalk@5.4.1:
    resolution:
      {
        integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==
      }
    engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

  character-entities-html4@2.1.0:
    resolution:
      {
        integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==
      }

  character-entities-legacy@3.0.0:
    resolution:
      {
        integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==
      }

  character-entities@2.0.2:
    resolution:
      {
        integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==
      }

  character-reference-invalid@2.0.1:
    resolution:
      {
        integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==
      }

  charenc@0.0.2:
    resolution:
      {
        integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==
      }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
      }
    engines: { node: '>= 8.10.0' }

  chokidar@4.0.3:
    resolution:
      {
        integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==
      }
    engines: { node: '>= 14.16.0' }

  chownr@2.0.0:
    resolution:
      {
        integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==
      }
    engines: { node: '>=10' }

  ci-info@4.1.0:
    resolution:
      {
        integrity: sha512-HutrvTNsF48wnxkzERIXOe5/mlcfFcbfCmwcg6CJnizbSue78AbDt+1cgl26zwn61WFxhcPykPfZrbqjGmBb4A==
      }
    engines: { node: '>=8' }

  citty@0.1.6:
    resolution:
      {
        integrity: sha512-tskPPKEs8D2KPafUypv2gxwJP8h/OaJmC82QQGGDQcHvXX43xF2VDACcJVmZ0EuSxkpO9Kc4MlrA3q0+FG58AQ==
      }

  class-variance-authority@0.7.1:
    resolution:
      {
        integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==
      }

  clean-regexp@1.0.0:
    resolution:
      {
        integrity: sha512-GfisEZEJvzKrmGWkvfhgzcz/BllN1USeqD2V6tg14OAOgaCD2Z/PUEuxnAZ/nPvmaHRG7a8y77p1T/IRQ4D1Hw==
      }
    engines: { node: '>=4' }

  clear-cut@2.0.2:
    resolution:
      {
        integrity: sha512-WVgn/gSejQ+0aoR8ucbKIdo6icduPZW6AbWwyUmAUgxy63rUYjwa5rj/HeoNPhf0/XPrl82X8bO/hwBkSmsFtg==
      }

  cli-cursor@5.0.0:
    resolution:
      {
        integrity: sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==
      }
    engines: { node: '>=18' }

  cli-truncate@4.0.0:
    resolution:
      {
        integrity: sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==
      }
    engines: { node: '>=18' }

  client-only@0.0.1:
    resolution:
      {
        integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==
      }

  clone@2.1.2:
    resolution:
      {
        integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==
      }
    engines: { node: '>=0.8' }

  clsx@2.1.1:
    resolution:
      {
        integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==
      }
    engines: { node: '>=6' }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
      }
    engines: { node: '>=7.0.0' }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
      }

  color-string@1.9.1:
    resolution:
      {
        integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
      }

  color@4.2.3:
    resolution:
      {
        integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
      }
    engines: { node: '>=12.5.0' }

  colorette@2.0.20:
    resolution:
      {
        integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==
      }

  commander@10.0.1:
    resolution:
      {
        integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==
      }
    engines: { node: '>=14' }

  commander@12.1.0:
    resolution:
      {
        integrity: sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==
      }
    engines: { node: '>=18' }

  commander@13.1.0:
    resolution:
      {
        integrity: sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==
      }
    engines: { node: '>=18' }

  commander@2.20.3:
    resolution:
      {
        integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
      }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==
      }
    engines: { node: '>= 6' }

  concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
      }

  confbox@0.1.8:
    resolution:
      {
        integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==
      }

  config-chain@1.1.13:
    resolution:
      {
        integrity: sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==
      }

  consola@3.4.0:
    resolution:
      {
        integrity: sha512-EiPU8G6dQG0GFHNR8ljnZFki/8a+cQwEQ+7wpxdChl02Q8HXlwEZWD5lqAF8vC2sEC3Tehr8hy7vErz88LHyUA==
      }
    engines: { node: ^14.18.0 || >=16.10.0 }

  console-table-printer@2.12.1:
    resolution:
      {
        integrity: sha512-wKGOQRRvdnd89pCeH96e2Fn4wkbenSP6LMHfjfyNLMbGuHEFbMqQNuxXqd0oXG9caIOQ1FTvc5Uijp9/4jujnQ==
      }

  convert-source-map@1.9.0:
    resolution:
      {
        integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==
      }

  core-js-compat@3.40.0:
    resolution:
      {
        integrity: sha512-0XEDpr5y5mijvw8Lbc6E5AkjrHfp7eEoPlu36SWeAbcL8fn1G1ANe8DBlo2XoNN89oVpxWwOjYIPVzR4ZvsKCQ==
      }

  core-js@2.6.12:
    resolution:
      {
        integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==
      }
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  cosmiconfig@7.1.0:
    resolution:
      {
        integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
      }
    engines: { node: '>=10' }

  croner@9.1.0:
    resolution:
      {
        integrity: sha512-p9nwwR4qyT5W996vBZhdvBCnMhicY5ytZkR4D1Xj0wuTDEiMnjwR57Q3RXYY/s0EpX6Ay3vgIcfaR+ewGHsi+g==
      }
    engines: { node: '>=18.0' }

  cross-env@7.0.3:
    resolution:
      {
        integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
      }
    engines: { node: '>=10.14', npm: '>=6', yarn: '>=1' }
    hasBin: true

  cross-spawn@7.0.6:
    resolution:
      {
        integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
      }
    engines: { node: '>= 8' }

  crypt@0.0.2:
    resolution:
      {
        integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==
      }

  crypto-js@4.2.0:
    resolution:
      {
        integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==
      }

  css-background-parser@0.1.0:
    resolution:
      {
        integrity: sha512-2EZLisiZQ+7m4wwur/qiYJRniHX4K5Tc9w93MT3AS0WS1u5kaZ4FKXlOTBhOjc+CgEgPiGY+fX1yWD8UwpEqUA==
      }

  css-box-shadow@1.0.0-3:
    resolution:
      {
        integrity: sha512-9jaqR6e7Ohds+aWwmhe6wILJ99xYQbfmK9QQB9CcMjDbTxPZjwEmUQpU91OG05Xgm8BahT5fW+svbsQGjS/zPg==
      }

  css-color-keywords@1.0.0:
    resolution:
      {
        integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==
      }
    engines: { node: '>=4' }

  css-gradient-parser@0.0.16:
    resolution:
      {
        integrity: sha512-3O5QdqgFRUbXvK1x5INf1YkBz1UKSWqrd63vWsum8MNHDBYD5urm3QtxZbKU259OrEXNM26lP/MPY3d1IGkBgA==
      }
    engines: { node: '>=16' }

  css-to-react-native@3.2.0:
    resolution:
      {
        integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==
      }

  css-tree@1.1.3:
    resolution:
      {
        integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
      }
    engines: { node: '>=8.0.0' }

  cssesc@3.0.0:
    resolution:
      {
        integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==
      }
    engines: { node: '>=4' }
    hasBin: true

  cssfilter@0.0.10:
    resolution:
      {
        integrity: sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==
      }

  csstype@3.1.3:
    resolution:
      {
        integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==
      }

  d@1.0.2:
    resolution:
      {
        integrity: sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==
      }
    engines: { node: '>=0.12' }

  damerau-levenshtein@1.0.8:
    resolution:
      {
        integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==
      }

  data-view-buffer@1.0.2:
    resolution:
      {
        integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==
      }
    engines: { node: '>= 0.4' }

  data-view-byte-length@1.0.2:
    resolution:
      {
        integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==
      }
    engines: { node: '>= 0.4' }

  data-view-byte-offset@1.0.1:
    resolution:
      {
        integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==
      }
    engines: { node: '>= 0.4' }

  dataloader@2.2.3:
    resolution:
      {
        integrity: sha512-y2krtASINtPFS1rSDjacrFgn1dcUuoREVabwlOGOe4SdxenREqwjwjElAdwvbGM7kgZz9a3KVicWR7vcz8rnzA==
      }

  date-fns@3.6.0:
    resolution:
      {
        integrity: sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==
      }

  date-fns@4.1.0:
    resolution:
      {
        integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==
      }

  dateformat@4.6.3:
    resolution:
      {
        integrity: sha512-2P0p0pFGzHS5EMnhdxQi7aJN+iMheud0UhG4dlE1DLAlvL8JHjJJTX/CSm4JXwV0Ka5nGk3zC5mcb5bUQUxxMA==
      }

  debug@3.2.7:
    resolution:
      {
        integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
      }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.0:
    resolution:
      {
        integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
      }
    engines: { node: '>=6.0' }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution:
      {
        integrity: sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==
      }

  decode-named-character-reference@1.0.2:
    resolution:
      {
        integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==
      }

  deep-is@0.1.4:
    resolution:
      {
        integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
      }

  deepmerge@4.3.1:
    resolution:
      {
        integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==
      }
    engines: { node: '>=0.10.0' }

  define-data-property@1.1.4:
    resolution:
      {
        integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
      }
    engines: { node: '>= 0.4' }

  define-properties@1.2.1:
    resolution:
      {
        integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
      }
    engines: { node: '>= 0.4' }

  defu@6.1.4:
    resolution:
      {
        integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==
      }

  dequal@2.0.3:
    resolution:
      {
        integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==
      }
    engines: { node: '>=6' }

  destr@2.0.3:
    resolution:
      {
        integrity: sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==
      }

  detect-file@1.0.0:
    resolution:
      {
        integrity: sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q==
      }
    engines: { node: '>=0.10.0' }

  detect-libc@2.0.3:
    resolution:
      {
        integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==
      }
    engines: { node: '>=8' }

  detect-libc@2.0.4:
    resolution:
      {
        integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==
      }
    engines: { node: '>=8' }

  detect-node-es@1.1.0:
    resolution:
      {
        integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==
      }

  devlop@1.1.0:
    resolution:
      {
        integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==
      }

  dfa@1.2.0:
    resolution:
      {
        integrity: sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==
      }

  didyoumean@1.2.2:
    resolution:
      {
        integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==
      }

  dlv@1.1.3:
    resolution:
      {
        integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==
      }

  doctrine@2.1.0:
    resolution:
      {
        integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
      }
    engines: { node: '>=0.10.0' }

  doctrine@3.0.0:
    resolution:
      {
        integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
      }
    engines: { node: '>=6.0.0' }

  dom-helpers@5.2.1:
    resolution:
      {
        integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
      }

  dom-serializer@2.0.0:
    resolution:
      {
        integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
      }

  domelementtype@2.3.0:
    resolution:
      {
        integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==
      }

  domhandler@5.0.3:
    resolution:
      {
        integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
      }
    engines: { node: '>= 4' }

  domutils@3.2.2:
    resolution:
      {
        integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==
      }

  dotenv@16.4.7:
    resolution:
      {
        integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==
      }
    engines: { node: '>=12' }

  dunder-proto@1.0.1:
    resolution:
      {
        integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
      }
    engines: { node: '>= 0.4' }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==
      }

  editorconfig@1.0.4:
    resolution:
      {
        integrity: sha512-L9Qe08KWTlqYMVvMcTIvMAdl1cDUubzRNYL+WfA4bLDMHe4nemKkpmYzkznE1FwLKu0EEmy6obgQKzMJrg4x9Q==
      }
    engines: { node: '>=14' }
    hasBin: true

  electron-to-chromium@1.5.90:
    resolution:
      {
        integrity: sha512-C3PN4aydfW91Natdyd449Kw+BzhLmof6tzy5W1pFC5SpQxVXT+oyiyOG9AgYYSN9OdA/ik3YkCrpwqI8ug5Tug==
      }

  emoji-regex-xs@2.0.1:
    resolution:
      {
        integrity: sha512-1QFuh8l7LqUcKe24LsPUNzjrzJQ7pgRwp1QMcZ5MX6mFplk2zQ08NVCM84++1cveaUUYtcCYHmeFEuNg16sU4g==
      }
    engines: { node: '>=10.0.0' }

  emoji-regex@10.4.0:
    resolution:
      {
        integrity: sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==
      }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==
      }

  encoding@0.1.13:
    resolution:
      {
        integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
      }

  end-of-stream@1.4.4:
    resolution:
      {
        integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
      }

  enhanced-resolve@5.18.0:
    resolution:
      {
        integrity: sha512-0/r0MySGYG8YqlayBZ6MuCfECmHFdJ5qyPh8s8wa5Hnm6SaFLSK1VYCbj+NKp090Nm1caZhD+QTnmxO7esYGyQ==
      }
    engines: { node: '>=10.13.0' }

  entities@4.5.0:
    resolution:
      {
        integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==
      }
    engines: { node: '>=0.12' }

  entities@6.0.1:
    resolution:
      {
        integrity: sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==
      }
    engines: { node: '>=0.12' }

  environment@1.1.0:
    resolution:
      {
        integrity: sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==
      }
    engines: { node: '>=18' }

  error-ex@1.3.2:
    resolution:
      {
        integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
      }

  es-abstract@1.23.9:
    resolution:
      {
        integrity: sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==
      }
    engines: { node: '>= 0.4' }

  es-define-property@1.0.1:
    resolution:
      {
        integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==
      }
    engines: { node: '>= 0.4' }

  es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==
      }
    engines: { node: '>= 0.4' }

  es-iterator-helpers@1.2.1:
    resolution:
      {
        integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==
      }
    engines: { node: '>= 0.4' }

  es-object-atoms@1.1.1:
    resolution:
      {
        integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
      }
    engines: { node: '>= 0.4' }

  es-set-tostringtag@2.1.0:
    resolution:
      {
        integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
      }
    engines: { node: '>= 0.4' }

  es-shim-unscopables@1.0.2:
    resolution:
      {
        integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
      }

  es-to-primitive@1.3.0:
    resolution:
      {
        integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==
      }
    engines: { node: '>= 0.4' }

  es5-ext@0.10.64:
    resolution:
      {
        integrity: sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==
      }
    engines: { node: '>=0.10' }

  es6-iterator@2.0.3:
    resolution:
      {
        integrity: sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==
      }

  es6-symbol@3.1.4:
    resolution:
      {
        integrity: sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==
      }
    engines: { node: '>=0.12' }

  esbuild@0.23.1:
    resolution:
      {
        integrity: sha512-VVNz/9Sa0bs5SELtn3f7qhJCDPCF5oMEl5cO9/SSinpE9hbPVvxbd572HH5AKiP7WD8INO53GgfDDhRjkylHEg==
      }
    engines: { node: '>=18' }
    hasBin: true

  esbuild@0.25.8:
    resolution:
      {
        integrity: sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q==
      }
    engines: { node: '>=18' }
    hasBin: true

  escalade@3.2.0:
    resolution:
      {
        integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==
      }
    engines: { node: '>=6' }

  escape-html@1.0.3:
    resolution:
      {
        integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==
      }

  escape-string-regexp@1.0.5:
    resolution:
      {
        integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
      }
    engines: { node: '>=0.8.0' }

  escape-string-regexp@4.0.0:
    resolution:
      {
        integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
      }
    engines: { node: '>=10' }

  eslint-config-next@15.0.0:
    resolution:
      {
        integrity: sha512-HFeTwCR2lFEUWmdB00WZrzaak2CvMvxici38gQknA6Bu2HPizSE4PNFGaFzr5GupjBt+SBJ/E0GIP57ZptOD3g==
      }
    peerDependencies:
      eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
      typescript: '>=3.3.1'
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-config-prettier@9.1.0:
    resolution:
      {
        integrity: sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==
      }
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-import-resolver-node@0.3.9:
    resolution:
      {
        integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
      }

  eslint-import-resolver-typescript@3.7.0:
    resolution:
      {
        integrity: sha512-Vrwyi8HHxY97K5ebydMtffsWAn1SCR9eol49eCd5fJS4O1WV7PaAjbcjmbfJJSMz/t4Mal212Uz/fQZrOB8mow==
      }
    engines: { node: ^14.18.0 || >=16.0.0 }
    peerDependencies:
      eslint: '*'
      eslint-plugin-import: '*'
      eslint-plugin-import-x: '*'
    peerDependenciesMeta:
      eslint-plugin-import:
        optional: true
      eslint-plugin-import-x:
        optional: true

  eslint-module-utils@2.12.0:
    resolution:
      {
        integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==
      }
    engines: { node: '>=4' }
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-disable@2.0.3:
    resolution:
      {
        integrity: sha512-JWT4TPhcIhd8jcwACbsDValvAtHl7BQDhbTZC40vqcp4Fciz2oTnEAFtOCGojdskviN3VGOObVCrjjWe8DGYIg==
      }
    peerDependencies:
      eslint: '>=0.16.0'

  eslint-plugin-import@2.31.0:
    resolution:
      {
        integrity: sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==
      }
    engines: { node: '>=4' }
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jsx-a11y@6.10.2:
    resolution:
      {
        integrity: sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==
      }
    engines: { node: '>=4.0' }
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

  eslint-plugin-react-hooks@5.1.0:
    resolution:
      {
        integrity: sha512-mpJRtPgHN2tNAvZ35AMfqeB3Xqeo273QxrHJsbBEPWODRM4r0yB6jfoROqKEYrOn27UtRPpcpHc2UqyBSuUNTw==
      }
    engines: { node: '>=10' }
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react@7.37.4:
    resolution:
      {
        integrity: sha512-BGP0jRmfYyvOyvMoRX/uoUeW+GqNj9y16bPQzqAHf3AYII/tDs+jMN0dBVkl88/OZwNGwrVFxE7riHsXVfy/LQ==
      }
    engines: { node: '>=4' }
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-unicorn@56.0.1:
    resolution:
      {
        integrity: sha512-FwVV0Uwf8XPfVnKSGpMg7NtlZh0G0gBarCaFcMUOoqPxXryxdYxTRRv4kH6B9TFCVIrjRXG+emcxIk2ayZilog==
      }
    engines: { node: '>=18.18' }
    peerDependencies:
      eslint: '>=8.56.0'

  eslint-scope@7.2.2:
    resolution:
      {
        integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint-visitor-keys@3.4.3:
    resolution:
      {
        integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint-visitor-keys@4.2.0:
    resolution:
      {
        integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  eslint@8.57.1:
    resolution:
      {
        integrity: sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  esniff@2.0.1:
    resolution:
      {
        integrity: sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==
      }
    engines: { node: '>=0.10' }

  espree@9.6.1:
    resolution:
      {
        integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  esquery@1.6.0:
    resolution:
      {
        integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
      }
    engines: { node: '>=0.10' }

  esrecurse@4.3.0:
    resolution:
      {
        integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
      }
    engines: { node: '>=4.0' }

  estraverse@5.3.0:
    resolution:
      {
        integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
      }
    engines: { node: '>=4.0' }

  estree-util-is-identifier-name@3.0.0:
    resolution:
      {
        integrity: sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==
      }

  estree-util-visit@2.0.0:
    resolution:
      {
        integrity: sha512-m5KgiH85xAhhW8Wta0vShLcUvOsh3LLPI2YVwcbio1l7E09NTLL1EyMZFM1OyWowoH0skScNbhOPl4kcBgzTww==
      }

  esutils@2.0.3:
    resolution:
      {
        integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
      }
    engines: { node: '>=0.10.0' }

  event-emitter@0.3.5:
    resolution:
      {
        integrity: sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==
      }

  eventemitter3@5.0.1:
    resolution:
      {
        integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==
      }

  events@3.3.0:
    resolution:
      {
        integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==
      }
    engines: { node: '>=0.8.x' }

  execa@8.0.1:
    resolution:
      {
        integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==
      }
    engines: { node: '>=16.17' }

  expand-tilde@2.0.2:
    resolution:
      {
        integrity: sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==
      }
    engines: { node: '>=0.10.0' }

  ext@1.7.0:
    resolution:
      {
        integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==
      }

  fast-base64-decode@1.0.0:
    resolution:
      {
        integrity: sha512-qwaScUgUGBYeDNRnbc/KyllVU88Jk1pRHPStuF/lO7B0/RTRLj7U0lkdTAutlBblY08rwZDff6tNU9cjv6j//Q==
      }

  fast-copy@3.0.2:
    resolution:
      {
        integrity: sha512-dl0O9Vhju8IrcLndv2eU4ldt1ftXMqqfgN4H1cpmGV7P6jeB9FwpN9a2c8DPGE1Ys88rNUJVYDHq73CGAGOPfQ==
      }

  fast-deep-equal@2.0.1:
    resolution:
      {
        integrity: sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==
      }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
      }

  fast-glob@3.3.1:
    resolution:
      {
        integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==
      }
    engines: { node: '>=8.6.0' }

  fast-glob@3.3.3:
    resolution:
      {
        integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==
      }
    engines: { node: '>=8.6.0' }

  fast-json-stable-stringify@2.1.0:
    resolution:
      {
        integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
      }

  fast-levenshtein@2.0.6:
    resolution:
      {
        integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
      }

  fast-redact@3.5.0:
    resolution:
      {
        integrity: sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==
      }
    engines: { node: '>=6' }

  fast-safe-stringify@2.1.1:
    resolution:
      {
        integrity: sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==
      }

  fast-uri@3.0.6:
    resolution:
      {
        integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==
      }

  fast-xml-parser@4.4.1:
    resolution:
      {
        integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==
      }
    hasBin: true

  fast-xml-parser@5.2.5:
    resolution:
      {
        integrity: sha512-pfX9uG9Ki0yekDHx2SiuRIyFdyAr1kMIMitPvb0YBo8SUfKvia7w7FIyd/l6av85pFYRhZscS75MwMnbvY+hcQ==
      }
    hasBin: true

  fastq@1.18.0:
    resolution:
      {
        integrity: sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==
      }

  fdir@6.4.3:
    resolution:
      {
        integrity: sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==
      }
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  fflate@0.7.4:
    resolution:
      {
        integrity: sha512-5u2V/CDW15QM1XbbgS+0DfPxVB+jUKhWEKuuFuHncbk3tEEqzmoXL+2KyOFuKGqOnmdIy0/davWF1CkuwtibCw==
      }

  file-entry-cache@6.0.1:
    resolution:
      {
        integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
      }
    engines: { node: ^10.12.0 || >=12.0.0 }

  file-type@19.3.0:
    resolution:
      {
        integrity: sha512-mROwiKLZf/Kwa/2Rol+OOZQn1eyTkPB3ZTwC0ExY6OLFCbgxHYZvBm7xI77NvfZFMKBsmuXfmLJnD4eEftEhrA==
      }
    engines: { node: '>=18' }

  fill-range@7.1.1:
    resolution:
      {
        integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
      }
    engines: { node: '>=8' }

  find-node-modules@2.1.3:
    resolution:
      {
        integrity: sha512-UC2I2+nx1ZuOBclWVNdcnbDR5dlrOdVb7xNjmT/lHE+LsgztWks3dG7boJ37yTS/venXw84B/mAW9uHVoC5QRg==
      }

  find-root@1.1.0:
    resolution:
      {
        integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==
      }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
      }
    engines: { node: '>=8' }

  find-up@5.0.0:
    resolution:
      {
        integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
      }
    engines: { node: '>=10' }

  findup-sync@4.0.0:
    resolution:
      {
        integrity: sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ==
      }
    engines: { node: '>= 8' }

  flat-cache@3.2.0:
    resolution:
      {
        integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
      }
    engines: { node: ^10.12.0 || >=12.0.0 }

  flatted@3.3.2:
    resolution:
      {
        integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==
      }

  focus-trap@7.5.4:
    resolution:
      {
        integrity: sha512-N7kHdlgsO/v+iD/dMoJKtsSqs5Dz/dXZVebRgJw23LDk+jMi/974zyiOYDziY2JPp8xivq9BmUGwIJMiuSBi7w==
      }

  fontkit@2.0.4:
    resolution:
      {
        integrity: sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==
      }

  for-each@0.3.4:
    resolution:
      {
        integrity: sha512-kKaIINnFpzW6ffJNDjjyjrk21BkDx38c0xa/klsT8VzLCaMEefv4ZTacrcVR4DmgTeBra++jMDAfS/tS799YDw==
      }
    engines: { node: '>= 0.4' }

  foreground-child@3.3.0:
    resolution:
      {
        integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==
      }
    engines: { node: '>=14' }

  fraction.js@4.3.7:
    resolution:
      {
        integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==
      }

  framer-motion@12.5.0:
    resolution:
      {
        integrity: sha512-buPlioFbH9/W7rDzYh1C09AuZHAk2D1xTA1BlounJ2Rb9aRg84OXexP0GLd+R83v0khURdMX7b5MKnGTaSg5iA==
      }
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fs-minipass@2.1.0:
    resolution:
      {
        integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
      }
    engines: { node: '>= 8' }

  fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
      }

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==
      }

  function.prototype.name@1.1.8:
    resolution:
      {
        integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==
      }
    engines: { node: '>= 0.4' }

  functions-have-names@1.2.3:
    resolution:
      {
        integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==
      }

  get-east-asian-width@1.3.0:
    resolution:
      {
        integrity: sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==
      }
    engines: { node: '>=18' }

  get-intrinsic@1.2.7:
    resolution:
      {
        integrity: sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==
      }
    engines: { node: '>= 0.4' }

  get-nonce@1.0.1:
    resolution:
      {
        integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==
      }
    engines: { node: '>=6' }

  get-proto@1.0.1:
    resolution:
      {
        integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
      }
    engines: { node: '>= 0.4' }

  get-stream@8.0.1:
    resolution:
      {
        integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==
      }
    engines: { node: '>=16' }

  get-symbol-description@1.1.0:
    resolution:
      {
        integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==
      }
    engines: { node: '>= 0.4' }

  get-tsconfig@4.10.0:
    resolution:
      {
        integrity: sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==
      }

  get-tsconfig@4.8.1:
    resolution:
      {
        integrity: sha512-k9PN+cFBmaLWtVz29SkUoqU5O0slLuHJXt/2P+tMVFT+phsSGXGkp9t3rQIqdz0e+06EHNGs3oM6ZX1s2zHxRg==
      }

  giget@1.2.4:
    resolution:
      {
        integrity: sha512-Wv+daGyispVoA31TrWAVR+aAdP7roubTPEM/8JzRnqXhLbdJH0T9eQyXVFF8fjk3WKTsctII6QcyxILYgNp2DA==
      }
    hasBin: true

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
      }
    engines: { node: '>= 6' }

  glob-parent@6.0.2:
    resolution:
      {
        integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
      }
    engines: { node: '>=10.13.0' }

  glob@10.4.5:
    resolution:
      {
        integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
      }
    hasBin: true

  glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
      }
    deprecated: Glob versions prior to v9 are no longer supported

  global-modules@1.0.0:
    resolution:
      {
        integrity: sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==
      }
    engines: { node: '>=0.10.0' }

  global-prefix@1.0.2:
    resolution:
      {
        integrity: sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==
      }
    engines: { node: '>=0.10.0' }

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
      }
    engines: { node: '>=4' }

  globals@13.24.0:
    resolution:
      {
        integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
      }
    engines: { node: '>=8' }

  globals@15.14.0:
    resolution:
      {
        integrity: sha512-OkToC372DtlQeje9/zHIo5CT8lRP/FUgEOKBEhU4e0abL7J7CD24fD9ohiLN5hagG/kWCYj4K5oaxxtj2Z0Dig==
      }
    engines: { node: '>=18' }

  globalthis@1.0.4:
    resolution:
      {
        integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
      }
    engines: { node: '>= 0.4' }

  gopd@1.2.0:
    resolution:
      {
        integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==
      }
    engines: { node: '>= 0.4' }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==
      }

  graphemer@1.4.0:
    resolution:
      {
        integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==
      }

  graphql-http@1.22.4:
    resolution:
      {
        integrity: sha512-OC3ucK988teMf+Ak/O+ZJ0N2ukcgrEurypp8ePyJFWq83VzwRAmHxxr+XxrMpxO/FIwI4a7m/Fzv3tWGJv0wPA==
      }
    engines: { node: '>=12' }
    peerDependencies:
      graphql: '>=0.11 <=16'

  graphql-playground-html@1.6.30:
    resolution:
      {
        integrity: sha512-tpCujhsJMva4aqE8ULnF7/l3xw4sNRZcSHu+R00VV+W0mfp+Q20Plvcrp+5UXD+2yS6oyCXncA+zoQJQqhGCEw==
      }

  graphql-scalars@1.22.2:
    resolution:
      {
        integrity: sha512-my9FB4GtghqXqi/lWSVAOPiTzTnnEzdOXCsAC2bb5V7EFNQjVjwy3cSSbUvgYOtDuDibd+ZsCDhz+4eykYOlhQ==
      }
    engines: { node: '>=10' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  graphql@16.10.0:
    resolution:
      {
        integrity: sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==
      }
    engines: { node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0 }

  handlebars@4.7.8:
    resolution:
      {
        integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==
      }
    engines: { node: '>=0.4.7' }
    hasBin: true

  has-bigints@1.1.0:
    resolution:
      {
        integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==
      }
    engines: { node: '>= 0.4' }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
      }
    engines: { node: '>=8' }

  has-property-descriptors@1.0.2:
    resolution:
      {
        integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
      }

  has-proto@1.2.0:
    resolution:
      {
        integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==
      }
    engines: { node: '>= 0.4' }

  has-symbols@1.1.0:
    resolution:
      {
        integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==
      }
    engines: { node: '>= 0.4' }

  has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
      }
    engines: { node: '>= 0.4' }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
      }
    engines: { node: '>= 0.4' }

  help-me@5.0.0:
    resolution:
      {
        integrity: sha512-7xgomUX6ADmcYzFik0HzAxh/73YlKR9bmFzf51CZwR+b6YtzU2m0u49hQCqV6SvlqIqsaxovfwdvbnsw3b/zpg==
      }

  hex-rgb@4.3.0:
    resolution:
      {
        integrity: sha512-Ox1pJVrDCyGHMG9CFg1tmrRUMRPRsAWYc/PinY0XzJU4K7y7vjNoLKIQ7BR5UJMCxNN8EM1MNDmHWA/B3aZUuw==
      }
    engines: { node: '>=6' }

  hoist-non-react-statics@3.3.2:
    resolution:
      {
        integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
      }

  homedir-polyfill@1.0.3:
    resolution:
      {
        integrity: sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==
      }
    engines: { node: '>=0.10.0' }

  hosted-git-info@2.8.9:
    resolution:
      {
        integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==
      }

  hsl-to-hex@1.0.0:
    resolution:
      {
        integrity: sha512-K6GVpucS5wFf44X0h2bLVRDsycgJmf9FF2elg+CrqD8GcFU8c6vYhgXn8NjUkFCwj+xDFb70qgLbTUm6sxwPmA==
      }

  hsl-to-rgb-for-reals@1.1.1:
    resolution:
      {
        integrity: sha512-LgOWAkrN0rFaQpfdWBQlv/VhkOxb5AsBjk6NQVx4yEzWS923T07X0M1Y0VNko2H52HeSpZrZNNMJ0aFqsdVzQg==
      }

  html-dom-parser@5.1.1:
    resolution:
      {
        integrity: sha512-+o4Y4Z0CLuyemeccvGN4bAO20aauB2N9tFEAep5x4OW34kV4PTarBHm6RL02afYt2BMKcr0D2Agep8S3nJPIBg==
      }

  html-react-parser@5.2.5:
    resolution:
      {
        integrity: sha512-bRPdv8KTqG9CEQPMNGksDqmbiRfVQeOidry8pVetdh/1jQ1Edx4KX5m0lWvDD89Pt4CqTYjK1BLz6NoNVxN/Uw==
      }
    peerDependencies:
      '@types/react': 0.14 || 15 || 16 || 17 || 18 || 19
      react: 0.14 || 15 || 16 || 17 || 18 || 19
    peerDependenciesMeta:
      '@types/react':
        optional: true

  html-to-text@9.0.5:
    resolution:
      {
        integrity: sha512-qY60FjREgVZL03vJU6IfMV4GDjGBIoOyvuFdpBDIX9yTlDw0TjxVBQp+P8NvpdIXNJvfWBTNul7fsAQJq2FNpg==
      }
    engines: { node: '>=14' }

  htmlparser2@10.0.0:
    resolution:
      {
        integrity: sha512-TwAZM+zE5Tq3lrEHvOlvwgj1XLWQCtaaibSN11Q+gGBAS7Y1uZSWwXXRe4iF6OXnaq1riyQAPFOBtYc77Mxq0g==
      }

  htmlparser2@8.0.2:
    resolution:
      {
        integrity: sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==
      }

  http-status@2.1.0:
    resolution:
      {
        integrity: sha512-O5kPr7AW7wYd/BBiOezTwnVAnmSNFY+J7hlZD2X5IOxVBetjcHAiTXhzj0gMrnojQlwy+UT1/Y3H3vJ3UlmvLA==
      }
    engines: { node: '>= 0.4.0' }

  human-signals@5.0.0:
    resolution:
      {
        integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==
      }
    engines: { node: '>=16.17.0' }

  husky@9.1.7:
    resolution:
      {
        integrity: sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA==
      }
    engines: { node: '>=18' }
    hasBin: true

  hyphen@1.10.6:
    resolution:
      {
        integrity: sha512-fXHXcGFTXOvZTSkPJuGOQf5Lv5T/R2itiiCVPg9LxAje5D00O0pP83yJShFq5V89Ly//Gt6acj7z8pbBr34stw==
      }

  iconv-lite@0.6.3:
    resolution:
      {
        integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
      }
    engines: { node: '>=0.10.0' }

  ieee754@1.2.1:
    resolution:
      {
        integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==
      }

  ignore@5.3.2:
    resolution:
      {
        integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==
      }
    engines: { node: '>= 4' }

  image-size@2.0.2:
    resolution:
      {
        integrity: sha512-IRqXKlaXwgSMAMtpNzZa1ZAe8m+Sa1770Dhk8VkSsP9LS+iHD62Zd8FQKs8fbPiagBE7BzoFX23cxFnwshpV6w==
      }
    engines: { node: '>=16.x' }
    hasBin: true

  immutable@4.3.7:
    resolution:
      {
        integrity: sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==
      }

  import-fresh@3.3.0:
    resolution:
      {
        integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
      }
    engines: { node: '>=6' }

  imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
      }
    engines: { node: '>=0.8.19' }

  indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==
      }
    engines: { node: '>=8' }

  inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
      }
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
      }

  ini@1.3.8:
    resolution:
      {
        integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==
      }

  inline-style-parser@0.2.4:
    resolution:
      {
        integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==
      }

  input-otp@1.4.2:
    resolution:
      {
        integrity: sha512-l3jWwYNvrEa6NTCt7BECfCm48GvwuZzkoeG3gBL2w4CHeOXW3eKFmf9UNYkNfYc3mxMrthMnxjIE07MT0zLBQA==
      }
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc

  install@0.13.0:
    resolution:
      {
        integrity: sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==
      }
    engines: { node: '>= 0.10' }

  internal-slot@1.1.0:
    resolution:
      {
        integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==
      }
    engines: { node: '>= 0.4' }

  intl-messageformat@10.7.14:
    resolution:
      {
        integrity: sha512-mMGnE4E1otdEutV5vLUdCxRJygHB5ozUBxsPB5qhitewssrS/qGruq9bmvIRkkGsNeK5ZWLfYRld18UHGTIifQ==
      }

  ipaddr.js@2.2.0:
    resolution:
      {
        integrity: sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==
      }
    engines: { node: '>= 10' }

  is-alphabetical@2.0.1:
    resolution:
      {
        integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==
      }

  is-alphanumerical@2.0.1:
    resolution:
      {
        integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==
      }

  is-array-buffer@3.0.5:
    resolution:
      {
        integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==
      }
    engines: { node: '>= 0.4' }

  is-arrayish@0.2.1:
    resolution:
      {
        integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==
      }

  is-arrayish@0.3.2:
    resolution:
      {
        integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==
      }

  is-async-function@2.1.1:
    resolution:
      {
        integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==
      }
    engines: { node: '>= 0.4' }

  is-bigint@1.1.0:
    resolution:
      {
        integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==
      }
    engines: { node: '>= 0.4' }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
      }
    engines: { node: '>=8' }

  is-boolean-object@1.2.1:
    resolution:
      {
        integrity: sha512-l9qO6eFlUETHtuihLcYOaLKByJ1f+N4kthcU9YjHy3N+B3hWv0y/2Nd0mu/7lTFnRQHTrSdXF50HQ3bl5fEnng==
      }
    engines: { node: '>= 0.4' }

  is-buffer@1.1.6:
    resolution:
      {
        integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==
      }

  is-builtin-module@3.2.1:
    resolution:
      {
        integrity: sha512-BSLE3HnV2syZ0FK0iMA/yUGplUeMmNz4AW5fnTunbCIqZi4vG3WjJT9FHMy5D69xmAYBHXQhJdALdpwVxV501A==
      }
    engines: { node: '>=6' }

  is-bun-module@1.3.0:
    resolution:
      {
        integrity: sha512-DgXeu5UWI0IsMQundYb5UAOzm6G2eVnarJ0byP6Tm55iZNKceD59LNPA2L4VvsScTtHcw0yEkVwSf7PC+QoLSA==
      }

  is-callable@1.2.7:
    resolution:
      {
        integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
      }
    engines: { node: '>= 0.4' }

  is-core-module@2.16.1:
    resolution:
      {
        integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
      }
    engines: { node: '>= 0.4' }

  is-data-view@1.0.2:
    resolution:
      {
        integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==
      }
    engines: { node: '>= 0.4' }

  is-date-object@1.1.0:
    resolution:
      {
        integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
      }
    engines: { node: '>= 0.4' }

  is-decimal@2.0.1:
    resolution:
      {
        integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==
      }

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
      }
    engines: { node: '>=0.10.0' }

  is-finalizationregistry@1.1.1:
    resolution:
      {
        integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==
      }
    engines: { node: '>= 0.4' }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==
      }
    engines: { node: '>=8' }

  is-fullwidth-code-point@4.0.0:
    resolution:
      {
        integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==
      }
    engines: { node: '>=12' }

  is-fullwidth-code-point@5.0.0:
    resolution:
      {
        integrity: sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==
      }
    engines: { node: '>=18' }

  is-generator-function@1.1.0:
    resolution:
      {
        integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
      }
    engines: { node: '>= 0.4' }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
      }
    engines: { node: '>=0.10.0' }

  is-hexadecimal@2.0.1:
    resolution:
      {
        integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==
      }

  is-map@2.0.3:
    resolution:
      {
        integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==
      }
    engines: { node: '>= 0.4' }

  is-number-object@1.1.1:
    resolution:
      {
        integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==
      }
    engines: { node: '>= 0.4' }

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==
      }
    engines: { node: '>=0.12.0' }

  is-path-inside@3.0.3:
    resolution:
      {
        integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==
      }
    engines: { node: '>=8' }

  is-regex@1.2.1:
    resolution:
      {
        integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
      }
    engines: { node: '>= 0.4' }

  is-set@2.0.3:
    resolution:
      {
        integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==
      }
    engines: { node: '>= 0.4' }

  is-shared-array-buffer@1.0.4:
    resolution:
      {
        integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==
      }
    engines: { node: '>= 0.4' }

  is-stream@3.0.0:
    resolution:
      {
        integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  is-string@1.1.1:
    resolution:
      {
        integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==
      }
    engines: { node: '>= 0.4' }

  is-symbol@1.1.1:
    resolution:
      {
        integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==
      }
    engines: { node: '>= 0.4' }

  is-typed-array@1.1.15:
    resolution:
      {
        integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
      }
    engines: { node: '>= 0.4' }

  is-url@1.2.4:
    resolution:
      {
        integrity: sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==
      }

  is-weakmap@2.0.2:
    resolution:
      {
        integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==
      }
    engines: { node: '>= 0.4' }

  is-weakref@1.1.0:
    resolution:
      {
        integrity: sha512-SXM8Nwyys6nT5WP6pltOwKytLV7FqQ4UiibxVmW+EIosHcmCqkkjViTb5SNssDlkCiEYRP1/pdWUKVvZBmsR2Q==
      }
    engines: { node: '>= 0.4' }

  is-weakset@2.0.4:
    resolution:
      {
        integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==
      }
    engines: { node: '>= 0.4' }

  is-windows@1.0.2:
    resolution:
      {
        integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==
      }
    engines: { node: '>=0.10.0' }

  isarray@1.0.0:
    resolution:
      {
        integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==
      }

  isarray@2.0.5:
    resolution:
      {
        integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==
      }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
      }

  isomorphic-unfetch@3.1.0:
    resolution:
      {
        integrity: sha512-geDJjpoZ8N0kWexiwkX8F9NkTsXhetLPVbZFQ+JTW239QNOwvB0gniuR1Wc6f0AMTn7/mFGyXvHTifrCp/GH8Q==
      }

  isomorphic.js@0.2.5:
    resolution:
      {
        integrity: sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==
      }

  iterator.prototype@1.1.5:
    resolution:
      {
        integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==
      }
    engines: { node: '>= 0.4' }

  jackspeak@3.4.3:
    resolution:
      {
        integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
      }

  javascript-natural-sort@0.7.1:
    resolution:
      {
        integrity: sha512-nO6jcEfZWQXDhOiBtG2KvKyEptz7RVbpGP4vTD2hLBdmNQSsCiicO2Ioinv6UI4y9ukqnBpy+XZ9H6uLNgJTlw==
      }

  jay-peg@1.1.1:
    resolution:
      {
        integrity: sha512-D62KEuBxz/ip2gQKOEhk/mx14o7eiFRaU+VNNSP4MOiIkwb/D6B3G1Mfas7C/Fit8EsSV2/IWjZElx/Gs6A4ww==
      }

  jiti@1.21.7:
    resolution:
      {
        integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==
      }
    hasBin: true

  jiti@2.4.2:
    resolution:
      {
        integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==
      }
    hasBin: true

  jose@5.9.6:
    resolution:
      {
        integrity: sha512-AMlnetc9+CV9asI19zHmrgS/WYsWUwCn2R7RzlbJWD7F9eWYUTGyBmU9o6PxngtLGOiDGPRu+Uc4fhKzbpteZQ==
      }

  joycon@3.1.1:
    resolution:
      {
        integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==
      }
    engines: { node: '>=10' }

  jpeg-exif@1.1.4:
    resolution:
      {
        integrity: sha512-a+bKEcCjtuW5WTdgeXFzswSrdqi0jk4XlEtZlx5A94wCoBpFjfFTbo/Tra5SpNCl/YFZPvcV1dJc+TAYeg6ROQ==
      }

  js-beautify@1.15.1:
    resolution:
      {
        integrity: sha512-ESjNzSlt/sWE8sciZH8kBF8BPlwXPwhR6pWKAw8bw4Bwj+iZcnKW6ONWUutJ7eObuBZQpiIb8S7OYspWrKt7rA==
      }
    engines: { node: '>=14' }
    hasBin: true

  js-cookie@2.2.1:
    resolution:
      {
        integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==
      }

  js-cookie@3.0.5:
    resolution:
      {
        integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==
      }
    engines: { node: '>=14' }

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
      }

  js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
      }
    hasBin: true

  jsesc@0.5.0:
    resolution:
      {
        integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==
      }
    hasBin: true

  jsesc@2.5.2:
    resolution:
      {
        integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==
      }
    engines: { node: '>=4' }
    hasBin: true

  jsesc@3.1.0:
    resolution:
      {
        integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==
      }
    engines: { node: '>=6' }
    hasBin: true

  json-buffer@3.0.1:
    resolution:
      {
        integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==
      }

  json-parse-even-better-errors@2.3.1:
    resolution:
      {
        integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==
      }

  json-schema-to-typescript@15.0.3:
    resolution:
      {
        integrity: sha512-iOKdzTUWEVM4nlxpFudFsWyUiu/Jakkga4OZPEt7CGoSEsAsUgdOZqR6pcgx2STBek9Gm4hcarJpXSzIvZ/hKA==
      }
    engines: { node: '>=16.0.0' }
    hasBin: true

  json-schema-traverse@0.4.1:
    resolution:
      {
        integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
      }

  json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==
      }

  json-stable-stringify-without-jsonify@1.0.1:
    resolution:
      {
        integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
      }

  json5@1.0.2:
    resolution:
      {
        integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
      }
    hasBin: true

  jsox@1.2.121:
    resolution:
      {
        integrity: sha512-9Ag50tKhpTwS6r5wh3MJSAvpSof0UBr39Pto8OnzFT32Z/pAbxAsKHzyvsyMEHVslELvHyO/4/jaQELHk8wDcw==
      }
    hasBin: true

  jsx-ast-utils@3.3.5:
    resolution:
      {
        integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
      }
    engines: { node: '>=4.0' }

  kareem@2.6.3:
    resolution:
      {
        integrity: sha512-C3iHfuGUXK2u8/ipq9LfjFfXFxAZMQJJq7vLS45r3D9Y2xQ/m4S8zaR4zMLFWh9AsNPXmcFfUDhTEO8UIC/V6Q==
      }
    engines: { node: '>=12.0.0' }

  keyv@4.5.4:
    resolution:
      {
        integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
      }

  kleur@3.0.3:
    resolution:
      {
        integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==
      }
    engines: { node: '>=6' }

  language-subtag-registry@0.3.23:
    resolution:
      {
        integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==
      }

  language-tags@1.0.9:
    resolution:
      {
        integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
      }
    engines: { node: '>=0.10' }

  leac@0.6.0:
    resolution:
      {
        integrity: sha512-y+SqErxb8h7nE/fiEX07jsbuhrpO9lL8eca7/Y1nuWV2moNlXhyd59iDGcRf6moVyDMbmTNzL40SUyrFU/yDpg==
      }

  levn@0.4.1:
    resolution:
      {
        integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
      }
    engines: { node: '>= 0.8.0' }

  lexical@0.28.0:
    resolution:
      {
        integrity: sha512-dLE3O1PZg0TlZxRQo9YDpjCjDUj8zluGyBO9MHdjo21qZmMUNrxQPeCRt8fn2s5l4HKYFQ1YNgl7k1pOJB/vZQ==
      }

  lib0@0.2.114:
    resolution:
      {
        integrity: sha512-gcxmNFzA4hv8UYi8j43uPlQ7CGcyMJ2KQb5kZASw6SnAKAf10hK12i2fjrS3Cl/ugZa5Ui6WwIu1/6MIXiHttQ==
      }
    engines: { node: '>=16' }
    hasBin: true

  lilconfig@3.1.3:
    resolution:
      {
        integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==
      }
    engines: { node: '>=14' }

  linebreak@1.1.0:
    resolution:
      {
        integrity: sha512-MHp03UImeVhB7XZtjd0E4n6+3xr5Dq/9xI/5FptGk5FrbDR3zagPa2DS6U8ks/3HjbKWG9Q1M2ufOzxV2qLYSQ==
      }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==
      }

  lint-staged@15.4.3:
    resolution:
      {
        integrity: sha512-FoH1vOeouNh1pw+90S+cnuoFwRfUD9ijY2GKy5h7HS3OR7JVir2N2xrsa0+Twc1B7cW72L+88geG5cW4wIhn7g==
      }
    engines: { node: '>=18.12.0' }
    hasBin: true

  listr2@8.2.5:
    resolution:
      {
        integrity: sha512-iyAZCeyD+c1gPyE9qpFu8af0Y+MRtmKOncdGoA2S5EY8iFq99dmmvkNnHiWo+pj0s7yH7l3KPIgee77tKpXPWQ==
      }
    engines: { node: '>=18.0.0' }

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
      }
    engines: { node: '>=8' }

  locate-path@6.0.0:
    resolution:
      {
        integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
      }
    engines: { node: '>=10' }

  lodash.get@4.4.2:
    resolution:
      {
        integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==
      }
    deprecated: This package is deprecated. Use the optional chaining (?.) operator instead.

  lodash.merge@4.6.2:
    resolution:
      {
        integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==
      }

  lodash@4.17.21:
    resolution:
      {
        integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
      }

  log-update@6.1.0:
    resolution:
      {
        integrity: sha512-9ie8ItPR6tjY5uYJh8K/Zrv/RMZ5VOlOWvtZdEHYSTFKZfIBPQa9tOAEeAWhd+AnIneLJ22w5fjOYtoutpWq5w==
      }
    engines: { node: '>=18' }

  longest-streak@3.1.0:
    resolution:
      {
        integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==
      }

  loose-envify@1.4.0:
    resolution:
      {
        integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
      }
    hasBin: true

  lru-cache@10.4.3:
    resolution:
      {
        integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==
      }

  lucide-react@0.461.0:
    resolution:
      {
        integrity: sha512-Scpw3D/dV1bgVRC5Kh774RCm99z0iZpPv75M6kg7QL1lLvkQ1rmI1Sjjic1aGp1ULBwd7FokV6ry0g+d6pMB+w==
      }
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc

  marked@7.0.4:
    resolution:
      {
        integrity: sha512-t8eP0dXRJMtMvBojtkcsA7n48BkauktUKzfkPSCq85ZMTJ0v76Rke4DYz01omYpPTUh4p/f7HePgRo3ebG8+QQ==
      }
    engines: { node: '>= 16' }
    hasBin: true

  math-intrinsics@1.1.0:
    resolution:
      {
        integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==
      }
    engines: { node: '>= 0.4' }

  md-to-react-email@5.0.5:
    resolution:
      {
        integrity: sha512-OvAXqwq57uOk+WZqFFNCMZz8yDp8BD3WazW1wAKHUrPbbdr89K9DWS6JXY09vd9xNdPNeurI8DU/X4flcfaD8A==
      }
    peerDependencies:
      react: ^18.0 || ^19.0

  md5@2.3.0:
    resolution:
      {
        integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
      }

  mdast-util-from-markdown@2.0.2:
    resolution:
      {
        integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==
      }

  mdast-util-mdx-jsx@3.1.3:
    resolution:
      {
        integrity: sha512-bfOjvNt+1AcbPLTFMFWY149nJz0OjmewJs3LQQ5pIyVGxP4CdOqNVJL6kTaM5c68p8q82Xv3nCyFfUnuEcH3UQ==
      }

  mdast-util-phrasing@4.1.0:
    resolution:
      {
        integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==
      }

  mdast-util-to-markdown@2.1.2:
    resolution:
      {
        integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==
      }

  mdast-util-to-string@4.0.0:
    resolution:
      {
        integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==
      }

  mdn-data@2.0.14:
    resolution:
      {
        integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==
      }

  media-engine@1.0.3:
    resolution:
      {
        integrity: sha512-aa5tG6sDoK+k70B9iEX1NeyfT8ObCKhNDs6lJVpwF6r8vhUfuKMslIcirq6HIUYuuUYLefcEQOn9bSBOvawtwg==
      }

  memoize-one@6.0.0:
    resolution:
      {
        integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==
      }

  memory-pager@1.5.0:
    resolution:
      {
        integrity: sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==
      }

  merge-stream@2.0.0:
    resolution:
      {
        integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==
      }
    engines: { node: '>= 8' }

  merge@2.1.1:
    resolution:
      {
        integrity: sha512-jz+Cfrg9GWOZbQAnDQ4hlVnQky+341Yk5ru8bZSe6sIDTCIg8n9i/u7hSQGSVOF3C7lH6mGtqjkiT9G4wFLL0w==
      }

  micromark-core-commonmark@2.0.2:
    resolution:
      {
        integrity: sha512-FKjQKbxd1cibWMM1P9N+H8TwlgGgSkWZMmfuVucLCHaYqeSvJ0hFeHsIa65pA2nYbes0f8LDHPMrd9X7Ujxg9w==
      }

  micromark-extension-mdx-jsx@3.0.1:
    resolution:
      {
        integrity: sha512-vNuFb9czP8QCtAQcEJn0UJQJZA8Dk6DXKBqx+bg/w0WGuSxDxNr7hErW89tHUY31dUW4NqEOWwmEUNhjTFmHkg==
      }

  micromark-factory-destination@2.0.1:
    resolution:
      {
        integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==
      }

  micromark-factory-label@2.0.1:
    resolution:
      {
        integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==
      }

  micromark-factory-mdx-expression@2.0.2:
    resolution:
      {
        integrity: sha512-5E5I2pFzJyg2CtemqAbcyCktpHXuJbABnsb32wX2U8IQKhhVFBqkcZR5LRm1WVoFqa4kTueZK4abep7wdo9nrw==
      }

  micromark-factory-space@2.0.1:
    resolution:
      {
        integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==
      }

  micromark-factory-title@2.0.1:
    resolution:
      {
        integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==
      }

  micromark-factory-whitespace@2.0.1:
    resolution:
      {
        integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==
      }

  micromark-util-character@2.1.1:
    resolution:
      {
        integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==
      }

  micromark-util-chunked@2.0.1:
    resolution:
      {
        integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==
      }

  micromark-util-classify-character@2.0.1:
    resolution:
      {
        integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==
      }

  micromark-util-combine-extensions@2.0.1:
    resolution:
      {
        integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==
      }

  micromark-util-decode-numeric-character-reference@2.0.2:
    resolution:
      {
        integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==
      }

  micromark-util-decode-string@2.0.1:
    resolution:
      {
        integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==
      }

  micromark-util-encode@2.0.1:
    resolution:
      {
        integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==
      }

  micromark-util-events-to-acorn@2.0.2:
    resolution:
      {
        integrity: sha512-Fk+xmBrOv9QZnEDguL9OI9/NQQp6Hz4FuQ4YmCb/5V7+9eAh1s6AYSvL20kHkD67YIg7EpE54TiSlcsf3vyZgA==
      }

  micromark-util-html-tag-name@2.0.1:
    resolution:
      {
        integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==
      }

  micromark-util-normalize-identifier@2.0.1:
    resolution:
      {
        integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==
      }

  micromark-util-resolve-all@2.0.1:
    resolution:
      {
        integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==
      }

  micromark-util-sanitize-uri@2.0.1:
    resolution:
      {
        integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==
      }

  micromark-util-subtokenize@2.0.4:
    resolution:
      {
        integrity: sha512-N6hXjrin2GTJDe3MVjf5FuXpm12PGm80BrUAeub9XFXca8JZbP+oIwY4LJSVwFUCL1IPm/WwSVUN7goFHmSGGQ==
      }

  micromark-util-symbol@2.0.1:
    resolution:
      {
        integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==
      }

  micromark-util-types@2.0.1:
    resolution:
      {
        integrity: sha512-534m2WhVTddrcKVepwmVEVnUAmtrx9bfIjNoQHRqfnvdaHQiFytEhJoTgpWJvDEXCO5gLTQh3wYC1PgOJA4NSQ==
      }

  micromark@4.0.1:
    resolution:
      {
        integrity: sha512-eBPdkcoCNvYcxQOAKAlceo5SNdzZWfF+FcSupREAzdAh9rRmE239CEQAiTwIgblwnoM8zzj35sZ5ZwvSEOF6Kw==
      }

  micromatch@4.0.8:
    resolution:
      {
        integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
      }
    engines: { node: '>=8.6' }

  mimic-fn@4.0.0:
    resolution:
      {
        integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==
      }
    engines: { node: '>=12' }

  mimic-function@5.0.1:
    resolution:
      {
        integrity: sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==
      }
    engines: { node: '>=18' }

  min-indent@1.0.1:
    resolution:
      {
        integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==
      }
    engines: { node: '>=4' }

  minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
      }

  minimatch@9.0.1:
    resolution:
      {
        integrity: sha512-0jWhJpD/MdhPXwPuiRkCbfYfSKp2qnn2eOc279qI7f+osl/l+prKSrvhg157zSYvx/1nmgn2NqdT6k2Z7zSH9w==
      }
    engines: { node: '>=16 || 14 >=14.17' }

  minimatch@9.0.5:
    resolution:
      {
        integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
      }
    engines: { node: '>=16 || 14 >=14.17' }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
      }

  minipass@3.3.6:
    resolution:
      {
        integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
      }
    engines: { node: '>=8' }

  minipass@5.0.0:
    resolution:
      {
        integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==
      }
    engines: { node: '>=8' }

  minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==
      }
    engines: { node: '>=16 || 14 >=14.17' }

  minizlib@2.1.2:
    resolution:
      {
        integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
      }
    engines: { node: '>= 8' }

  mkdirp@1.0.4:
    resolution:
      {
        integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==
      }
    engines: { node: '>=10' }
    hasBin: true

  mlly@1.7.4:
    resolution:
      {
        integrity: sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==
      }

  monaco-editor@0.52.2:
    resolution:
      {
        integrity: sha512-GEQWEZmfkOGLdd3XK8ryrfWz3AIP8YymVXiPHEdewrUq7mh0qrKrfHLNCXcbB6sTnMLnOZ3ztSiKcciFUkIJwQ==
      }

  mongodb-connection-string-url@3.0.2:
    resolution:
      {
        integrity: sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA==
      }

  mongodb@6.16.0:
    resolution:
      {
        integrity: sha512-D1PNcdT0y4Grhou5Zi/qgipZOYeWrhLEpk33n3nm6LGtz61jvO88WlrWCK/bigMjpnOdAUKKQwsGIl0NtWMyYw==
      }
    engines: { node: '>=16.20.1' }
    peerDependencies:
      '@aws-sdk/credential-providers': ^3.188.0
      '@mongodb-js/zstd': ^1.1.0 || ^2.0.0
      gcp-metadata: ^5.2.0
      kerberos: ^2.0.1
      mongodb-client-encryption: '>=6.0.0 <7'
      snappy: ^7.2.2
      socks: ^2.7.1
    peerDependenciesMeta:
      '@aws-sdk/credential-providers':
        optional: true
      '@mongodb-js/zstd':
        optional: true
      gcp-metadata:
        optional: true
      kerberos:
        optional: true
      mongodb-client-encryption:
        optional: true
      snappy:
        optional: true
      socks:
        optional: true

  mongoose-paginate-v2@1.8.5:
    resolution:
      {
        integrity: sha512-kFxhot+yw9KmpAGSSrF/o+f00aC2uawgNUbhyaM0USS9L7dln1NA77/pLg4lgOaRgXMtfgCENamjqZwIM1Zrig==
      }
    engines: { node: '>=4.0.0' }

  mongoose@8.15.1:
    resolution:
      {
        integrity: sha512-RhQ4DzmBi5BNGcS0w4u1vdMRIKcteXTCNzDt1j7XRcdWYBz1MjMjulBhPaeC5jBCHOD1yinuOFTTSOWLLGexWw==
      }
    engines: { node: '>=16.20.1' }

  motion-dom@12.5.0:
    resolution:
      {
        integrity: sha512-uH2PETDh7m+Hjd1UQQ56yHqwn83SAwNjimNPE/kC+Kds0t4Yh7+29rfo5wezVFpPOv57U4IuWved5d1x0kNhbQ==
      }

  motion-utils@12.5.0:
    resolution:
      {
        integrity: sha512-+hFFzvimn0sBMP9iPxBa9OtRX35ZQ3py0UHnb8U29VD+d8lQ8zH3dTygJWqK7av2v6yhg7scj9iZuvTS0f4+SA==
      }

  motion@12.5.0:
    resolution:
      {
        integrity: sha512-BTAYKszMmTvXSsIyeHNMPSicjWgUA4j7OmZv1xPpthm4sPub3ch66fy9U7BhJ1uXNL3YeprsIegzuvps3FkEMw==
      }
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  mpath@0.9.0:
    resolution:
      {
        integrity: sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==
      }
    engines: { node: '>=4.0.0' }

  mquery@5.0.0:
    resolution:
      {
        integrity: sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==
      }
    engines: { node: '>=14.0.0' }

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
      }

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
      }

  nanoid@3.3.8:
    resolution:
      {
        integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  nanoid@5.1.5:
    resolution:
      {
        integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==
      }
    engines: { node: ^18 || >=20 }
    hasBin: true

  natural-compare@1.4.0:
    resolution:
      {
        integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
      }

  negotiator@1.0.0:
    resolution:
      {
        integrity: sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==
      }
    engines: { node: '>= 0.6' }

  neo-async@2.6.2:
    resolution:
      {
        integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==
      }

  next-intl@3.26.3:
    resolution:
      {
        integrity: sha512-6Y97ODrDsEE1J8cXKMHwg1laLdtkN66QMIqG8BzH4zennJRUNTtM8UMtBDyhfmF6uiZ+xsbWLXmHUgmUymUsfQ==
      }
    peerDependencies:
      next: ^10.0.0 || ^11.0.0 || ^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0

  next-logger@5.0.1:
    resolution:
      {
        integrity: sha512-zWTPtS0YwTB+4iSK4VxUVtCYt+zg8+Sx2Tjbtgmpd4SXsFnWdmCbXAeFZFKtEH8yNlucLCUaj0xqposMQ9rKRg==
      }
    peerDependencies:
      next: '>=9.0.0'
      pino: ^8.0.0 || ^9.0.0
      winston: ^3.0.0
    peerDependenciesMeta:
      pino:
        optional: true
      winston:
        optional: true

  next-themes@0.4.4:
    resolution:
      {
        integrity: sha512-LDQ2qIOJF0VnuVrrMSMLrWGjRMkq+0mpgl6e0juCLqdJ+oo8Q84JRWT6Wh11VDQKkMMe+dVzDKLWs5n87T+PkQ==
      }
    peerDependencies:
      react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
      react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc

  next-tick@1.1.0:
    resolution:
      {
        integrity: sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==
      }

  next@15.3.4:
    resolution:
      {
        integrity: sha512-mHKd50C+mCjam/gcnwqL1T1vPx/XQNFlXqFIVdgQdVAFY9iIQtY0IfaVflEYzKiqjeA7B0cYYMaCrmAYFjs4rA==
      }
    engines: { node: ^18.18.0 || ^19.8.0 || >= 20.0.0 }
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      babel-plugin-react-compiler: '*'
      react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      babel-plugin-react-compiler:
        optional: true
      sass:
        optional: true

  node-fetch-native@1.6.6:
    resolution:
      {
        integrity: sha512-8Mc2HhqPdlIfedsuZoc3yioPuzp6b+L5jRCRY1QzuWZh2EGJVQrGppC6V6cF0bLdbW0+O2YpqCA25aF/1lvipQ==
      }

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-releases@2.0.19:
    resolution:
      {
        integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==
      }

  nodemailer@6.9.16:
    resolution:
      {
        integrity: sha512-psAuZdTIRN08HKVd/E8ObdV6NO7NTBY3KsC30F7M4H1OnmLCUNaS56FpYxyb26zWLSyYF9Ozch9KYHhHegsiOQ==
      }
    engines: { node: '>=6.0.0' }

  nopt@7.2.1:
    resolution:
      {
        integrity: sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==
      }
    engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }
    hasBin: true

  normalize-package-data@2.5.0:
    resolution:
      {
        integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
      }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==
      }
    engines: { node: '>=0.10.0' }

  normalize-range@0.1.2:
    resolution:
      {
        integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==
      }
    engines: { node: '>=0.10.0' }

  normalize-svg-path@1.1.0:
    resolution:
      {
        integrity: sha512-r9KHKG2UUeB5LoTouwDzBy2VxXlHsiM6fyLQvnJa0S5hrhzqElH/CH7TUGhT1fVvIYBIKf3OpY4YJ4CK+iaqHg==
      }

  npm-run-path@5.3.0:
    resolution:
      {
        integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  nypm@0.5.2:
    resolution:
      {
        integrity: sha512-AHzvnyUJYSrrphPhRWWZNcoZfArGNp3Vrc4pm/ZurO74tYNTgAPrEyBQEKy+qioqmWlPXwvMZCG2wOaHlPG0Pw==
      }
    engines: { node: ^14.16.0 || >=16.10.0 }
    hasBin: true

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
      }
    engines: { node: '>=0.10.0' }

  object-hash@3.0.0:
    resolution:
      {
        integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==
      }
    engines: { node: '>= 6' }

  object-inspect@1.13.3:
    resolution:
      {
        integrity: sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==
      }
    engines: { node: '>= 0.4' }

  object-keys@1.1.1:
    resolution:
      {
        integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
      }
    engines: { node: '>= 0.4' }

  object-to-formdata@4.5.1:
    resolution:
      {
        integrity: sha512-QiM9D0NiU5jV6J6tjE1g7b4Z2tcUnKs1OPUi4iMb2zH+7jwlcUrASghgkFk9GtzqNNq8rTQJtT8AzjBAvLoNMw==
      }

  object.assign@4.1.7:
    resolution:
      {
        integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==
      }
    engines: { node: '>= 0.4' }

  object.entries@1.1.8:
    resolution:
      {
        integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
      }
    engines: { node: '>= 0.4' }

  object.fromentries@2.0.8:
    resolution:
      {
        integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
      }
    engines: { node: '>= 0.4' }

  object.groupby@1.0.3:
    resolution:
      {
        integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
      }
    engines: { node: '>= 0.4' }

  object.values@1.2.1:
    resolution:
      {
        integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==
      }
    engines: { node: '>= 0.4' }

  ohash@1.1.4:
    resolution:
      {
        integrity: sha512-FlDryZAahJmEF3VR3w1KogSEdWX3WhA5GPakFx4J81kEAiHyLMpdLLElS8n8dfNadMgAne/MywcvmogzscVt4g==
      }

  on-exit-leak-free@2.1.2:
    resolution:
      {
        integrity: sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==
      }
    engines: { node: '>=14.0.0' }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
      }

  onetime@6.0.0:
    resolution:
      {
        integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
      }
    engines: { node: '>=12' }

  onetime@7.0.0:
    resolution:
      {
        integrity: sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==
      }
    engines: { node: '>=18' }

  optionator@0.9.4:
    resolution:
      {
        integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
      }
    engines: { node: '>= 0.8.0' }

  own-keys@1.0.1:
    resolution:
      {
        integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==
      }
    engines: { node: '>= 0.4' }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
      }
    engines: { node: '>=6' }

  p-limit@3.1.0:
    resolution:
      {
        integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
      }
    engines: { node: '>=10' }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
      }
    engines: { node: '>=8' }

  p-locate@5.0.0:
    resolution:
      {
        integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
      }
    engines: { node: '>=10' }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==
      }
    engines: { node: '>=6' }

  package-json-from-dist@1.0.1:
    resolution:
      {
        integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==
      }

  pagedjs@0.4.3:
    resolution:
      {
        integrity: sha512-YtAN9JAjsQw1142gxEjEAwXvOF5nYQuDwnQ67RW2HZDkMLI+b4RsBE37lULZa9gAr6kDAOGBOhXI4wGMoY3raw==
      }

  pako@0.2.9:
    resolution:
      {
        integrity: sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==
      }

  pako@1.0.11:
    resolution:
      {
        integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==
      }

  parent-module@1.0.1:
    resolution:
      {
        integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
      }
    engines: { node: '>=6' }

  parse-css-color@0.2.1:
    resolution:
      {
        integrity: sha512-bwS/GGIFV3b6KS4uwpzCFj4w297Yl3uqnSgIPsoQkx7GMLROXfMnWvxfNkL0oh8HVhZA4hvJoEoEIqonfJ3BWg==
      }

  parse-entities@4.0.2:
    resolution:
      {
        integrity: sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==
      }

  parse-json@5.2.0:
    resolution:
      {
        integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
      }
    engines: { node: '>=8' }

  parse-passwd@1.0.0:
    resolution:
      {
        integrity: sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q==
      }
    engines: { node: '>=0.10.0' }

  parse-svg-path@0.1.2:
    resolution:
      {
        integrity: sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ==
      }

  parseley@0.12.1:
    resolution:
      {
        integrity: sha512-e6qHKe3a9HWr0oMRVDTRhKce+bRO8VGQR3NyVwcjwrbhMmFCX9KszEV35+rn4AdilFAq9VPxP/Fe1wC9Qjd2lw==
      }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
      }
    engines: { node: '>=8' }

  path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
      }
    engines: { node: '>=0.10.0' }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
      }
    engines: { node: '>=8' }

  path-key@4.0.0:
    resolution:
      {
        integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==
      }
    engines: { node: '>=12' }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
      }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
      }
    engines: { node: '>=16 || 14 >=14.18' }

  path-to-regexp@6.3.0:
    resolution:
      {
        integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==
      }

  path-type@4.0.0:
    resolution:
      {
        integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==
      }
    engines: { node: '>=8' }

  pathe@1.1.2:
    resolution:
      {
        integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==
      }

  pathe@2.0.2:
    resolution:
      {
        integrity: sha512-15Ztpk+nov8DR524R4BF7uEuzESgzUEAV4Ah7CUMNGXdE5ELuvxElxGXndBl32vMSsWa1jpNf22Z+Er3sKwq+w==
      }

  payload@3.50.0:
    resolution:
      {
        integrity: sha512-zIDV+NRjp/unb4rPBp7SxOgc1vTKh/ZvNckMEvUgB4OG3XW7tiI/93CCZkht2kNsK9f+KpZqjsOQ2eFPfQ0DaA==
      }
    engines: { node: ^18.20.2 || >=20.9.0 }
    hasBin: true
    peerDependencies:
      graphql: ^16.8.1

  pdfkit@0.17.1:
    resolution:
      {
        integrity: sha512-Kkf1I9no14O/uo593DYph5u3QwiMfby7JsBSErN1WqeyTgCBNJE3K4pXBn3TgkdKUIVu+buSl4bYUNC+8Up4xg==
      }

  peberminta@0.9.0:
    resolution:
      {
        integrity: sha512-XIxfHpEuSJbITd1H3EeQwpcZbTLHc+VVr8ANI9t5sit565tsI4/xK3KWTUFE2e6QiangUkh3B0jihzmGnNrRsQ==
      }

  peek-readable@5.3.1:
    resolution:
      {
        integrity: sha512-GVlENSDW6KHaXcd9zkZltB7tCLosKB/4Hg0fqBJkAoBgYG2Tn1xtMgXtSUuMU9AK/gCm/tTdT8mgAeF4YNeeqw==
      }
    engines: { node: '>=14.16' }

  perfect-debounce@1.0.0:
    resolution:
      {
        integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==
      }

  picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==
      }
    engines: { node: '>=8.6' }

  picomatch@4.0.2:
    resolution:
      {
        integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==
      }
    engines: { node: '>=12' }

  pidtree@0.6.0:
    resolution:
      {
        integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==
      }
    engines: { node: '>=0.10' }
    hasBin: true

  pify@2.3.0:
    resolution:
      {
        integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==
      }
    engines: { node: '>=0.10.0' }

  pino-abstract-transport@2.0.0:
    resolution:
      {
        integrity: sha512-F63x5tizV6WCh4R6RHyi2Ml+M70DNRXt/+HANowMflpgGFMAym/VKm6G7ZOQRjqN7XbGxK1Lg9t6ZrtzOaivMw==
      }

  pino-pretty@13.0.0:
    resolution:
      {
        integrity: sha512-cQBBIVG3YajgoUjo1FdKVRX6t9XPxwB9lcNJVD5GCnNM4Y6T12YYx8c6zEejxQsU0wrg9TwmDulcE9LR7qcJqA==
      }
    hasBin: true

  pino-std-serializers@7.0.0:
    resolution:
      {
        integrity: sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==
      }

  pino@9.5.0:
    resolution:
      {
        integrity: sha512-xSEmD4pLnV54t0NOUN16yCl7RIB1c5UUOse5HSyEXtBp+FgFQyPeDutc+Q2ZO7/22vImV7VfEjH/1zV2QuqvYw==
      }
    hasBin: true

  pino@9.6.0:
    resolution:
      {
        integrity: sha512-i85pKRCt4qMjZ1+L7sy2Ag4t1atFcdbEt76+7iRJn1g2BvsnRMGu9p8pivl9fs63M2kF/A0OacFZhTub+m/qMg==
      }
    hasBin: true

  pirates@4.0.6:
    resolution:
      {
        integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==
      }
    engines: { node: '>= 6' }

  pkg-types@1.3.1:
    resolution:
      {
        integrity: sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==
      }

  pluralize@8.0.0:
    resolution:
      {
        integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==
      }
    engines: { node: '>=4' }

  png-js@1.0.0:
    resolution:
      {
        integrity: sha512-k+YsbhpA9e+EFfKjTCH3VW6aoKlyNYI6NYdTfDL4CIvFnvsuO84ttonmZE7rc+v23SLTH8XX+5w/Ak9v0xGY4g==
      }

  possible-typed-array-names@1.0.0:
    resolution:
      {
        integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==
      }
    engines: { node: '>= 0.4' }

  postcss-import@15.1.0:
    resolution:
      {
        integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
      }
    engines: { node: '>=14.0.0' }
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution:
      {
        integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
      }
    engines: { node: ^12 || ^14 || >= 16 }
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution:
      {
        integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
      }
    engines: { node: '>= 14' }
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution:
      {
        integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
      }
    engines: { node: '>=12.0' }
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution:
      {
        integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
      }
    engines: { node: '>=4' }

  postcss-value-parser@4.2.0:
    resolution:
      {
        integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==
      }

  postcss@8.4.31:
    resolution:
      {
        integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==
      }
    engines: { node: ^10 || ^12 || >=14 }

  postcss@8.5.1:
    resolution:
      {
        integrity: sha512-6oz2beyjc5VMn/KV1pPw8fliQkhBXrVn1Z3TVyqZxU8kZpzEKhBdmCFqI6ZbmGtamQvQGuU1sgPTk8ZrXDD7jQ==
      }
    engines: { node: ^10 || ^12 || >=14 }

  prelude-ls@1.2.1:
    resolution:
      {
        integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==
      }
    engines: { node: '>= 0.8.0' }

  prettier@3.3.3:
    resolution:
      {
        integrity: sha512-i2tDNA0O5IrMO757lfrdQZCc2jPNDVntV0m/+4whiDfWaTKfMNgR7Qz0NAeGz/nRqF4m5/6CLzbP4/liHt12Ew==
      }
    engines: { node: '>=14' }
    hasBin: true

  prettier@3.4.2:
    resolution:
      {
        integrity: sha512-e9MewbtFo+Fevyuxn/4rrcDAaq0IYxPGLvObpQjiZBMAzB9IGmzlnG9RZy3FFas+eBMu2vA0CszMeduow5dIuQ==
      }
    engines: { node: '>=14' }
    hasBin: true

  prismjs@1.29.0:
    resolution:
      {
        integrity: sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q==
      }
    engines: { node: '>=6' }

  prismjs@1.30.0:
    resolution:
      {
        integrity: sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==
      }
    engines: { node: '>=6' }

  process-warning@4.0.1:
    resolution:
      {
        integrity: sha512-3c2LzQ3rY9d0hc1emcsHhfT9Jwz0cChib/QN89oME2R451w5fy3f0afAhERFZAwrbDU43wk12d0ORBpDVME50Q==
      }

  prompts@2.4.2:
    resolution:
      {
        integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
      }
    engines: { node: '>= 6' }

  prop-types@15.8.1:
    resolution:
      {
        integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
      }

  proto-list@1.2.4:
    resolution:
      {
        integrity: sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==
      }

  pump@3.0.2:
    resolution:
      {
        integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==
      }
    engines: { node: '>=6' }

  qs-esm@7.0.2:
    resolution:
      {
        integrity: sha512-D8NAthKSD7SGn748v+GLaaO6k08Mvpoqroa35PqIQC4gtUa8/Pb/k+r0m0NnGBVbHDP1gKZ2nVywqfMisRhV5A==
      }
    engines: { node: '>=18' }

  qs@6.14.0:
    resolution:
      {
        integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==
      }
    engines: { node: '>=0.6' }

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
      }

  queue@6.0.2:
    resolution:
      {
        integrity: sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==
      }

  quick-format-unescaped@4.0.4:
    resolution:
      {
        integrity: sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==
      }

  range-parser@1.2.1:
    resolution:
      {
        integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==
      }
    engines: { node: '>= 0.6' }

  rc9@2.1.2:
    resolution:
      {
        integrity: sha512-btXCnMmRIBINM2LDZoEmOogIZU7Qe7zn4BpomSKZ/ykbLObuBdvG+mFq11DL6fjH1DRwHhrlgtYWG96bJiC7Cg==
      }

  react-datepicker@7.6.0:
    resolution:
      {
        integrity: sha512-9cQH6Z/qa4LrGhzdc3XoHbhrxNcMi9MKjZmYgF/1MNNaJwvdSjv3Xd+jjvrEEbKEf71ZgCA3n7fQbdwd70qCRw==
      }
    peerDependencies:
      react: ^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc

  react-dom@19.0.0:
    resolution:
      {
        integrity: sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==
      }
    peerDependencies:
      react: ^19.0.0

  react-error-boundary@3.1.4:
    resolution:
      {
        integrity: sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA==
      }
    engines: { node: '>=10', npm: '>=6' }
    peerDependencies:
      react: '>=16.13.1'

  react-error-boundary@4.1.2:
    resolution:
      {
        integrity: sha512-GQDxZ5Jd+Aq/qUxbCm1UtzmL/s++V7zKgE8yMktJiCQXCCFZnMZh9ng+6/Ne6PjNSXH0L9CjeOEREfRnq6Duag==
      }
    peerDependencies:
      react: '>=16.13.1'

  react-hook-form@7.57.0:
    resolution:
      {
        integrity: sha512-RbEks3+cbvTP84l/VXGUZ+JMrKOS8ykQCRYdm5aYsxnDquL0vspsyNhGRO7pcH6hsZqWlPOjLye7rJqdtdAmlg==
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19

  react-hotkeys-hook@4.6.1:
    resolution:
      {
        integrity: sha512-XlZpbKUj9tkfgPgT9gA+1p7Ey6vFIZHttUjPqpTdyT5nqQ8mHL7elxvSbaC+dpSiHUSmr21Ya1mDxBZG3aje4Q==
      }
    peerDependencies:
      react: '>=16.8.1'
      react-dom: '>=16.8.1'

  react-image-crop@10.1.8:
    resolution:
      {
        integrity: sha512-4rb8XtXNx7ZaOZarKKnckgz4xLMvds/YrU6mpJfGhGAsy2Mg4mIw1x+DCCGngVGq2soTBVVOxx2s/C6mTX9+pA==
      }
    peerDependencies:
      react: '>=16.13.1'

  react-is@16.13.1:
    resolution:
      {
        integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==
      }

  react-promise-suspense@0.3.4:
    resolution:
      {
        integrity: sha512-I42jl7L3Ze6kZaq+7zXWSunBa3b1on5yfvUW6Eo/3fFOj6dZ5Bqmcd264nJbTK/gn1HjjILAjSwnZbV4RpSaNQ==
      }

  react-property@2.0.2:
    resolution:
      {
        integrity: sha512-+PbtI3VuDV0l6CleQMsx2gtK0JZbZKbpdu5ynr+lbsuvtmgbNcS3VM0tuY2QjFNOcWxvXeHjDpy42RO+4U2rug==
      }

  react-remove-scroll-bar@2.3.8:
    resolution:
      {
        integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.6.3:
    resolution:
      {
        integrity: sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-select@5.9.0:
    resolution:
      {
        integrity: sha512-nwRKGanVHGjdccsnzhFte/PULziueZxGD8LL2WojON78Mvnq7LdAMEtu2frrwld1fr3geixg3iiMBIc/LLAZpw==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  react-style-singleton@2.2.3:
    resolution:
      {
        integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-transition-group@4.4.5:
    resolution:
      {
        integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
      }
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@19.0.0:
    resolution:
      {
        integrity: sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==
      }
    engines: { node: '>=0.10.0' }

  read-cache@1.0.0:
    resolution:
      {
        integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
      }

  read-pkg-up@7.0.1:
    resolution:
      {
        integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==
      }
    engines: { node: '>=8' }

  read-pkg@5.2.0:
    resolution:
      {
        integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
      }
    engines: { node: '>=8' }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
      }
    engines: { node: '>= 6' }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
      }
    engines: { node: '>=8.10.0' }

  readdirp@4.1.1:
    resolution:
      {
        integrity: sha512-h80JrZu/MHUZCyHu5ciuoI0+WxsCxzxJTILn6Fs8rxSnFPh+UVHYfeIxK1nVGugMqkfC4vJcBOYbkfkwYK0+gw==
      }
    engines: { node: '>= 14.18.0' }

  real-require@0.2.0:
    resolution:
      {
        integrity: sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==
      }
    engines: { node: '>= 12.13.0' }

  reflect.getprototypeof@1.0.10:
    resolution:
      {
        integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==
      }
    engines: { node: '>= 0.4' }

  regenerator-runtime@0.13.11:
    resolution:
      {
        integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==
      }

  regenerator-runtime@0.14.1:
    resolution:
      {
        integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==
      }

  regexp-tree@0.1.27:
    resolution:
      {
        integrity: sha512-iETxpjK6YoRWJG5o6hXLwvjYAoW+FEZn9os0PD/b6AP6xQwsa/Y7lCVgIixBbUPMfhu+i2LtdeAqVTgGlQarfA==
      }
    hasBin: true

  regexp.prototype.flags@1.5.4:
    resolution:
      {
        integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
      }
    engines: { node: '>= 0.4' }

  regjsparser@0.10.0:
    resolution:
      {
        integrity: sha512-qx+xQGZVsy55CH0a1hiVwHmqjLryfh7wQyF5HO07XJ9f7dQMY/gPQHhlyDkIzJKC+x2fUCpCcUODUUUFrm7SHA==
      }
    hasBin: true

  require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==
      }
    engines: { node: '>=0.10.0' }

  resend@4.1.1:
    resolution:
      {
        integrity: sha512-nkcRpIOgPb3sFPA/GyOTr6Vmlrkhwsu+XlC20kKbbebOfw0WbAjbBbJ1m4AcjKOkPf57O4DH9Bnbxi9i18JYng==
      }
    engines: { node: '>=18' }

  resolve-dir@1.0.1:
    resolution:
      {
        integrity: sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==
      }
    engines: { node: '>=0.10.0' }

  resolve-from@4.0.0:
    resolution:
      {
        integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
      }
    engines: { node: '>=4' }

  resolve-pkg-maps@1.0.0:
    resolution:
      {
        integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==
      }

  resolve@1.22.10:
    resolution:
      {
        integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
      }
    engines: { node: '>= 0.4' }
    hasBin: true

  resolve@2.0.0-next.5:
    resolution:
      {
        integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
      }
    hasBin: true

  restore-cursor@5.1.0:
    resolution:
      {
        integrity: sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==
      }
    engines: { node: '>=18' }

  restructure@3.0.2:
    resolution:
      {
        integrity: sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==
      }

  reusify@1.0.4:
    resolution:
      {
        integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
      }
    engines: { iojs: '>=1.0.0', node: '>=0.10.0' }

  rfdc@1.4.1:
    resolution:
      {
        integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==
      }

  rimraf@3.0.2:
    resolution:
      {
        integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
      }
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
      }

  safe-array-concat@1.1.3:
    resolution:
      {
        integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==
      }
    engines: { node: '>=0.4' }

  safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==
      }

  safe-push-apply@1.0.0:
    resolution:
      {
        integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==
      }
    engines: { node: '>= 0.4' }

  safe-regex-test@1.1.0:
    resolution:
      {
        integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
      }
    engines: { node: '>= 0.4' }

  safe-stable-stringify@2.5.0:
    resolution:
      {
        integrity: sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==
      }
    engines: { node: '>=10' }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
      }

  sanitize-filename@1.6.3:
    resolution:
      {
        integrity: sha512-y/52Mcy7aw3gRm7IrcGDFx/bCk4AhRh2eI9luHOQM86nZsqwiRkkq2GekHXBBD+SmPidc8i2PqtYZl+pWJ8Oeg==
      }

  sass@1.77.4:
    resolution:
      {
        integrity: sha512-vcF3Ckow6g939GMA4PeU7b2K/9FALXk2KF9J87txdHzXbUF9XRQRwSxcAs/fGaTnJeBFd7UoV22j3lzMLdM0Pw==
      }
    engines: { node: '>=14.0.0' }
    hasBin: true

  satori@0.15.2:
    resolution:
      {
        integrity: sha512-vu/49vdc8MzV5jUchs3TIRDCOkOvMc1iJ11MrZvhg9tE4ziKIEIBjBZvies6a9sfM2vQ2gc3dXeu6rCK7AztHA==
      }
    engines: { node: '>=16' }

  scheduler@0.25.0:
    resolution:
      {
        integrity: sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==
      }

  scheduler@0.25.0-rc-603e6108-20241029:
    resolution:
      {
        integrity: sha512-pFwF6H1XrSdYYNLfOcGlM28/j8CGLu8IvdrxqhjWULe2bPcKiKW4CV+OWqR/9fT52mywx65l7ysNkjLKBda7eA==
      }

  scmp@2.1.0:
    resolution:
      {
        integrity: sha512-o/mRQGk9Rcer/jEEw/yw4mwo3EU/NvYvp577/Btqrym9Qy5/MdWGBqipbALgd2lrdWTJ5/gqDusxfnQBxOxT2Q==
      }

  secure-json-parse@2.7.0:
    resolution:
      {
        integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==
      }

  selderee@0.11.0:
    resolution:
      {
        integrity: sha512-5TF+l7p4+OsnP8BCCvSyZiSPc4x4//p5uPwK8TCnVPJYRmU2aYKMpOXvw8zM5a5JvuuCGN1jmsMwuU2W02ukfA==
      }

  semver@5.7.2:
    resolution:
      {
        integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==
      }
    hasBin: true

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==
      }
    hasBin: true

  semver@7.7.0:
    resolution:
      {
        integrity: sha512-DrfFnPzblFmNrIZzg5RzHegbiRWg7KMR7btwi2yjHwx06zsUbO5g613sVwEV7FTwmzJu+Io0lJe2GJ3LxqpvBQ==
      }
    engines: { node: '>=10' }
    hasBin: true

  semver@7.7.2:
    resolution:
      {
        integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==
      }
    engines: { node: '>=10' }
    hasBin: true

  set-function-length@1.2.2:
    resolution:
      {
        integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
      }
    engines: { node: '>= 0.4' }

  set-function-name@2.0.2:
    resolution:
      {
        integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
      }
    engines: { node: '>= 0.4' }

  set-proto@1.0.0:
    resolution:
      {
        integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==
      }
    engines: { node: '>= 0.4' }

  sharp@0.33.5:
    resolution:
      {
        integrity: sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }

  sharp@0.34.2:
    resolution:
      {
        integrity: sha512-lszvBmB9QURERtyKT2bNmsgxXK0ShJrL/fvqlonCo7e6xBF8nT8xU6pW+PMIbLsz0RxQk3rgH9kd8UmvOzlMJg==
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
      }
    engines: { node: '>=8' }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
      }
    engines: { node: '>=8' }

  side-channel-list@1.0.0:
    resolution:
      {
        integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
      }
    engines: { node: '>= 0.4' }

  side-channel-map@1.0.1:
    resolution:
      {
        integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
      }
    engines: { node: '>= 0.4' }

  side-channel-weakmap@1.0.2:
    resolution:
      {
        integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
      }
    engines: { node: '>= 0.4' }

  side-channel@1.1.0:
    resolution:
      {
        integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
      }
    engines: { node: '>= 0.4' }

  sift@17.1.3:
    resolution:
      {
        integrity: sha512-Rtlj66/b0ICeFzYTuNvX/EF1igRbbnGSvEyT79McoZa/DeGhMyC5pWKOEsZKnpkqtSeovd5FL/bjHWC3CIIvCQ==
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==
      }
    engines: { node: '>=14' }

  simple-swizzle@0.2.2:
    resolution:
      {
        integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
      }

  simple-wcswidth@1.0.1:
    resolution:
      {
        integrity: sha512-xMO/8eNREtaROt7tJvWJqHBDTMFN4eiQ5I4JRMuilwfnFcV5W9u7RUkueNkdw0jPqGMX36iCywelS5yilTuOxg==
      }

  sisteransi@1.0.5:
    resolution:
      {
        integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==
      }

  slice-ansi@5.0.0:
    resolution:
      {
        integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==
      }
    engines: { node: '>=12' }

  slice-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==
      }
    engines: { node: '>=18' }

  sonic-boom@4.2.0:
    resolution:
      {
        integrity: sha512-INb7TM37/mAcsGmc9hyyI6+QR3rR1zVRu36B0NeGXKnOOLiZOfER5SA+N7X7k3yUYRzLWafduTDvJAfDswwEww==
      }

  sonner@1.7.4:
    resolution:
      {
        integrity: sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==
      }
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==
      }
    engines: { node: '>=0.10.0' }

  source-map@0.5.7:
    resolution:
      {
        integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==
      }
    engines: { node: '>=0.10.0' }

  source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
      }
    engines: { node: '>=0.10.0' }

  sparse-bitfield@3.0.3:
    resolution:
      {
        integrity: sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
      }

  spdx-correct@3.2.0:
    resolution:
      {
        integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
      }

  spdx-exceptions@2.5.0:
    resolution:
      {
        integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==
      }

  spdx-expression-parse@3.0.1:
    resolution:
      {
        integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
      }

  spdx-license-ids@3.0.21:
    resolution:
      {
        integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==
      }

  split2@4.2.0:
    resolution:
      {
        integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==
      }
    engines: { node: '>= 10.x' }

  stable-hash@0.0.4:
    resolution:
      {
        integrity: sha512-LjdcbuBeLcdETCrPn9i8AYAZ1eCtu4ECAWtP7UleOiZ9LzVxRzzUZEoZ8zB24nhkQnDWyET0I+3sWokSDS3E7g==
      }

  state-local@1.0.7:
    resolution:
      {
        integrity: sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==
      }

  stream-browserify@3.0.0:
    resolution:
      {
        integrity: sha512-H73RAHsVBapbim0tU2JwwOiXUj+fikfiaoYAKHF3VJfA0pe2BCzkhAHBlLG6REzE+2WNZcxOXjK7lkso+9euLA==
      }

  streamsearch@1.1.0:
    resolution:
      {
        integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==
      }
    engines: { node: '>=10.0.0' }

  string-argv@0.3.2:
    resolution:
      {
        integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==
      }
    engines: { node: '>=0.6.19' }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
      }
    engines: { node: '>=8' }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
      }
    engines: { node: '>=12' }

  string-width@7.2.0:
    resolution:
      {
        integrity: sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==
      }
    engines: { node: '>=18' }

  string.prototype.codepointat@0.2.1:
    resolution:
      {
        integrity: sha512-2cBVCj6I4IOvEnjgO/hWqXjqBGsY+zwPmHl12Srk9IXSZ56Jwwmy+66XO5Iut/oQVR7t5ihYdLB0GMa4alEUcg==
      }

  string.prototype.includes@2.0.1:
    resolution:
      {
        integrity: sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==
      }
    engines: { node: '>= 0.4' }

  string.prototype.matchall@4.0.12:
    resolution:
      {
        integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==
      }
    engines: { node: '>= 0.4' }

  string.prototype.repeat@1.0.0:
    resolution:
      {
        integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==
      }

  string.prototype.trim@1.2.10:
    resolution:
      {
        integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==
      }
    engines: { node: '>= 0.4' }

  string.prototype.trimend@1.0.9:
    resolution:
      {
        integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==
      }
    engines: { node: '>= 0.4' }

  string.prototype.trimstart@1.0.8:
    resolution:
      {
        integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
      }
    engines: { node: '>= 0.4' }

  string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
      }

  stringify-entities@4.0.4:
    resolution:
      {
        integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==
      }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
      }
    engines: { node: '>=8' }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
      }
    engines: { node: '>=12' }

  strip-bom@3.0.0:
    resolution:
      {
        integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==
      }
    engines: { node: '>=4' }

  strip-final-newline@3.0.0:
    resolution:
      {
        integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==
      }
    engines: { node: '>=12' }

  strip-indent@3.0.0:
    resolution:
      {
        integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==
      }
    engines: { node: '>=8' }

  strip-json-comments@3.1.1:
    resolution:
      {
        integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
      }
    engines: { node: '>=8' }

  stripe@10.17.0:
    resolution:
      {
        integrity: sha512-JHV2KoL+nMQRXu3m9ervCZZvi4DDCJfzHUE6CmtJxR9TmizyYfrVuhGvnsZLLnheby9Qrnf4Hq6iOEcejGwnGQ==
      }
    engines: { node: ^8.1 || >=10.* }

  stripe@18.3.0:
    resolution:
      {
        integrity: sha512-FkxrTUUcWB4CVN2yzgsfF/YHD6WgYHduaa7VmokCy5TLCgl5UNJkwortxcedrxSavQ8Qfa4Ir4JxcbIYiBsyLg==
      }
    engines: { node: '>=12.*' }
    peerDependencies:
      '@types/node': '>=12.x.x'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  strnum@1.0.5:
    resolution:
      {
        integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==
      }

  strnum@2.1.1:
    resolution:
      {
        integrity: sha512-7ZvoFTiCnGxBtDqJ//Cu6fWtZtc7Y3x+QOirG15wztbdngGSkht27o2pyGWrVy0b4WAy3jbKmnoK6g5VlVNUUw==
      }

  strtok3@8.1.0:
    resolution:
      {
        integrity: sha512-ExzDvHYPj6F6QkSNe/JxSlBxTh3OrI6wrAIz53ulxo1c4hBJ1bT9C/JrAthEKHWG9riVH3Xzg7B03Oxty6S2Lw==
      }
    engines: { node: '>=16' }

  style-to-js@1.1.16:
    resolution:
      {
        integrity: sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==
      }

  style-to-object@1.0.8:
    resolution:
      {
        integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==
      }

  styled-jsx@5.1.6:
    resolution:
      {
        integrity: sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==
      }
    engines: { node: '>= 12.0.0' }
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true

  stylis@4.2.0:
    resolution:
      {
        integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==
      }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
      }
    engines: { node: '>=16 || 14 >=14.17' }
    hasBin: true

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
      }
    engines: { node: '>=8' }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
      }
    engines: { node: '>= 0.4' }

  svg-arc-to-cubic-bezier@3.2.0:
    resolution:
      {
        integrity: sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g==
      }

  tabbable@6.2.0:
    resolution:
      {
        integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==
      }

  tailwind-merge@2.6.0:
    resolution:
      {
        integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==
      }

  tailwindcss-animate@1.0.7:
    resolution:
      {
        integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==
      }
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'

  tailwindcss@3.4.17:
    resolution:
      {
        integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==
      }
    engines: { node: '>=14.0.0' }
    hasBin: true

  tapable@2.2.1:
    resolution:
      {
        integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==
      }
    engines: { node: '>=6' }

  tar@6.2.1:
    resolution:
      {
        integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
      }
    engines: { node: '>=10' }

  text-table@0.2.0:
    resolution:
      {
        integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
      }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
      }
    engines: { node: '>=0.8' }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
      }

  thread-stream@3.1.0:
    resolution:
      {
        integrity: sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==
      }

  tiny-cookie@2.5.1:
    resolution:
      {
        integrity: sha512-w8FlBk1LMEBA7NY1lreQu4iTma/jxRQaKc2ucXFIv9/pDDPcnFrgO4swahGFAwJebHjwrHTyeoI7moH7G9YzSg==
      }

  tiny-inflate@1.0.3:
    resolution:
      {
        integrity: sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==
      }

  tinyexec@0.3.2:
    resolution:
      {
        integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==
      }

  tinyglobby@0.2.10:
    resolution:
      {
        integrity: sha512-Zc+8eJlFMvgatPZTl6A9L/yht8QqdmUNtURHaKZLmKBE12hNPSrqNkUp2cs3M/UKmNVVAMFQYSjYIVHDjW5zew==
      }
    engines: { node: '>=12.0.0' }

  to-fast-properties@2.0.0:
    resolution:
      {
        integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==
      }
    engines: { node: '>=4' }

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
      }
    engines: { node: '>=8.0' }

  token-types@6.0.0:
    resolution:
      {
        integrity: sha512-lbDrTLVsHhOMljPscd0yitpozq7Ga2M5Cvez5AjGg8GASBjtt6iERCAJ93yommPmz62fb45oFIXHEZ3u9bfJEA==
      }
    engines: { node: '>=14.16' }

  tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==
      }

  tr46@5.0.0:
    resolution:
      {
        integrity: sha512-tk2G5R2KRwBd+ZN0zaEXpmzdKyOYksXwywulIX95MBODjSzMIuQnQ3m8JxgbhnL1LeVo7lqQKsYa1O3Htl7K5g==
      }
    engines: { node: '>=18' }

  truncate-utf8-bytes@1.0.2:
    resolution:
      {
        integrity: sha512-95Pu1QXQvruGEhv62XCMO3Mm90GscOCClvrIUwCM0PYOXK3kaF3l3sIHxx71ThJfcbM2O5Au6SO3AWCSEfW4mQ==
      }

  ts-api-utils@2.0.0:
    resolution:
      {
        integrity: sha512-xCt/TOAc+EOHS1XPnijD3/yzpH6qg2xppZO1YDqGoVsNXfQfzHpOdNuXwrwOU8u4ITXJyDCTyt8w5g1sZv9ynQ==
      }
    engines: { node: '>=18.12' }
    peerDependencies:
      typescript: '>=4.8.4'

  ts-essentials@10.0.3:
    resolution:
      {
        integrity: sha512-/FrVAZ76JLTWxJOERk04fm8hYENDo0PWSP3YLQKxevLwWtxemGcl5JJEzN4iqfDlRve0ckyfFaOBu4xbNH/wZw==
      }
    peerDependencies:
      typescript: '>=4.5.0'
    peerDependenciesMeta:
      typescript:
        optional: true

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==
      }

  tsconfig-paths@3.15.0:
    resolution:
      {
        integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
      }

  tslib@1.14.1:
    resolution:
      {
        integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==
      }

  tslib@2.8.1:
    resolution:
      {
        integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==
      }

  tsx@4.19.2:
    resolution:
      {
        integrity: sha512-pOUl6Vo2LUq/bSa8S5q7b91cgNSjctn9ugq/+Mvow99qW6x/UZYwzxy/3NmqoT66eHYfCVvFvACC58UBPFf28g==
      }
    engines: { node: '>=18.0.0' }
    hasBin: true

  tsx@4.20.3:
    resolution:
      {
        integrity: sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==
      }
    engines: { node: '>=18.0.0' }
    hasBin: true

  type-check@0.4.0:
    resolution:
      {
        integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
      }
    engines: { node: '>= 0.8.0' }

  type-fest@0.20.2:
    resolution:
      {
        integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==
      }
    engines: { node: '>=10' }

  type-fest@0.6.0:
    resolution:
      {
        integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==
      }
    engines: { node: '>=8' }

  type-fest@0.8.1:
    resolution:
      {
        integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==
      }
    engines: { node: '>=8' }

  type@2.7.3:
    resolution:
      {
        integrity: sha512-8j+1QmAbPvLZow5Qpi6NCaN8FB60p/6x8/vfNqOk/hC+HuvFZhL4+WfekuhQLiqFZXOgQdrs3B+XxEmCc6b3FQ==
      }

  typed-array-buffer@1.0.3:
    resolution:
      {
        integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==
      }
    engines: { node: '>= 0.4' }

  typed-array-byte-length@1.0.3:
    resolution:
      {
        integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==
      }
    engines: { node: '>= 0.4' }

  typed-array-byte-offset@1.0.4:
    resolution:
      {
        integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==
      }
    engines: { node: '>= 0.4' }

  typed-array-length@1.0.7:
    resolution:
      {
        integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==
      }
    engines: { node: '>= 0.4' }

  types-react-dom@19.0.0-rc.1:
    resolution:
      {
        integrity: sha512-VSLZJl8VXCD0fAWp7DUTFUDCcZ8DVXOQmjhJMD03odgeFmu14ZQJHCXeETm3BEAhJqfgJaFkLnGkQv88sRx0fQ==
      }

  types-react@19.0.0-rc.1:
    resolution:
      {
        integrity: sha512-RshndUfqTW6K3STLPis8BtAYCGOkMbtvYsi90gmVNDZBXUyUc5juf2PE9LfS/JmOlUIRO8cWTS/1MTnmhjDqyQ==
      }

  typescript@5.6.3:
    resolution:
      {
        integrity: sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==
      }
    engines: { node: '>=14.17' }
    hasBin: true

  ufo@1.5.4:
    resolution:
      {
        integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==
      }

  uglify-js@3.19.3:
    resolution:
      {
        integrity: sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ==
      }
    engines: { node: '>=0.8.0' }
    hasBin: true

  uint8array-extras@1.4.0:
    resolution:
      {
        integrity: sha512-ZPtzy0hu4cZjv3z5NW9gfKnNLjoz4y6uv4HlelAjDK7sY/xOkKZv9xK/WQpcsBB3jEybChz9DPC2U/+cusjJVQ==
      }
    engines: { node: '>=18' }

  unbox-primitive@1.1.0:
    resolution:
      {
        integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==
      }
    engines: { node: '>= 0.4' }

  undici-types@6.20.0:
    resolution:
      {
        integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==
      }

  undici@7.10.0:
    resolution:
      {
        integrity: sha512-u5otvFBOBZvmdjWLVW+5DAc9Nkq8f24g0O9oY7qw2JVIF1VocIFoyz9JFkuVOS2j41AufeO0xnlweJ2RLT8nGw==
      }
    engines: { node: '>=20.18.1' }

  unfetch@4.2.0:
    resolution:
      {
        integrity: sha512-F9p7yYCn6cIW9El1zi0HI6vqpeIvBsr3dSuRO6Xuppb1u5rXpCPmMvLSyECLhybr9isec8Ohl0hPekMVrEinDA==
      }

  unicode-properties@1.4.1:
    resolution:
      {
        integrity: sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==
      }

  unicode-trie@2.0.0:
    resolution:
      {
        integrity: sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==
      }

  unist-util-is@6.0.0:
    resolution:
      {
        integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==
      }

  unist-util-position-from-estree@2.0.0:
    resolution:
      {
        integrity: sha512-KaFVRjoqLyF6YXCbVLNad/eS4+OfPQQn2yOd7zF/h5T/CSL2v8NpN6a5TPvtbXthAGw5nG+PuTtq+DdIZr+cRQ==
      }

  unist-util-stringify-position@4.0.0:
    resolution:
      {
        integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==
      }

  unist-util-visit-parents@6.0.1:
    resolution:
      {
        integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==
      }

  unist-util-visit@5.0.0:
    resolution:
      {
        integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==
      }

  update-browserslist-db@1.1.2:
    resolution:
      {
        integrity: sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg==
      }
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution:
      {
        integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
      }

  use-callback-ref@1.3.3:
    resolution:
      {
        integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-context-selector@2.0.0:
    resolution:
      {
        integrity: sha512-owfuSmUNd3eNp3J9CdDl0kMgfidV+MkDvHPpvthN5ThqM+ibMccNE0k+Iq7TWC6JPFvGZqanqiGCuQx6DyV24g==
      }
    peerDependencies:
      react: '>=18.0.0'
      scheduler: '>=0.19.0'

  use-intl@3.26.3:
    resolution:
      {
        integrity: sha512-yY0a2YseO17cKwHA9M6fcpiEJ2Uo81DEU0NOUxNTp6lJVNOuI6nULANPVVht6IFdrYFtlsMmMoc97+Eq9/Tnng==
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0

  use-isomorphic-layout-effect@1.2.0:
    resolution:
      {
        integrity: sha512-q6ayo8DWoPZT0VdG4u3D3uxcgONP3Mevx2i2b0434cwWBoL+aelL1DzkXI6w3PhTZzUeR2kaVlZn70iCiseP6w==
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution:
      {
        integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  utf8-byte-length@1.0.5:
    resolution:
      {
        integrity: sha512-Xn0w3MtiQ6zoz2vFyUVruaCL53O/DwUvkEeOvj+uulMm0BkUGYWmBYVyElqZaSLhY6ZD0ulfU3aBra2aVT4xfA==
      }

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
      }

  uuid@10.0.0:
    resolution:
      {
        integrity: sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==
      }
    hasBin: true

  uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==
      }
    hasBin: true

  validate-npm-package-license@3.0.4:
    resolution:
      {
        integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
      }

  validator@13.12.0:
    resolution:
      {
        integrity: sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==
      }
    engines: { node: '>= 0.10' }

  vaul@1.1.2:
    resolution:
      {
        integrity: sha512-ZFkClGpWyI2WUQjdLJ/BaGuV6AVQiJ3uELGk3OYtP+B6yCO7Cmn9vPFXVJkRaGkOJu3m8bQMgtyzNHixULceQA==
      }
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc

  vfile-message@4.0.2:
    resolution:
      {
        integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==
      }

  vite-compatible-readable-stream@3.6.1:
    resolution:
      {
        integrity: sha512-t20zYkrSf868+j/p31cRIGN28Phrjm3nRSLR2fyc2tiWi4cZGVdv68yNlwnIINTkMTmPoMiSlc0OadaO7DXZaQ==
      }
    engines: { node: '>= 6' }

  webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==
      }

  webidl-conversions@7.0.0:
    resolution:
      {
        integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==
      }
    engines: { node: '>=12' }

  whatwg-url@14.1.0:
    resolution:
      {
        integrity: sha512-jlf/foYIKywAt3x/XWKZ/3rz8OSJPiWktjmk891alJUEjiVxKX9LEO92qH3hv4aJ0mN3MWPvGMCy8jQi95xK4w==
      }
    engines: { node: '>=18' }

  whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
      }

  which-boxed-primitive@1.1.1:
    resolution:
      {
        integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==
      }
    engines: { node: '>= 0.4' }

  which-builtin-type@1.2.1:
    resolution:
      {
        integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==
      }
    engines: { node: '>= 0.4' }

  which-collection@1.0.2:
    resolution:
      {
        integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
      }
    engines: { node: '>= 0.4' }

  which-typed-array@1.1.18:
    resolution:
      {
        integrity: sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==
      }
    engines: { node: '>= 0.4' }

  which@1.3.1:
    resolution:
      {
        integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
      }
    hasBin: true

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
      }
    engines: { node: '>= 8' }
    hasBin: true

  word-wrap@1.2.5:
    resolution:
      {
        integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==
      }
    engines: { node: '>=0.10.0' }

  wordwrap@1.0.0:
    resolution:
      {
        integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==
      }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
      }
    engines: { node: '>=10' }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
      }
    engines: { node: '>=12' }

  wrap-ansi@9.0.0:
    resolution:
      {
        integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==
      }
    engines: { node: '>=18' }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
      }

  ws@8.18.0:
    resolution:
      {
        integrity: sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==
      }
    engines: { node: '>=10.0.0' }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xss@1.0.15:
    resolution:
      {
        integrity: sha512-FVdlVVC67WOIPvfOwhoMETV72f6GbW7aOabBC3WxN/oUdoEMDyLz4OgRv5/gck2ZeNqEQu+Tb0kloovXOfpYVg==
      }
    engines: { node: '>= 0.10.0' }
    hasBin: true

  yallist@4.0.0:
    resolution:
      {
        integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
      }

  yaml@1.10.2:
    resolution:
      {
        integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==
      }
    engines: { node: '>= 6' }

  yaml@2.7.0:
    resolution:
      {
        integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==
      }
    engines: { node: '>= 14' }
    hasBin: true

  yjs@13.6.23:
    resolution:
      {
        integrity: sha512-ExtnT5WIOVpkL56bhLeisG/N5c4fmzKn4k0ROVfJa5TY2QHbH7F0Wu2T5ZhR7ErsFWQEFafyrnSI8TPKVF9Few==
      }
    engines: { node: '>=16.0.0', npm: '>=8.0.0' }

  yocto-queue@0.1.0:
    resolution:
      {
        integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
      }
    engines: { node: '>=10' }

  yoga-layout@3.2.1:
    resolution:
      {
        integrity: sha512-0LPOt3AxKqMdFBZA3HBAt/t/8vIKq7VaQYbuA8WxCgung+p9TVyKRYdpvCb80HcdTN2NkbIKbhNwKUfm3tQywQ==
      }

  yoga-wasm-web@0.3.3:
    resolution:
      {
        integrity: sha512-N+d4UJSJbt/R3wqY7Coqs5pcV0aUj2j9IaQ3rNj9bVCLld8tTGKRa2USARjnvZJWVx1NDmQev8EknoczaOQDOA==
      }

  zod@3.24.1:
    resolution:
      {
        integrity: sha512-muH7gBL9sI1nciMZV67X5fTKKBLtwpZ5VBp1vsOQzj1MhrBZ4wlVCm3gedKZWLp0Oyel8sIGfeiz54Su+OVT+A==
      }

  zwitch@2.0.4:
    resolution:
      {
        integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==
      }

snapshots:
  '@alloc/quick-lru@5.2.0': {}

  '@apidevtools/json-schema-ref-parser@11.7.2':
    dependencies:
      '@jsdevtools/ono': 7.1.3
      '@types/json-schema': 7.0.15
      js-yaml: 4.1.0

  '@apidevtools/json-schema-ref-parser@11.9.0':
    dependencies:
      '@jsdevtools/ono': 7.1.3
      '@types/json-schema': 7.0.15
      js-yaml: 4.1.0

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.840.0
      tslib: 2.8.1

  '@aws-crypto/crc32c@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.840.0
      tslib: 2.8.1

  '@aws-crypto/sha1-browser@5.2.0':
    dependencies:
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/util-locate-window': 3.723.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/util-locate-window': 3.723.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-js@1.2.2':
    dependencies:
      '@aws-crypto/util': 1.2.2
      '@aws-sdk/types': 3.840.0
      tslib: 1.14.1

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.840.0
      tslib: 2.8.1

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.8.1

  '@aws-crypto/util@1.2.2':
    dependencies:
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.840.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-sdk/client-cognito-identity@3.738.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/credential-provider-node': 3.738.0
      '@aws-sdk/middleware-host-header': 3.734.0
      '@aws-sdk/middleware-logger': 3.734.0
      '@aws-sdk/middleware-recursion-detection': 3.734.0
      '@aws-sdk/middleware-user-agent': 3.734.0
      '@aws-sdk/region-config-resolver': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-endpoints': 3.734.0
      '@aws-sdk/util-user-agent-browser': 3.734.0
      '@aws-sdk/util-user-agent-node': 3.734.0
      '@smithy/config-resolver': 4.0.1
      '@smithy/core': 3.7.1
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.1
      '@smithy/invalid-dependency': 4.0.1
      '@smithy/middleware-content-length': 4.0.1
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/middleware-retry': 4.0.4
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.4
      '@smithy/util-defaults-mode-node': 4.0.4
      '@smithy/util-endpoints': 3.0.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-s3@3.738.0':
    dependencies:
      '@aws-crypto/sha1-browser': 5.2.0
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/credential-provider-node': 3.738.0
      '@aws-sdk/middleware-bucket-endpoint': 3.734.0
      '@aws-sdk/middleware-expect-continue': 3.734.0
      '@aws-sdk/middleware-flexible-checksums': 3.735.0
      '@aws-sdk/middleware-host-header': 3.734.0
      '@aws-sdk/middleware-location-constraint': 3.734.0
      '@aws-sdk/middleware-logger': 3.734.0
      '@aws-sdk/middleware-recursion-detection': 3.734.0
      '@aws-sdk/middleware-sdk-s3': 3.734.0
      '@aws-sdk/middleware-ssec': 3.734.0
      '@aws-sdk/middleware-user-agent': 3.734.0
      '@aws-sdk/region-config-resolver': 3.734.0
      '@aws-sdk/signature-v4-multi-region': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-endpoints': 3.734.0
      '@aws-sdk/util-user-agent-browser': 3.734.0
      '@aws-sdk/util-user-agent-node': 3.734.0
      '@aws-sdk/xml-builder': 3.734.0
      '@smithy/config-resolver': 4.0.1
      '@smithy/core': 3.7.1
      '@smithy/eventstream-serde-browser': 4.0.1
      '@smithy/eventstream-serde-config-resolver': 4.0.1
      '@smithy/eventstream-serde-node': 4.0.1
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-blob-browser': 4.0.1
      '@smithy/hash-node': 4.0.1
      '@smithy/hash-stream-node': 4.0.1
      '@smithy/invalid-dependency': 4.0.1
      '@smithy/md5-js': 4.0.1
      '@smithy/middleware-content-length': 4.0.1
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/middleware-retry': 4.0.4
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.4
      '@smithy/util-defaults-mode-node': 4.0.4
      '@smithy/util-endpoints': 3.0.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.1
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      '@smithy/util-waiter': 4.0.2
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso@3.734.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/middleware-host-header': 3.734.0
      '@aws-sdk/middleware-logger': 3.734.0
      '@aws-sdk/middleware-recursion-detection': 3.734.0
      '@aws-sdk/middleware-user-agent': 3.734.0
      '@aws-sdk/region-config-resolver': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-endpoints': 3.734.0
      '@aws-sdk/util-user-agent-browser': 3.734.0
      '@aws-sdk/util-user-agent-node': 3.734.0
      '@smithy/config-resolver': 4.0.1
      '@smithy/core': 3.7.1
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.1
      '@smithy/invalid-dependency': 4.0.1
      '@smithy/middleware-content-length': 4.0.1
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/middleware-retry': 4.0.4
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.4
      '@smithy/util-defaults-mode-node': 4.0.4
      '@smithy/util-endpoints': 3.0.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/core': 3.7.1
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-middleware': 4.0.4
      fast-xml-parser: 4.4.1
      tslib: 2.8.1

  '@aws-sdk/core@3.846.0':
    dependencies:
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/xml-builder': 3.821.0
      '@smithy/core': 3.7.1
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-utf8': 4.0.0
      fast-xml-parser: 5.2.5
      tslib: 2.8.1

  '@aws-sdk/credential-provider-cognito-identity@3.738.0':
    dependencies:
      '@aws-sdk/client-cognito-identity': 3.738.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-env@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-http@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/node-http-handler': 4.1.0
      '@smithy/property-provider': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-stream': 4.2.3
      tslib: 2.8.1

  '@aws-sdk/credential-provider-ini@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/credential-provider-env': 3.734.0
      '@aws-sdk/credential-provider-http': 3.734.0
      '@aws-sdk/credential-provider-process': 3.734.0
      '@aws-sdk/credential-provider-sso': 3.734.0
      '@aws-sdk/credential-provider-web-identity': 3.734.0
      '@aws-sdk/nested-clients': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/credential-provider-imds': 4.0.1
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-node@3.738.0':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.734.0
      '@aws-sdk/credential-provider-http': 3.734.0
      '@aws-sdk/credential-provider-ini': 3.734.0
      '@aws-sdk/credential-provider-process': 3.734.0
      '@aws-sdk/credential-provider-sso': 3.734.0
      '@aws-sdk/credential-provider-web-identity': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/credential-provider-imds': 4.0.1
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-process@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-sso@3.734.0':
    dependencies:
      '@aws-sdk/client-sso': 3.734.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/token-providers': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/nested-clients': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-providers@3.738.0':
    dependencies:
      '@aws-sdk/client-cognito-identity': 3.738.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/credential-provider-cognito-identity': 3.738.0
      '@aws-sdk/credential-provider-env': 3.734.0
      '@aws-sdk/credential-provider-http': 3.734.0
      '@aws-sdk/credential-provider-ini': 3.734.0
      '@aws-sdk/credential-provider-node': 3.738.0
      '@aws-sdk/credential-provider-process': 3.734.0
      '@aws-sdk/credential-provider-sso': 3.734.0
      '@aws-sdk/credential-provider-web-identity': 3.734.0
      '@aws-sdk/nested-clients': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/core': 3.7.1
      '@smithy/credential-provider-imds': 4.0.1
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/lib-storage@3.738.0(@aws-sdk/client-s3@3.738.0)':
    dependencies:
      '@aws-sdk/client-s3': 3.738.0
      '@smithy/abort-controller': 4.0.4
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/smithy-client': 4.4.8
      buffer: 5.6.0
      events: 3.3.0
      stream-browserify: 3.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-bucket-endpoint@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-arn-parser': 3.723.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-expect-continue@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-flexible-checksums@3.735.0':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@aws-crypto/crc32c': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/is-array-buffer': 4.0.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-host-header@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-location-constraint@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-logger@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-recursion-detection@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-sdk-s3@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-arn-parser': 3.723.0
      '@smithy/core': 3.7.1
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-sdk-s3@3.846.0':
    dependencies:
      '@aws-sdk/core': 3.846.0
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/util-arn-parser': 3.804.0
      '@smithy/core': 3.7.1
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-ssec@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-user-agent@3.734.0':
    dependencies:
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-endpoints': 3.734.0
      '@smithy/core': 3.7.1
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/nested-clients@3.734.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.734.0
      '@aws-sdk/middleware-host-header': 3.734.0
      '@aws-sdk/middleware-logger': 3.734.0
      '@aws-sdk/middleware-recursion-detection': 3.734.0
      '@aws-sdk/middleware-user-agent': 3.734.0
      '@aws-sdk/region-config-resolver': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-endpoints': 3.734.0
      '@aws-sdk/util-user-agent-browser': 3.734.0
      '@aws-sdk/util-user-agent-node': 3.734.0
      '@smithy/config-resolver': 4.0.1
      '@smithy/core': 3.7.1
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.1
      '@smithy/invalid-dependency': 4.0.1
      '@smithy/middleware-content-length': 4.0.1
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/middleware-retry': 4.0.4
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.4
      '@smithy/util-defaults-mode-node': 4.0.4
      '@smithy/util-endpoints': 3.0.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/region-config-resolver@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@aws-sdk/s3-request-presigner@3.848.0':
    dependencies:
      '@aws-sdk/signature-v4-multi-region': 3.846.0
      '@aws-sdk/types': 3.840.0
      '@aws-sdk/util-format-url': 3.840.0
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/signature-v4-multi-region@3.734.0':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/signature-v4-multi-region@3.846.0':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.846.0
      '@aws-sdk/types': 3.840.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/token-providers@3.734.0':
    dependencies:
      '@aws-sdk/nested-clients': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/types@3.734.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/types@3.840.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/util-arn-parser@3.723.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-arn-parser@3.804.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-endpoints@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/types': 4.3.1
      '@smithy/util-endpoints': 3.0.1
      tslib: 2.8.1

  '@aws-sdk/util-format-url@3.840.0':
    dependencies:
      '@aws-sdk/types': 3.840.0
      '@smithy/querystring-builder': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/util-locate-window@3.723.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-browser@3.734.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/types': 4.3.1
      bowser: 2.11.0
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-node@3.734.0':
    dependencies:
      '@aws-sdk/middleware-user-agent': 3.734.0
      '@aws-sdk/types': 3.734.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/util-utf8-browser@3.259.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/xml-builder@3.734.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/xml-builder@3.821.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/generator@7.17.7':
    dependencies:
      '@babel/types': 7.26.7
      jsesc: 2.5.2
      source-map: 0.5.7

  '@babel/generator@7.26.5':
    dependencies:
      '@babel/parser': 7.26.7
      '@babel/types': 7.26.7
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-environment-visitor@7.24.7':
    dependencies:
      '@babel/types': 7.26.7

  '@babel/helper-function-name@7.24.7':
    dependencies:
      '@babel/template': 7.25.9
      '@babel/types': 7.26.7

  '@babel/helper-hoist-variables@7.24.7':
    dependencies:
      '@babel/types': 7.26.7

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.7
      '@babel/types': 7.26.7
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-split-export-declaration@7.24.7':
    dependencies:
      '@babel/types': 7.26.7

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/parser@7.26.7':
    dependencies:
      '@babel/types': 7.26.7

  '@babel/polyfill@7.12.1':
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.13.11

  '@babel/runtime@7.26.7':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.25.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.7
      '@babel/types': 7.26.7

  '@babel/traverse@7.23.2':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.5
      '@babel/helper-environment-visitor': 7.24.7
      '@babel/helper-function-name': 7.24.7
      '@babel/helper-hoist-variables': 7.24.7
      '@babel/helper-split-export-declaration': 7.24.7
      '@babel/parser': 7.26.7
      '@babel/types': 7.26.7
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/traverse@7.26.7':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.5
      '@babel/parser': 7.26.7
      '@babel/template': 7.25.9
      '@babel/types': 7.26.7
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.17.0':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      to-fast-properties: 2.0.0

  '@babel/types@7.26.7':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@date-fns/tz@1.2.0': {}

  '@dnd-kit/accessibility@3.1.1(react@19.0.0)':
    dependencies:
      react: 19.0.0
      tslib: 2.8.1

  '@dnd-kit/core@6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@dnd-kit/accessibility': 3.1.1(react@19.0.0)
      '@dnd-kit/utilities': 3.2.2(react@19.0.0)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      tslib: 2.8.1

  '@dnd-kit/sortable@7.0.2(@dnd-kit/core@6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@dnd-kit/core': 6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@dnd-kit/utilities': 3.2.2(react@19.0.0)
      react: 19.0.0
      tslib: 2.8.1

  '@dnd-kit/utilities@3.2.2(react@19.0.0)':
    dependencies:
      react: 19.0.0
      tslib: 2.8.1

  '@emnapi/runtime@1.3.1':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@emnapi/runtime@1.4.3':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@emotion/babel-plugin@11.13.5':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/runtime': 7.26.7
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.3
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/cache@11.14.0':
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0

  '@emotion/hash@0.9.2': {}

  '@emotion/memoize@0.9.0': {}

  '@emotion/react@11.14.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@babel/runtime': 7.26.7
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@19.0.0)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
    transitivePeerDependencies:
      - supports-color

  '@emotion/serialize@1.3.3':
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.2
      csstype: 3.1.3

  '@emotion/sheet@1.4.0': {}

  '@emotion/unitless@0.10.0': {}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@emotion/utils@1.4.2': {}

  '@emotion/weak-memoize@0.4.0': {}

  '@esbuild/aix-ppc64@0.23.1':
    optional: true

  '@esbuild/aix-ppc64@0.25.8':
    optional: true

  '@esbuild/android-arm64@0.23.1':
    optional: true

  '@esbuild/android-arm64@0.25.8':
    optional: true

  '@esbuild/android-arm@0.23.1':
    optional: true

  '@esbuild/android-arm@0.25.8':
    optional: true

  '@esbuild/android-x64@0.23.1':
    optional: true

  '@esbuild/android-x64@0.25.8':
    optional: true

  '@esbuild/darwin-arm64@0.23.1':
    optional: true

  '@esbuild/darwin-arm64@0.25.8':
    optional: true

  '@esbuild/darwin-x64@0.23.1':
    optional: true

  '@esbuild/darwin-x64@0.25.8':
    optional: true

  '@esbuild/freebsd-arm64@0.23.1':
    optional: true

  '@esbuild/freebsd-arm64@0.25.8':
    optional: true

  '@esbuild/freebsd-x64@0.23.1':
    optional: true

  '@esbuild/freebsd-x64@0.25.8':
    optional: true

  '@esbuild/linux-arm64@0.23.1':
    optional: true

  '@esbuild/linux-arm64@0.25.8':
    optional: true

  '@esbuild/linux-arm@0.23.1':
    optional: true

  '@esbuild/linux-arm@0.25.8':
    optional: true

  '@esbuild/linux-ia32@0.23.1':
    optional: true

  '@esbuild/linux-ia32@0.25.8':
    optional: true

  '@esbuild/linux-loong64@0.23.1':
    optional: true

  '@esbuild/linux-loong64@0.25.8':
    optional: true

  '@esbuild/linux-mips64el@0.23.1':
    optional: true

  '@esbuild/linux-mips64el@0.25.8':
    optional: true

  '@esbuild/linux-ppc64@0.23.1':
    optional: true

  '@esbuild/linux-ppc64@0.25.8':
    optional: true

  '@esbuild/linux-riscv64@0.23.1':
    optional: true

  '@esbuild/linux-riscv64@0.25.8':
    optional: true

  '@esbuild/linux-s390x@0.23.1':
    optional: true

  '@esbuild/linux-s390x@0.25.8':
    optional: true

  '@esbuild/linux-x64@0.23.1':
    optional: true

  '@esbuild/linux-x64@0.25.8':
    optional: true

  '@esbuild/netbsd-arm64@0.25.8':
    optional: true

  '@esbuild/netbsd-x64@0.23.1':
    optional: true

  '@esbuild/netbsd-x64@0.25.8':
    optional: true

  '@esbuild/openbsd-arm64@0.23.1':
    optional: true

  '@esbuild/openbsd-arm64@0.25.8':
    optional: true

  '@esbuild/openbsd-x64@0.23.1':
    optional: true

  '@esbuild/openbsd-x64@0.25.8':
    optional: true

  '@esbuild/openharmony-arm64@0.25.8':
    optional: true

  '@esbuild/sunos-x64@0.23.1':
    optional: true

  '@esbuild/sunos-x64@0.25.8':
    optional: true

  '@esbuild/win32-arm64@0.23.1':
    optional: true

  '@esbuild/win32-arm64@0.25.8':
    optional: true

  '@esbuild/win32-ia32@0.23.1':
    optional: true

  '@esbuild/win32-ia32@0.25.8':
    optional: true

  '@esbuild/win32-x64@0.23.1':
    optional: true

  '@esbuild/win32-x64@0.25.8':
    optional: true

  '@eslint-community/eslint-utils@4.4.1(eslint@8.57.1)':
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.1': {}

  '@faceless-ui/modal@3.0.0-beta.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      body-scroll-lock: 4.0.0-beta.0
      focus-trap: 7.5.4
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-transition-group: 4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)

  '@faceless-ui/scroll-info@2.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@faceless-ui/window-info@3.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@floating-ui/dom': 1.6.13
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@floating-ui/react@0.27.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@floating-ui/utils': 0.2.9
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      tabbable: 6.2.0

  '@floating-ui/utils@0.2.9': {}

  '@formatjs/ecma402-abstract@2.3.2':
    dependencies:
      '@formatjs/fast-memoize': 2.2.6
      '@formatjs/intl-localematcher': 0.5.10
      decimal.js: 10.5.0
      tslib: 2.8.1

  '@formatjs/fast-memoize@2.2.6':
    dependencies:
      tslib: 2.8.1

  '@formatjs/icu-messageformat-parser@2.11.0':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.2
      '@formatjs/icu-skeleton-parser': 1.8.12
      tslib: 2.8.1

  '@formatjs/icu-skeleton-parser@1.8.12':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.2
      tslib: 2.8.1

  '@formatjs/intl-localematcher@0.5.10':
    dependencies:
      tslib: 2.8.1

  '@hey-api/client-fetch@0.4.4': {}

  '@hey-api/openapi-ts@0.54.4(typescript@5.6.3)':
    dependencies:
      '@apidevtools/json-schema-ref-parser': 11.7.2
      c12: 2.0.1
      commander: 12.1.0
      handlebars: 4.7.8
      typescript: 5.6.3
    transitivePeerDependencies:
      - magicast

  '@hookform/resolvers@3.10.0(react-hook-form@7.57.0(react@19.0.0))':
    dependencies:
      react-hook-form: 7.57.0(react@19.0.0)

  '@humanwhocodes/config-array@0.13.0':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@img/sharp-darwin-arm64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-darwin-arm64': 1.0.4
    optional: true

  '@img/sharp-darwin-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-darwin-arm64': 1.1.0
    optional: true

  '@img/sharp-darwin-x64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-darwin-x64': 1.0.4
    optional: true

  '@img/sharp-darwin-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-darwin-x64': 1.1.0
    optional: true

  '@img/sharp-libvips-darwin-arm64@1.0.4':
    optional: true

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-darwin-x64@1.0.4':
    optional: true

  '@img/sharp-libvips-darwin-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm64@1.0.4':
    optional: true

  '@img/sharp-libvips-linux-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm@1.0.5':
    optional: true

  '@img/sharp-libvips-linux-arm@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-s390x@1.0.4':
    optional: true

  '@img/sharp-libvips-linux-s390x@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-x64@1.0.4':
    optional: true

  '@img/sharp-libvips-linux-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    optional: true

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    optional: true

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    optional: true

  '@img/sharp-linux-arm64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm64': 1.0.4
    optional: true

  '@img/sharp-linux-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm64': 1.1.0
    optional: true

  '@img/sharp-linux-arm@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm': 1.0.5
    optional: true

  '@img/sharp-linux-arm@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm': 1.1.0
    optional: true

  '@img/sharp-linux-s390x@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linux-s390x': 1.0.4
    optional: true

  '@img/sharp-linux-s390x@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-s390x': 1.1.0
    optional: true

  '@img/sharp-linux-x64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linux-x64': 1.0.4
    optional: true

  '@img/sharp-linux-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-x64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-arm64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
    optional: true

  '@img/sharp-linuxmusl-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-x64@0.33.5':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-x64': 1.0.4
    optional: true

  '@img/sharp-linuxmusl-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
    optional: true

  '@img/sharp-wasm32@0.33.5':
    dependencies:
      '@emnapi/runtime': 1.3.1
    optional: true

  '@img/sharp-wasm32@0.34.2':
    dependencies:
      '@emnapi/runtime': 1.4.3
    optional: true

  '@img/sharp-win32-arm64@0.34.2':
    optional: true

  '@img/sharp-win32-ia32@0.33.5':
    optional: true

  '@img/sharp-win32-ia32@0.34.2':
    optional: true

  '@img/sharp-win32-x64@0.33.5':
    optional: true

  '@img/sharp-win32-x64@0.34.2':
    optional: true

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@jsdevtools/ono@7.1.3': {}

  '@lexical/clipboard@0.28.0':
    dependencies:
      '@lexical/html': 0.28.0
      '@lexical/list': 0.28.0
      '@lexical/selection': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/code@0.28.0':
    dependencies:
      '@lexical/utils': 0.28.0
      lexical: 0.28.0
      prismjs: 1.30.0

  '@lexical/devtools-core@0.28.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@lexical/html': 0.28.0
      '@lexical/link': 0.28.0
      '@lexical/mark': 0.28.0
      '@lexical/table': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@lexical/dragon@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/hashtag@0.28.0':
    dependencies:
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/headless@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/history@0.28.0':
    dependencies:
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/html@0.28.0':
    dependencies:
      '@lexical/selection': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/link@0.28.0':
    dependencies:
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/list@0.28.0':
    dependencies:
      '@lexical/selection': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/mark@0.28.0':
    dependencies:
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/markdown@0.28.0':
    dependencies:
      '@lexical/code': 0.28.0
      '@lexical/link': 0.28.0
      '@lexical/list': 0.28.0
      '@lexical/rich-text': 0.28.0
      '@lexical/text': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/offset@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/overflow@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/plain-text@0.28.0':
    dependencies:
      '@lexical/clipboard': 0.28.0
      '@lexical/selection': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/react@0.28.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(yjs@13.6.23)':
    dependencies:
      '@lexical/devtools-core': 0.28.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@lexical/dragon': 0.28.0
      '@lexical/hashtag': 0.28.0
      '@lexical/history': 0.28.0
      '@lexical/link': 0.28.0
      '@lexical/list': 0.28.0
      '@lexical/mark': 0.28.0
      '@lexical/markdown': 0.28.0
      '@lexical/overflow': 0.28.0
      '@lexical/plain-text': 0.28.0
      '@lexical/rich-text': 0.28.0
      '@lexical/table': 0.28.0
      '@lexical/text': 0.28.0
      '@lexical/utils': 0.28.0
      '@lexical/yjs': 0.28.0(yjs@13.6.23)
      lexical: 0.28.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-error-boundary: 3.1.4(react@19.0.0)
    transitivePeerDependencies:
      - yjs

  '@lexical/rich-text@0.28.0':
    dependencies:
      '@lexical/clipboard': 0.28.0
      '@lexical/selection': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/selection@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/table@0.28.0':
    dependencies:
      '@lexical/clipboard': 0.28.0
      '@lexical/utils': 0.28.0
      lexical: 0.28.0

  '@lexical/text@0.28.0':
    dependencies:
      lexical: 0.28.0

  '@lexical/utils@0.28.0':
    dependencies:
      '@lexical/list': 0.28.0
      '@lexical/selection': 0.28.0
      '@lexical/table': 0.28.0
      lexical: 0.28.0

  '@lexical/yjs@0.28.0(yjs@13.6.23)':
    dependencies:
      '@lexical/offset': 0.28.0
      '@lexical/selection': 0.28.0
      lexical: 0.28.0
      yjs: 13.6.23

  '@monaco-editor/loader@1.5.0':
    dependencies:
      state-local: 1.0.7

  '@monaco-editor/react@4.7.0(monaco-editor@0.52.2)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@monaco-editor/loader': 1.5.0
      monaco-editor: 0.52.2
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@mongodb-js/saslprep@1.1.9':
    dependencies:
      sparse-bitfield: 3.0.3

  '@next/env@15.3.4': {}

  '@next/eslint-plugin-next@15.0.0':
    dependencies:
      fast-glob: 3.3.1

  '@next/swc-darwin-arm64@15.3.4':
    optional: true

  '@next/swc-darwin-x64@15.3.4':
    optional: true

  '@next/swc-linux-arm64-gnu@15.3.4':
    optional: true

  '@next/swc-linux-arm64-musl@15.3.4':
    optional: true

  '@next/swc-linux-x64-gnu@15.3.4':
    optional: true

  '@next/swc-linux-x64-musl@15.3.4':
    optional: true

  '@next/swc-win32-arm64-msvc@15.3.4':
    optional: true

  '@next/swc-win32-x64-msvc@15.3.4':
    optional: true

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.18.0

  '@nolyfill/is-core-module@1.0.39': {}

  '@one-ini/wasm@0.1.1': {}

  '@payloadcms/db-mongodb@3.50.0(@aws-sdk/credential-providers@3.738.0)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      mongoose: 8.15.1(@aws-sdk/credential-providers@3.738.0)
      mongoose-paginate-v2: 1.8.5
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      prompts: 2.4.2
      uuid: 10.0.0
    transitivePeerDependencies:
      - '@aws-sdk/credential-providers'
      - '@mongodb-js/zstd'
      - gcp-metadata
      - kerberos
      - mongodb-client-encryption
      - snappy
      - socks
      - supports-color

  '@payloadcms/email-nodemailer@3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      nodemailer: 6.9.16
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)

  '@payloadcms/email-resend@3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)

  '@payloadcms/graphql@3.50.0(graphql@16.10.0)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(typescript@5.6.3)':
    dependencies:
      graphql: 16.10.0
      graphql-scalars: 1.22.2(graphql@16.10.0)
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      pluralize: 8.0.0
      ts-essentials: 10.0.3(typescript@5.6.3)
      tsx: 4.19.2
    transitivePeerDependencies:
      - typescript

  '@payloadcms/live-preview-react@3.50.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@payloadcms/live-preview': 3.50.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@payloadcms/live-preview@3.50.0': {}

  '@payloadcms/next@3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@dnd-kit/core': 6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@payloadcms/graphql': 3.50.0(graphql@16.10.0)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(typescript@5.6.3)
      '@payloadcms/translations': 3.50.0
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      busboy: 1.6.0
      dequal: 2.0.3
      file-type: 19.3.0
      graphql: 16.10.0
      graphql-http: 1.22.4(graphql@16.10.0)
      graphql-playground-html: 1.6.30
      http-status: 2.1.0
      next: 15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4)
      path-to-regexp: 6.3.0
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      qs-esm: 7.0.2
      sass: 1.77.4
      uuid: 10.0.0
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - react
      - react-dom
      - supports-color
      - typescript

  '@payloadcms/payload-cloud@3.50.0(encoding@0.1.13)(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      '@aws-sdk/client-cognito-identity': 3.738.0
      '@aws-sdk/client-s3': 3.738.0
      '@aws-sdk/credential-providers': 3.738.0
      '@aws-sdk/lib-storage': 3.738.0(@aws-sdk/client-s3@3.738.0)
      '@payloadcms/email-nodemailer': 3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))
      amazon-cognito-identity-js: 6.3.12(encoding@0.1.13)
      nodemailer: 6.9.16
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
    transitivePeerDependencies:
      - aws-crt
      - encoding

  '@payloadcms/plugin-cloud-storage@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      find-node-modules: 2.1.3
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      range-parser: 1.2.1
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - next
      - supports-color
      - typescript

  '@payloadcms/plugin-form-builder@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      escape-html: 1.0.3
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - next
      - supports-color
      - typescript

  '@payloadcms/plugin-nested-docs@3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)

  '@payloadcms/plugin-redirects@3.50.0(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))':
    dependencies:
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)

  '@payloadcms/plugin-search@3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@payloadcms/next': 3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'
      - graphql
      - monaco-editor
      - next
      - supports-color
      - typescript

  '@payloadcms/plugin-seo@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@payloadcms/translations': 3.50.0
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - next
      - supports-color
      - typescript

  '@payloadcms/plugin-stripe@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@payloadcms/translations': 3.50.0
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      lodash.get: 4.4.2
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      stripe: 10.17.0
      uuid: 10.0.0
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - next
      - react
      - react-dom
      - supports-color
      - typescript

  '@payloadcms/richtext-lexical@3.50.0(@faceless-ui/modal@3.0.0-beta.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@faceless-ui/scroll-info@2.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(@payloadcms/next@3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3))(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)(yjs@13.6.23)':
    dependencies:
      '@faceless-ui/modal': 3.0.0-beta.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@faceless-ui/scroll-info': 2.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@lexical/headless': 0.28.0
      '@lexical/html': 0.28.0
      '@lexical/link': 0.28.0
      '@lexical/list': 0.28.0
      '@lexical/mark': 0.28.0
      '@lexical/react': 0.28.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(yjs@13.6.23)
      '@lexical/rich-text': 0.28.0
      '@lexical/selection': 0.28.0
      '@lexical/table': 0.28.0
      '@lexical/utils': 0.28.0
      '@payloadcms/next': 3.50.0(graphql@16.10.0)(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@payloadcms/translations': 3.50.0
      '@payloadcms/ui': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      '@types/uuid': 10.0.0
      acorn: 8.12.1
      bson-objectid: 2.0.4
      csstype: 3.1.3
      dequal: 2.0.3
      escape-html: 1.0.3
      jsox: 1.2.121
      lexical: 0.28.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-mdx-jsx: 3.1.3
      micromark-extension-mdx-jsx: 3.0.1
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      qs-esm: 7.0.2
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-error-boundary: 4.1.2(react@19.0.0)
      ts-essentials: 10.0.3(typescript@5.6.3)
      uuid: 10.0.0
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - next
      - supports-color
      - typescript
      - yjs

  '@payloadcms/storage-s3@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@aws-sdk/client-s3': 3.738.0
      '@aws-sdk/lib-storage': 3.738.0(@aws-sdk/client-s3@3.738.0)
      '@aws-sdk/s3-request-presigner': 3.848.0
      '@payloadcms/plugin-cloud-storage': 3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
    transitivePeerDependencies:
      - '@types/react'
      - aws-crt
      - monaco-editor
      - next
      - react
      - react-dom
      - supports-color
      - typescript

  '@payloadcms/translations@3.50.0':
    dependencies:
      date-fns: 4.1.0

  '@payloadcms/ui@3.50.0(monaco-editor@0.52.2)(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(payload@3.50.0(graphql@16.10.0)(typescript@5.6.3))(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)(typescript@5.6.3)':
    dependencies:
      '@date-fns/tz': 1.2.0
      '@dnd-kit/core': 6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@dnd-kit/sortable': 7.0.2(@dnd-kit/core@6.0.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0))(react@19.0.0)
      '@dnd-kit/utilities': 3.2.2(react@19.0.0)
      '@faceless-ui/modal': 3.0.0-beta.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@faceless-ui/scroll-info': 2.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@faceless-ui/window-info': 3.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@monaco-editor/react': 4.7.0(monaco-editor@0.52.2)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@payloadcms/translations': 3.50.0
      bson-objectid: 2.0.4
      date-fns: 4.1.0
      dequal: 2.0.3
      md5: 2.3.0
      next: 15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4)
      object-to-formdata: 4.5.1
      payload: 3.50.0(graphql@16.10.0)(typescript@5.6.3)
      qs-esm: 7.0.2
      react: 19.0.0
      react-datepicker: 7.6.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      react-dom: 19.0.0(react@19.0.0)
      react-image-crop: 10.1.8(react@19.0.0)
      react-select: 5.9.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1)
      scheduler: 0.25.0
      sonner: 1.7.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      ts-essentials: 10.0.3(typescript@5.6.3)
      use-context-selector: 2.0.0(react@19.0.0)(scheduler@0.25.0)
      uuid: 10.0.0
    transitivePeerDependencies:
      - '@types/react'
      - monaco-editor
      - supports-color
      - typescript

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@raddix/use-media-query@0.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@radix-ui/number@1.1.0': {}

  '@radix-ui/primitive@1.1.1': {}

  '@radix-ui/react-accordion@1.2.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collapsible': 1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-collection': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-alert-dialog@1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dialog': 1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-arrow@1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-arrow@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-avatar@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-checkbox@1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-previous': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-size': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-collapsible@1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-collection@1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-collection@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.2(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-compose-refs@1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-context@1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-dialog@1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dismissable-layer': 1.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-guards': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-scope': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-portal': 1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      aria-hidden: 1.2.4
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-remove-scroll: 2.6.3(react@19.0.0)(types-react@19.0.0-rc.1)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-direction@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-dismissable-layer@1.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-escape-keydown': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-dismissable-layer@1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-escape-keydown': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-dropdown-menu@2.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-menu': 2.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-focus-guards@1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-focus-scope@1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-focus-scope@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-id@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-label@2.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-menu@2.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dismissable-layer': 1.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-guards': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-scope': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-popper': 1.2.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-portal': 1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-roving-focus': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      aria-hidden: 1.2.4
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-remove-scroll: 2.6.3(react@19.0.0)(types-react@19.0.0-rc.1)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-popper@1.2.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@radix-ui/react-arrow': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-rect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-size': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/rect': 1.1.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-popper@1.2.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@radix-ui/react-arrow': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-rect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-size': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/rect': 1.1.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-portal@1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-portal@1.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-presence@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-primitive@2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-slot': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-primitive@2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-slot': 1.1.2(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-radio-group@1.2.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-roving-focus': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-previous': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-size': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-roving-focus@1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-roving-focus@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-select@2.1.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/number': 1.1.0
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-dismissable-layer': 1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-guards': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-focus-scope': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-popper': 1.2.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-portal': 1.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-slot': 1.1.2(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-previous': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-visually-hidden': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      aria-hidden: 1.2.4
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-remove-scroll: 2.6.3(react@19.0.0)(types-react@19.0.0-rc.1)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-separator@1.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-slot@1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-slot@1.1.2(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-switch@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-previous': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-size': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-tabs@1.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-context': 1.1.1(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-direction': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-id': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@radix-ui/react-presence': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-roving-focus': 1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/react-use-callback-ref@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-controllable-state@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-escape-keydown@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-layout-effect@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-previous@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-rect@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/rect': 1.1.0
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-use-size@1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(react@19.0.0)(types-react@19.0.0-rc.1)
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@radix-ui/react-visually-hidden@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1
      '@types/react-dom': types-react-dom@19.0.0-rc.1

  '@radix-ui/rect@1.1.0': {}

  '@react-email/body@0.0.11(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/button@0.0.19(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/code-block@0.0.11(react@19.0.0)':
    dependencies:
      prismjs: 1.29.0
      react: 19.0.0

  '@react-email/code-inline@0.0.5(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/column@0.0.13(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/components@0.0.31(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@react-email/body': 0.0.11(react@19.0.0)
      '@react-email/button': 0.0.19(react@19.0.0)
      '@react-email/code-block': 0.0.11(react@19.0.0)
      '@react-email/code-inline': 0.0.5(react@19.0.0)
      '@react-email/column': 0.0.13(react@19.0.0)
      '@react-email/container': 0.0.15(react@19.0.0)
      '@react-email/font': 0.0.9(react@19.0.0)
      '@react-email/head': 0.0.12(react@19.0.0)
      '@react-email/heading': 0.0.15(react@19.0.0)
      '@react-email/hr': 0.0.11(react@19.0.0)
      '@react-email/html': 0.0.11(react@19.0.0)
      '@react-email/img': 0.0.11(react@19.0.0)
      '@react-email/link': 0.0.12(react@19.0.0)
      '@react-email/markdown': 0.0.14(react@19.0.0)
      '@react-email/preview': 0.0.12(react@19.0.0)
      '@react-email/render': 1.0.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      '@react-email/row': 0.0.12(react@19.0.0)
      '@react-email/section': 0.0.16(react@19.0.0)
      '@react-email/tailwind': 1.0.4(react@19.0.0)
      '@react-email/text': 0.0.11(react@19.0.0)
      react: 19.0.0
    transitivePeerDependencies:
      - react-dom

  '@react-email/container@0.0.15(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/font@0.0.9(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/head@0.0.12(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/heading@0.0.15(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/hr@0.0.11(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/html@0.0.11(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/img@0.0.11(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/link@0.0.12(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/markdown@0.0.14(react@19.0.0)':
    dependencies:
      md-to-react-email: 5.0.5(react@19.0.0)
      react: 19.0.0

  '@react-email/preview@0.0.12(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/render@1.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      html-to-text: 9.0.5
      js-beautify: 1.15.1
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-promise-suspense: 0.3.4

  '@react-email/render@1.0.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      html-to-text: 9.0.5
      prettier: 3.3.3
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-promise-suspense: 0.3.4

  '@react-email/row@0.0.12(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/section@0.0.16(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/tailwind@1.0.4(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-email/text@0.0.11(react@19.0.0)':
    dependencies:
      react: 19.0.0

  '@react-pdf/fns@3.1.2': {}

  '@react-pdf/font@4.0.2':
    dependencies:
      '@react-pdf/pdfkit': 4.0.3
      '@react-pdf/types': 2.9.0
      fontkit: 2.0.4
      is-url: 1.2.4

  '@react-pdf/image@3.0.3':
    dependencies:
      '@react-pdf/png-js': 3.0.0
      jay-peg: 1.1.1

  '@react-pdf/layout@4.4.0':
    dependencies:
      '@react-pdf/fns': 3.1.2
      '@react-pdf/image': 3.0.3
      '@react-pdf/primitives': 4.1.1
      '@react-pdf/stylesheet': 6.1.0
      '@react-pdf/textkit': 6.0.0
      '@react-pdf/types': 2.9.0
      emoji-regex: 10.4.0
      queue: 6.0.2
      yoga-layout: 3.2.1

  '@react-pdf/pdfkit@4.0.3':
    dependencies:
      '@babel/runtime': 7.26.7
      '@react-pdf/png-js': 3.0.0
      browserify-zlib: 0.2.0
      crypto-js: 4.2.0
      fontkit: 2.0.4
      jay-peg: 1.1.1
      linebreak: 1.1.0
      vite-compatible-readable-stream: 3.6.1

  '@react-pdf/png-js@3.0.0':
    dependencies:
      browserify-zlib: 0.2.0

  '@react-pdf/primitives@4.1.1': {}

  '@react-pdf/reconciler@1.1.4(react@19.0.0)':
    dependencies:
      object-assign: 4.1.1
      react: 19.0.0
      scheduler: 0.25.0-rc-603e6108-20241029

  '@react-pdf/render@4.3.0':
    dependencies:
      '@babel/runtime': 7.26.7
      '@react-pdf/fns': 3.1.2
      '@react-pdf/primitives': 4.1.1
      '@react-pdf/textkit': 6.0.0
      '@react-pdf/types': 2.9.0
      abs-svg-path: 0.1.1
      color-string: 1.9.1
      normalize-svg-path: 1.1.0
      parse-svg-path: 0.1.2
      svg-arc-to-cubic-bezier: 3.2.0

  '@react-pdf/renderer@4.3.0(react@19.0.0)':
    dependencies:
      '@babel/runtime': 7.26.7
      '@react-pdf/fns': 3.1.2
      '@react-pdf/font': 4.0.2
      '@react-pdf/layout': 4.4.0
      '@react-pdf/pdfkit': 4.0.3
      '@react-pdf/primitives': 4.1.1
      '@react-pdf/reconciler': 1.1.4(react@19.0.0)
      '@react-pdf/render': 4.3.0
      '@react-pdf/types': 2.9.0
      events: 3.3.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      queue: 6.0.2
      react: 19.0.0

  '@react-pdf/stylesheet@6.1.0':
    dependencies:
      '@react-pdf/fns': 3.1.2
      '@react-pdf/types': 2.9.0
      color-string: 1.9.1
      hsl-to-hex: 1.0.0
      media-engine: 1.0.3
      postcss-value-parser: 4.2.0

  '@react-pdf/textkit@6.0.0':
    dependencies:
      '@react-pdf/fns': 3.1.2
      bidi-js: 1.0.3
      hyphen: 1.10.6
      unicode-properties: 1.4.1

  '@react-pdf/types@2.9.0':
    dependencies:
      '@react-pdf/font': 4.0.2
      '@react-pdf/primitives': 4.1.1
      '@react-pdf/stylesheet': 6.1.0

  '@resvg/resvg-wasm@2.6.2': {}

  '@rtsao/scc@1.1.0': {}

  '@rushstack/eslint-patch@1.10.5': {}

  '@selderee/plugin-htmlparser2@0.11.0':
    dependencies:
      domhandler: 5.0.3
      selderee: 0.11.0

  '@shuding/opentype.js@1.4.0-beta.0':
    dependencies:
      fflate: 0.7.4
      string.prototype.codepointat: 0.2.1

  '@smithy/abort-controller@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/chunked-blob-reader-native@4.0.0':
    dependencies:
      '@smithy/util-base64': 4.0.0
      tslib: 2.8.1

  '@smithy/chunked-blob-reader@5.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/config-resolver@4.0.1':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@smithy/core@3.7.1':
    dependencies:
      '@smithy/middleware-serde': 4.0.8
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/credential-provider-imds@4.0.1':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      tslib: 2.8.1

  '@smithy/eventstream-codec@4.0.1':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 4.3.1
      '@smithy/util-hex-encoding': 4.0.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-browser@4.0.1':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.1
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-config-resolver@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-node@4.0.1':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.1
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-universal@4.0.1':
    dependencies:
      '@smithy/eventstream-codec': 4.0.1
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/fetch-http-handler@5.1.0':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/querystring-builder': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      tslib: 2.8.1

  '@smithy/hash-blob-browser@4.0.1':
    dependencies:
      '@smithy/chunked-blob-reader': 5.0.0
      '@smithy/chunked-blob-reader-native': 4.0.0
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/hash-node@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/hash-stream-node@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/invalid-dependency@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/is-array-buffer@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/md5-js@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/middleware-content-length@4.0.1':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/middleware-endpoint@4.1.16':
    dependencies:
      '@smithy/core': 3.7.1
      '@smithy/middleware-serde': 4.0.8
      '@smithy/node-config-provider': 4.1.3
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@smithy/middleware-retry@4.0.4':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/service-error-classification': 4.0.1
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.1
      tslib: 2.8.1
      uuid: 9.0.1

  '@smithy/middleware-serde@4.0.8':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/middleware-stack@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/node-config-provider@4.1.3':
    dependencies:
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/node-http-handler@4.1.0':
    dependencies:
      '@smithy/abort-controller': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/querystring-builder': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/property-provider@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/protocol-http@5.1.2':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/querystring-builder@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-uri-escape': 4.0.0
      tslib: 2.8.1

  '@smithy/querystring-parser@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/service-error-classification@4.0.1':
    dependencies:
      '@smithy/types': 4.3.1

  '@smithy/shared-ini-file-loader@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/signature-v4@5.1.2':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-uri-escape': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/smithy-client@4.4.8':
    dependencies:
      '@smithy/core': 3.7.1
      '@smithy/middleware-endpoint': 4.1.16
      '@smithy/middleware-stack': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-stream': 4.2.3
      tslib: 2.8.1

  '@smithy/types@4.3.1':
    dependencies:
      tslib: 2.8.1

  '@smithy/url-parser@4.0.4':
    dependencies:
      '@smithy/querystring-parser': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-base64@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-body-length-browser@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-body-length-node@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.8.1

  '@smithy/util-buffer-from@4.0.0':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      tslib: 2.8.1

  '@smithy/util-config-provider@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-defaults-mode-browser@4.0.4':
    dependencies:
      '@smithy/property-provider': 4.0.4
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      bowser: 2.11.0
      tslib: 2.8.1

  '@smithy/util-defaults-mode-node@4.0.4':
    dependencies:
      '@smithy/config-resolver': 4.0.1
      '@smithy/credential-provider-imds': 4.0.1
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/smithy-client': 4.4.8
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-endpoints@3.0.1':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-hex-encoding@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-middleware@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-retry@4.0.1':
    dependencies:
      '@smithy/service-error-classification': 4.0.1
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-stream@4.2.3':
    dependencies:
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/node-http-handler': 4.1.0
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-uri-escape@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.8.1

  '@smithy/util-utf8@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      tslib: 2.8.1

  '@smithy/util-waiter@4.0.2':
    dependencies:
      '@smithy/abort-controller': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@stripe/react-stripe-js@3.7.0(@stripe/stripe-js@7.5.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@stripe/stripe-js': 7.5.0
      prop-types: 15.8.1
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  '@stripe/stripe-js@7.5.0': {}

  '@swc/counter@0.1.3': {}

  '@swc/helpers@0.5.13':
    dependencies:
      tslib: 2.8.1

  '@swc/helpers@0.5.15':
    dependencies:
      tslib: 2.8.1

  '@tanstack/eslint-plugin-query@5.65.0(eslint@8.57.1)(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/utils': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      eslint: 8.57.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@tanstack/query-core@5.65.0': {}

  '@tanstack/query-devtools@5.65.0': {}

  '@tanstack/react-query-devtools@5.65.1(@tanstack/react-query@5.65.1(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@tanstack/query-devtools': 5.65.0
      '@tanstack/react-query': 5.65.1(react@19.0.0)
      react: 19.0.0

  '@tanstack/react-query@5.65.1(react@19.0.0)':
    dependencies:
      '@tanstack/query-core': 5.65.0
      react: 19.0.0

  '@tokenizer/token@0.3.0': {}

  '@trivago/prettier-plugin-sort-imports@4.3.0(prettier@3.4.2)':
    dependencies:
      '@babel/generator': 7.17.7
      '@babel/parser': 7.26.7
      '@babel/traverse': 7.23.2
      '@babel/types': 7.17.0
      javascript-natural-sort: 0.7.1
      lodash: 4.17.21
      prettier: 3.4.2
    transitivePeerDependencies:
      - supports-color

  '@types/acorn@4.0.6':
    dependencies:
      '@types/estree': 1.0.6

  '@types/busboy@1.5.4':
    dependencies:
      '@types/node': 22.12.0

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/estree-jsx@1.0.5':
    dependencies:
      '@types/estree': 1.0.6

  '@types/estree@1.0.6': {}

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/lodash@4.17.15': {}

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/ms@2.1.0': {}

  '@types/node@22.12.0':
    dependencies:
      undici-types: 6.20.0

  '@types/normalize-package-data@2.4.4': {}

  '@types/parse-json@4.0.2': {}

  '@types/react-transition-group@4.4.12(types-react@19.0.0-rc.1)':
    dependencies:
      '@types/react': types-react@19.0.0-rc.1

  '@types/react@19.0.8':
    dependencies:
      csstype: 3.1.3

  '@types/unist@2.0.11': {}

  '@types/unist@3.0.3': {}

  '@types/uuid@10.0.0': {}

  '@types/webidl-conversions@7.0.3': {}

  '@types/whatwg-url@11.0.5':
    dependencies:
      '@types/webidl-conversions': 7.0.3

  '@typescript-eslint/eslint-plugin@8.22.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint@8.57.1)(typescript@5.6.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      '@typescript-eslint/scope-manager': 8.22.0
      '@typescript-eslint/type-utils': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      '@typescript-eslint/utils': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.22.0
      eslint: 8.57.1
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 2.0.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.22.0
      '@typescript-eslint/types': 8.22.0
      '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.22.0
      debug: 4.4.0
      eslint: 8.57.1
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.22.0':
    dependencies:
      '@typescript-eslint/types': 8.22.0
      '@typescript-eslint/visitor-keys': 8.22.0

  '@typescript-eslint/type-utils@8.22.0(eslint@8.57.1)(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.6.3)
      '@typescript-eslint/utils': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      debug: 4.4.0
      eslint: 8.57.1
      ts-api-utils: 2.0.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.22.0': {}

  '@typescript-eslint/typescript-estree@8.22.0(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/types': 8.22.0
      '@typescript-eslint/visitor-keys': 8.22.0
      debug: 4.4.0
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.0
      ts-api-utils: 2.0.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.22.0(eslint@8.57.1)(typescript@5.6.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      '@typescript-eslint/scope-manager': 8.22.0
      '@typescript-eslint/types': 8.22.0
      '@typescript-eslint/typescript-estree': 8.22.0(typescript@5.6.3)
      eslint: 8.57.1
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.22.0':
    dependencies:
      '@typescript-eslint/types': 8.22.0
      eslint-visitor-keys: 4.2.0

  '@ungap/structured-clone@1.3.0': {}

  abbrev@2.0.0: {}

  abs-svg-path@0.1.1: {}

  acorn-jsx@5.3.2(acorn@8.14.0):
    dependencies:
      acorn: 8.14.0

  acorn@8.12.1: {}

  acorn@8.14.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  amazon-cognito-identity-js@6.3.12(encoding@0.1.13):
    dependencies:
      '@aws-crypto/sha256-js': 1.2.2
      buffer: 4.9.2
      fast-base64-decode: 1.0.0
      isomorphic-unfetch: 3.1.0(encoding@0.1.13)
      js-cookie: 2.2.1
    transitivePeerDependencies:
      - encoding

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.4:
    dependencies:
      tslib: 2.8.1

  aria-query@5.3.2: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      is-array-buffer: 3.0.5

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      get-intrinsic: 1.2.7
      is-string: 1.1.1

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.0.2

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      is-array-buffer: 3.0.5

  ast-types-flow@0.0.8: {}

  async-function@1.0.0: {}

  atomic-sleep@1.0.0: {}

  autoprefixer@10.4.20(postcss@8.5.1):
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001696
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.1
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axe-core@4.10.2: {}

  axobject-query@4.1.0: {}

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.26.7
      cosmiconfig: 7.1.0
      resolve: 1.22.10

  balanced-match@1.0.2: {}

  base64-js@0.0.8: {}

  base64-js@1.5.1: {}

  bidi-js@1.0.3:
    dependencies:
      require-from-string: 2.0.2

  binary-extensions@2.3.0: {}

  blob-stream@0.1.3:
    dependencies:
      blob: 0.0.4

  blob@0.0.4: {}

  body-scroll-lock@4.0.0-beta.0: {}

  bowser@2.11.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  brotli@1.3.3:
    dependencies:
      base64-js: 1.5.1

  browserify-zlib@0.2.0:
    dependencies:
      pako: 1.0.11

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001696
      electron-to-chromium: 1.5.90
      node-releases: 2.0.19
      update-browserslist-db: 1.1.2(browserslist@4.24.4)

  bson-objectid@2.0.4: {}

  bson@6.10.4: {}

  buffer@4.9.2:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0

  buffer@5.6.0:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  builtin-modules@3.3.0: {}

  busboy@1.6.0:
    dependencies:
      streamsearch: 1.1.0

  c12@2.0.1:
    dependencies:
      chokidar: 4.0.3
      confbox: 0.1.8
      defu: 6.1.4
      dotenv: 16.4.7
      giget: 1.2.4
      jiti: 2.4.2
      mlly: 1.7.4
      ohash: 1.1.4
      pathe: 1.1.2
      perfect-debounce: 1.0.0
      pkg-types: 1.3.1
      rc9: 2.1.2

  call-bind-apply-helpers@1.0.1:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      get-intrinsic: 1.2.7
      set-function-length: 1.2.2

  call-bound@1.0.3:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      get-intrinsic: 1.2.7

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  camelcase@8.0.0: {}

  camelize@1.0.1: {}

  caniuse-lite@1.0.30001696: {}

  ccount@2.0.1: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  character-entities@2.0.2: {}

  character-reference-invalid@2.0.1: {}

  charenc@0.0.2: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.1

  chownr@2.0.0: {}

  ci-info@4.1.0: {}

  citty@0.1.6:
    dependencies:
      consola: 3.4.0

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  clean-regexp@1.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  clear-cut@2.0.2: {}

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  client-only@0.0.1: {}

  clone@2.1.2: {}

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1

  colorette@2.0.20: {}

  commander@10.0.1: {}

  commander@12.1.0: {}

  commander@13.1.0: {}

  commander@2.20.3: {}

  commander@4.1.1: {}

  concat-map@0.0.1: {}

  confbox@0.1.8: {}

  config-chain@1.1.13:
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4

  consola@3.4.0: {}

  console-table-printer@2.12.1:
    dependencies:
      simple-wcswidth: 1.0.1

  convert-source-map@1.9.0: {}

  core-js-compat@3.40.0:
    dependencies:
      browserslist: 4.24.4

  core-js@2.6.12: {}

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  croner@9.1.0: {}

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypt@0.0.2: {}

  crypto-js@4.2.0: {}

  css-background-parser@0.1.0: {}

  css-box-shadow@1.0.0-3: {}

  css-color-keywords@1.0.0: {}

  css-gradient-parser@0.0.16: {}

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  cssesc@3.0.0: {}

  cssfilter@0.0.10: {}

  csstype@3.1.3: {}

  d@1.0.2:
    dependencies:
      es5-ext: 0.10.64
      type: 2.7.3

  damerau-levenshtein@1.0.8: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  dataloader@2.2.3: {}

  date-fns@3.6.0: {}

  date-fns@4.1.0: {}

  dateformat@4.6.3: {}

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decimal.js@10.5.0: {}

  decode-named-character-reference@1.0.2:
    dependencies:
      character-entities: 2.0.2

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  defu@6.1.4: {}

  dequal@2.0.3: {}

  destr@2.0.3: {}

  detect-file@1.0.0: {}

  detect-libc@2.0.3: {}

  detect-libc@2.0.4:
    optional: true

  detect-node-es@1.1.0: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  dfa@1.2.0: {}

  didyoumean@1.2.2: {}

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.26.7
      csstype: 3.1.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dotenv@16.4.7: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  editorconfig@1.0.4:
    dependencies:
      '@one-ini/wasm': 0.1.1
      commander: 10.0.1
      minimatch: 9.0.1
      semver: 7.7.0

  electron-to-chromium@1.5.90: {}

  emoji-regex-xs@2.0.1: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3
    optional: true

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  enhanced-resolve@5.18.0:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@4.5.0: {}

  entities@6.0.1: {}

  environment@1.1.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.23.9:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.2.7
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.0
      math-intrinsics: 1.1.0
      object-inspect: 1.13.3
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.18

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.7
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  es5-ext@0.10.64:
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.4
      esniff: 2.0.1
      next-tick: 1.1.0

  es6-iterator@2.0.3:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-symbol: 3.1.4

  es6-symbol@3.1.4:
    dependencies:
      d: 1.0.2
      ext: 1.7.0

  esbuild@0.23.1:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.23.1
      '@esbuild/android-arm': 0.23.1
      '@esbuild/android-arm64': 0.23.1
      '@esbuild/android-x64': 0.23.1
      '@esbuild/darwin-arm64': 0.23.1
      '@esbuild/darwin-x64': 0.23.1
      '@esbuild/freebsd-arm64': 0.23.1
      '@esbuild/freebsd-x64': 0.23.1
      '@esbuild/linux-arm': 0.23.1
      '@esbuild/linux-arm64': 0.23.1
      '@esbuild/linux-ia32': 0.23.1
      '@esbuild/linux-loong64': 0.23.1
      '@esbuild/linux-mips64el': 0.23.1
      '@esbuild/linux-ppc64': 0.23.1
      '@esbuild/linux-riscv64': 0.23.1
      '@esbuild/linux-s390x': 0.23.1
      '@esbuild/linux-x64': 0.23.1
      '@esbuild/netbsd-x64': 0.23.1
      '@esbuild/openbsd-arm64': 0.23.1
      '@esbuild/openbsd-x64': 0.23.1
      '@esbuild/sunos-x64': 0.23.1
      '@esbuild/win32-arm64': 0.23.1
      '@esbuild/win32-ia32': 0.23.1
      '@esbuild/win32-x64': 0.23.1

  esbuild@0.25.8:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.8
      '@esbuild/android-arm': 0.25.8
      '@esbuild/android-arm64': 0.25.8
      '@esbuild/android-x64': 0.25.8
      '@esbuild/darwin-arm64': 0.25.8
      '@esbuild/darwin-x64': 0.25.8
      '@esbuild/freebsd-arm64': 0.25.8
      '@esbuild/freebsd-x64': 0.25.8
      '@esbuild/linux-arm': 0.25.8
      '@esbuild/linux-arm64': 0.25.8
      '@esbuild/linux-ia32': 0.25.8
      '@esbuild/linux-loong64': 0.25.8
      '@esbuild/linux-mips64el': 0.25.8
      '@esbuild/linux-ppc64': 0.25.8
      '@esbuild/linux-riscv64': 0.25.8
      '@esbuild/linux-s390x': 0.25.8
      '@esbuild/linux-x64': 0.25.8
      '@esbuild/netbsd-arm64': 0.25.8
      '@esbuild/netbsd-x64': 0.25.8
      '@esbuild/openbsd-arm64': 0.25.8
      '@esbuild/openbsd-x64': 0.25.8
      '@esbuild/openharmony-arm64': 0.25.8
      '@esbuild/sunos-x64': 0.25.8
      '@esbuild/win32-arm64': 0.25.8
      '@esbuild/win32-ia32': 0.25.8
      '@esbuild/win32-x64': 0.25.8

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-next@15.0.0(eslint@8.57.1)(typescript@5.6.3):
    dependencies:
      '@next/eslint-plugin-next': 15.0.0
      '@rushstack/eslint-patch': 1.10.5
      '@typescript-eslint/eslint-plugin': 8.22.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint@8.57.1)(typescript@5.6.3)
      '@typescript-eslint/parser': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.7.0(eslint-plugin-import@2.31.0)(eslint@8.57.1)
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1)
      eslint-plugin-jsx-a11y: 6.10.2(eslint@8.57.1)
      eslint-plugin-react: 7.37.4(eslint@8.57.1)
      eslint-plugin-react-hooks: 5.1.0(eslint@8.57.1)
    optionalDependencies:
      typescript: 5.6.3
    transitivePeerDependencies:
      - eslint-import-resolver-webpack
      - eslint-plugin-import-x
      - supports-color

  eslint-config-prettier@9.1.0(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  eslint-import-resolver-typescript@3.7.0(eslint-plugin-import@2.31.0)(eslint@8.57.1):
    dependencies:
      '@nolyfill/is-core-module': 1.0.39
      debug: 4.4.0
      enhanced-resolve: 5.18.0
      eslint: 8.57.1
      fast-glob: 3.3.3
      get-tsconfig: 4.10.0
      is-bun-module: 1.3.0
      is-glob: 4.0.3
      stable-hash: 0.0.4
    optionalDependencies:
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1)
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.7.0(eslint-plugin-import@2.31.0)(eslint@8.57.1)
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-disable@2.0.3(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1
      resolve: 1.22.10

  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(@typescript-eslint/parser@8.22.0(eslint@8.57.1)(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@8.57.1)
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 8.22.0(eslint@8.57.1)(typescript@5.6.3)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    dependencies:
      aria-query: 5.3.2
      array-includes: 3.1.8
      array.prototype.flatmap: 1.3.3
      ast-types-flow: 0.0.8
      axe-core: 4.10.2
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      eslint: 8.57.1
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.1.0
      string.prototype.includes: 2.0.1

  eslint-plugin-react-hooks@5.1.0(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-plugin-react@7.37.4(eslint@8.57.1):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 8.57.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-unicorn@56.0.1(eslint@8.57.1):
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      ci-info: 4.1.0
      clean-regexp: 1.0.0
      core-js-compat: 3.40.0
      eslint: 8.57.1
      esquery: 1.6.0
      globals: 15.14.0
      indent-string: 4.0.0
      is-builtin-module: 3.2.1
      jsesc: 3.1.0
      pluralize: 8.0.0
      read-pkg-up: 7.0.1
      regexp-tree: 0.1.27
      regjsparser: 0.10.0
      semver: 7.7.0
      strip-indent: 3.0.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@8.57.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.3.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  esniff@2.0.1:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      event-emitter: 0.3.5
      type: 2.7.3

  espree@9.6.1:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 3.4.3

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-util-is-identifier-name@3.0.0: {}

  estree-util-visit@2.0.0:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/unist': 3.0.3

  esutils@2.0.3: {}

  event-emitter@0.3.5:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64

  eventemitter3@5.0.1: {}

  events@3.3.0: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expand-tilde@2.0.2:
    dependencies:
      homedir-polyfill: 1.0.3

  ext@1.7.0:
    dependencies:
      type: 2.7.3

  fast-base64-decode@1.0.0: {}

  fast-copy@3.0.2: {}

  fast-deep-equal@2.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.1:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-redact@3.5.0: {}

  fast-safe-stringify@2.1.1: {}

  fast-uri@3.0.6: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.0.5

  fast-xml-parser@5.2.5:
    dependencies:
      strnum: 2.1.1

  fastq@1.18.0:
    dependencies:
      reusify: 1.0.4

  fdir@6.4.3(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  fflate@0.7.4: {}

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-type@19.3.0:
    dependencies:
      strtok3: 8.1.0
      token-types: 6.0.0
      uint8array-extras: 1.4.0

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-node-modules@2.1.3:
    dependencies:
      findup-sync: 4.0.0
      merge: 2.1.1

  find-root@1.1.0: {}

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  findup-sync@4.0.0:
    dependencies:
      detect-file: 1.0.0
      is-glob: 4.0.3
      micromatch: 4.0.8
      resolve-dir: 1.0.1

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.2: {}

  focus-trap@7.5.4:
    dependencies:
      tabbable: 6.2.0

  fontkit@2.0.4:
    dependencies:
      '@swc/helpers': 0.5.13
      brotli: 1.3.3
      clone: 2.1.2
      dfa: 1.2.0
      fast-deep-equal: 3.1.3
      restructure: 3.0.2
      tiny-inflate: 1.0.3
      unicode-properties: 1.4.1
      unicode-trie: 2.0.0

  for-each@0.3.4:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.0:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  fraction.js@4.3.7: {}

  framer-motion@12.5.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      motion-dom: 12.5.0
      motion-utils: 12.5.0
      tslib: 2.8.1
    optionalDependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  get-east-asian-width@1.3.0: {}

  get-intrinsic@1.2.7:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7

  get-tsconfig@4.10.0:
    dependencies:
      resolve-pkg-maps: 1.0.0

  get-tsconfig@4.8.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  giget@1.2.4:
    dependencies:
      citty: 0.1.6
      consola: 3.4.0
      defu: 6.1.4
      node-fetch-native: 1.6.6
      nypm: 0.5.2
      ohash: 1.1.4
      pathe: 2.0.2
      tar: 6.2.1

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global-modules@1.0.0:
    dependencies:
      global-prefix: 1.0.2
      is-windows: 1.0.2
      resolve-dir: 1.0.1

  global-prefix@1.0.2:
    dependencies:
      expand-tilde: 2.0.2
      homedir-polyfill: 1.0.3
      ini: 1.3.8
      is-windows: 1.0.2
      which: 1.3.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globals@15.14.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  graphql-http@1.22.4(graphql@16.10.0):
    dependencies:
      graphql: 16.10.0

  graphql-playground-html@1.6.30:
    dependencies:
      xss: 1.0.15

  graphql-scalars@1.22.2(graphql@16.10.0):
    dependencies:
      graphql: 16.10.0
      tslib: 2.8.1

  graphql@16.10.0: {}

  handlebars@4.7.8:
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.19.3

  has-bigints@1.1.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  help-me@5.0.0: {}

  hex-rgb@4.3.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  homedir-polyfill@1.0.3:
    dependencies:
      parse-passwd: 1.0.0

  hosted-git-info@2.8.9: {}

  hsl-to-hex@1.0.0:
    dependencies:
      hsl-to-rgb-for-reals: 1.1.1

  hsl-to-rgb-for-reals@1.1.1: {}

  html-dom-parser@5.1.1:
    dependencies:
      domhandler: 5.0.3
      htmlparser2: 10.0.0

  html-react-parser@5.2.5(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      domhandler: 5.0.3
      html-dom-parser: 5.1.1
      react: 19.0.0
      react-property: 2.0.2
      style-to-js: 1.1.16
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  html-to-text@9.0.5:
    dependencies:
      '@selderee/plugin-htmlparser2': 0.11.0
      deepmerge: 4.3.1
      dom-serializer: 2.0.0
      htmlparser2: 8.0.2
      selderee: 0.11.0

  htmlparser2@10.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 6.0.1

  htmlparser2@8.0.2:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 4.5.0

  http-status@2.1.0: {}

  human-signals@5.0.0: {}

  husky@9.1.7: {}

  hyphen@1.10.6: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  image-size@2.0.2: {}

  immutable@4.3.7: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  inline-style-parser@0.2.4: {}

  input-otp@1.4.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  install@0.13.0: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  intl-messageformat@10.7.14:
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.2
      '@formatjs/fast-memoize': 2.2.6
      '@formatjs/icu-messageformat-parser': 2.11.0
      tslib: 2.8.1

  ipaddr.js@2.2.0: {}

  is-alphabetical@2.0.1: {}

  is-alphanumerical@2.0.1:
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.2.7

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.3
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-buffer@1.1.6: {}

  is-builtin-module@3.2.1:
    dependencies:
      builtin-modules: 3.3.0

  is-bun-module@1.3.0:
    dependencies:
      semver: 7.7.0

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.2.7
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-decimal@2.0.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.3

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.3
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-hexadecimal@2.0.1: {}

  is-map@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.3

  is-stream@3.0.0: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.18

  is-url@1.2.4: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.1.0:
    dependencies:
      call-bound: 1.0.3

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.2.7

  is-windows@1.0.2: {}

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isomorphic-unfetch@3.1.0(encoding@0.1.13):
    dependencies:
      node-fetch: 2.7.0(encoding@0.1.13)
      unfetch: 4.2.0
    transitivePeerDependencies:
      - encoding

  isomorphic.js@0.2.5: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.2.7
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  javascript-natural-sort@0.7.1: {}

  jay-peg@1.1.1:
    dependencies:
      restructure: 3.0.2

  jiti@1.21.7: {}

  jiti@2.4.2: {}

  jose@5.9.6: {}

  joycon@3.1.1: {}

  jpeg-exif@1.1.4: {}

  js-beautify@1.15.1:
    dependencies:
      config-chain: 1.1.13
      editorconfig: 1.0.4
      glob: 10.4.5
      js-cookie: 3.0.5
      nopt: 7.2.1

  js-cookie@2.2.1: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-to-typescript@15.0.3:
    dependencies:
      '@apidevtools/json-schema-ref-parser': 11.9.0
      '@types/json-schema': 7.0.15
      '@types/lodash': 4.17.15
      is-glob: 4.0.3
      js-yaml: 4.1.0
      lodash: 4.17.21
      minimist: 1.2.8
      prettier: 3.4.2
      tinyglobby: 0.2.10

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  jsox@1.2.121: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  kareem@2.6.3: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kleur@3.0.3: {}

  language-subtag-registry@0.3.23: {}

  language-tags@1.0.9:
    dependencies:
      language-subtag-registry: 0.3.23

  leac@0.6.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lexical@0.28.0: {}

  lib0@0.2.114:
    dependencies:
      isomorphic.js: 0.2.5

  lilconfig@3.1.3: {}

  linebreak@1.1.0:
    dependencies:
      base64-js: 0.0.8
      unicode-trie: 2.0.0

  lines-and-columns@1.2.4: {}

  lint-staged@15.4.3:
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      debug: 4.4.0
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.2.5
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.7.0
    transitivePeerDependencies:
      - supports-color

  listr2@8.2.5:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.get@4.4.2: {}

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  longest-streak@3.1.0: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  lucide-react@0.461.0(react@19.0.0):
    dependencies:
      react: 19.0.0

  marked@7.0.4: {}

  math-intrinsics@1.1.0: {}

  md-to-react-email@5.0.5(react@19.0.0):
    dependencies:
      marked: 7.0.4
      react: 19.0.0

  md5@2.3.0:
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6

  mdast-util-from-markdown@2.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-jsx@3.1.3:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      parse-entities: 4.0.2
      stringify-entities: 4.0.4
      unist-util-stringify-position: 4.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-phrasing@4.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-markdown@2.1.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4

  mdn-data@2.0.14: {}

  media-engine@1.0.3: {}

  memoize-one@6.0.0: {}

  memory-pager@1.5.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  merge@2.1.1: {}

  micromark-core-commonmark@2.0.2:
    dependencies:
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.0.4
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-extension-mdx-jsx@3.0.1:
    dependencies:
      '@types/acorn': 4.0.6
      '@types/estree': 1.0.6
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      micromark-factory-mdx-expression: 2.0.2
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.2
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1
      vfile-message: 4.0.2

  micromark-factory-destination@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-factory-label@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-factory-mdx-expression@2.0.2:
    dependencies:
      '@types/estree': 1.0.6
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.2
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1
      unist-util-position-from-estree: 2.0.0
      vfile-message: 4.0.2

  micromark-factory-space@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.1

  micromark-factory-title@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-factory-whitespace@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-chunked@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-classify-character@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-combine-extensions@2.0.1:
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-decode-numeric-character-reference@2.0.2:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-decode-string@2.0.1:
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-events-to-acorn@2.0.2:
    dependencies:
      '@types/acorn': 4.0.6
      '@types/estree': 1.0.6
      '@types/unist': 3.0.3
      devlop: 1.1.0
      estree-util-visit: 2.0.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1
      vfile-message: 4.0.2

  micromark-util-html-tag-name@2.0.1: {}

  micromark-util-normalize-identifier@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-resolve-all@2.0.1:
    dependencies:
      micromark-util-types: 2.0.1

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-subtokenize@2.0.4:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.1: {}

  micromark@4.0.1:
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.4.0
      decode-named-character-reference: 1.0.2
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.2
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.0.4
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp@1.0.4: {}

  mlly@1.7.4:
    dependencies:
      acorn: 8.14.0
      pathe: 2.0.2
      pkg-types: 1.3.1
      ufo: 1.5.4

  monaco-editor@0.52.2: {}

  mongodb-connection-string-url@3.0.2:
    dependencies:
      '@types/whatwg-url': 11.0.5
      whatwg-url: 14.1.0

  mongodb@6.16.0(@aws-sdk/credential-providers@3.738.0):
    dependencies:
      '@mongodb-js/saslprep': 1.1.9
      bson: 6.10.4
      mongodb-connection-string-url: 3.0.2
    optionalDependencies:
      '@aws-sdk/credential-providers': 3.738.0

  mongoose-paginate-v2@1.8.5: {}

  mongoose@8.15.1(@aws-sdk/credential-providers@3.738.0):
    dependencies:
      bson: 6.10.4
      kareem: 2.6.3
      mongodb: 6.16.0(@aws-sdk/credential-providers@3.738.0)
      mpath: 0.9.0
      mquery: 5.0.0
      ms: 2.1.3
      sift: 17.1.3
    transitivePeerDependencies:
      - '@aws-sdk/credential-providers'
      - '@mongodb-js/zstd'
      - gcp-metadata
      - kerberos
      - mongodb-client-encryption
      - snappy
      - socks
      - supports-color

  motion-dom@12.5.0:
    dependencies:
      motion-utils: 12.5.0

  motion-utils@12.5.0: {}

  motion@12.5.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      framer-motion: 12.5.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      tslib: 2.8.1
    optionalDependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  mpath@0.9.0: {}

  mquery@5.0.0:
    dependencies:
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.8: {}

  nanoid@5.1.5: {}

  natural-compare@1.4.0: {}

  negotiator@1.0.0: {}

  neo-async@2.6.2: {}

  next-intl@3.26.3(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(react@19.0.0):
    dependencies:
      '@formatjs/intl-localematcher': 0.5.10
      negotiator: 1.0.0
      next: 15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4)
      react: 19.0.0
      use-intl: 3.26.3(react@19.0.0)

  next-logger@5.0.1(next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4))(pino@9.6.0):
    dependencies:
      lilconfig: 3.1.3
      next: 15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4)
    optionalDependencies:
      pino: 9.6.0

  next-themes@0.4.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  next-tick@1.1.0: {}

  next@15.3.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(sass@1.77.4):
    dependencies:
      '@next/env': 15.3.4
      '@swc/counter': 0.1.3
      '@swc/helpers': 0.5.15
      busboy: 1.6.0
      caniuse-lite: 1.0.30001696
      postcss: 8.4.31
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      styled-jsx: 5.1.6(react@19.0.0)
    optionalDependencies:
      '@next/swc-darwin-arm64': 15.3.4
      '@next/swc-darwin-x64': 15.3.4
      '@next/swc-linux-arm64-gnu': 15.3.4
      '@next/swc-linux-arm64-musl': 15.3.4
      '@next/swc-linux-x64-gnu': 15.3.4
      '@next/swc-linux-x64-musl': 15.3.4
      '@next/swc-win32-arm64-msvc': 15.3.4
      '@next/swc-win32-x64-msvc': 15.3.4
      sass: 1.77.4
      sharp: 0.34.2
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros

  node-fetch-native@1.6.6: {}

  node-fetch@2.7.0(encoding@0.1.13):
    dependencies:
      whatwg-url: 5.0.0
    optionalDependencies:
      encoding: 0.1.13

  node-releases@2.0.19: {}

  nodemailer@6.9.16: {}

  nopt@7.2.1:
    dependencies:
      abbrev: 2.0.0

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.10
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-svg-path@1.1.0:
    dependencies:
      svg-arc-to-cubic-bezier: 3.2.0

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nypm@0.5.2:
    dependencies:
      citty: 0.1.6
      consola: 3.4.0
      pathe: 2.0.2
      pkg-types: 1.3.1
      tinyexec: 0.3.2
      ufo: 1.5.4

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.3: {}

  object-keys@1.1.1: {}

  object-to-formdata@4.5.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  ohash@1.1.4: {}

  on-exit-leak-free@2.1.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.2.7
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  pagedjs@0.4.3:
    dependencies:
      '@babel/polyfill': 7.12.1
      '@babel/runtime': 7.26.7
      clear-cut: 2.0.2
      css-tree: 1.1.3
      event-emitter: 0.3.5

  pako@0.2.9: {}

  pako@1.0.11: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-css-color@0.2.1:
    dependencies:
      color-name: 1.1.4
      hex-rgb: 4.3.0

  parse-entities@4.0.2:
    dependencies:
      '@types/unist': 2.0.11
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.0.2
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-passwd@1.0.0: {}

  parse-svg-path@0.1.2: {}

  parseley@0.12.1:
    dependencies:
      leac: 0.6.0
      peberminta: 0.9.0

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-to-regexp@6.3.0: {}

  path-type@4.0.0: {}

  pathe@1.1.2: {}

  pathe@2.0.2: {}

  payload@3.50.0(graphql@16.10.0)(typescript@5.6.3):
    dependencies:
      '@next/env': 15.3.4
      '@payloadcms/translations': 3.50.0
      '@types/busboy': 1.5.4
      ajv: 8.17.1
      bson-objectid: 2.0.4
      busboy: 1.6.0
      ci-info: 4.1.0
      console-table-printer: 2.12.1
      croner: 9.1.0
      dataloader: 2.2.3
      deepmerge: 4.3.1
      file-type: 19.3.0
      get-tsconfig: 4.8.1
      graphql: 16.10.0
      http-status: 2.1.0
      image-size: 2.0.2
      ipaddr.js: 2.2.0
      jose: 5.9.6
      json-schema-to-typescript: 15.0.3
      minimist: 1.2.8
      path-to-regexp: 6.3.0
      pino: 9.5.0
      pino-pretty: 13.0.0
      pluralize: 8.0.0
      qs-esm: 7.0.2
      sanitize-filename: 1.6.3
      scmp: 2.1.0
      ts-essentials: 10.0.3(typescript@5.6.3)
      tsx: 4.20.3
      undici: 7.10.0
      uuid: 10.0.0
      ws: 8.18.0
    transitivePeerDependencies:
      - bufferutil
      - typescript
      - utf-8-validate

  pdfkit@0.17.1:
    dependencies:
      crypto-js: 4.2.0
      fontkit: 2.0.4
      jpeg-exif: 1.1.4
      linebreak: 1.1.0
      png-js: 1.0.0

  peberminta@0.9.0: {}

  peek-readable@5.3.1: {}

  perfect-debounce@1.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.6.0: {}

  pify@2.3.0: {}

  pino-abstract-transport@2.0.0:
    dependencies:
      split2: 4.2.0

  pino-pretty@13.0.0:
    dependencies:
      colorette: 2.0.20
      dateformat: 4.6.3
      fast-copy: 3.0.2
      fast-safe-stringify: 2.1.1
      help-me: 5.0.0
      joycon: 3.1.1
      minimist: 1.2.8
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 2.0.0
      pump: 3.0.2
      secure-json-parse: 2.7.0
      sonic-boom: 4.2.0
      strip-json-comments: 3.1.1

  pino-std-serializers@7.0.0: {}

  pino@9.5.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 2.0.0
      pino-std-serializers: 7.0.0
      process-warning: 4.0.1
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.5.0
      sonic-boom: 4.2.0
      thread-stream: 3.1.0

  pino@9.6.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 2.0.0
      pino-std-serializers: 7.0.0
      process-warning: 4.0.1
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.5.0
      sonic-boom: 4.2.0
      thread-stream: 3.1.0

  pirates@4.0.6: {}

  pkg-types@1.3.1:
    dependencies:
      confbox: 0.1.8
      mlly: 1.7.4
      pathe: 2.0.2

  pluralize@8.0.0: {}

  png-js@1.0.0: {}

  possible-typed-array-names@1.0.0: {}

  postcss-import@15.1.0(postcss@8.5.1):
    dependencies:
      postcss: 8.5.1
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-js@4.0.1(postcss@8.5.1):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.1

  postcss-load-config@4.0.2(postcss@8.5.1):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.7.0
    optionalDependencies:
      postcss: 8.5.1

  postcss-nested@6.2.0(postcss@8.5.1):
    dependencies:
      postcss: 8.5.1
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postcss@8.5.1:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier@3.3.3: {}

  prettier@3.4.2: {}

  prismjs@1.29.0: {}

  prismjs@1.30.0: {}

  process-warning@4.0.1: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proto-list@1.2.4: {}

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@2.3.1: {}

  qs-esm@7.0.2: {}

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  queue-microtask@1.2.3: {}

  queue@6.0.2:
    dependencies:
      inherits: 2.0.4

  quick-format-unescaped@4.0.4: {}

  range-parser@1.2.1: {}

  rc9@2.1.2:
    dependencies:
      defu: 6.1.4
      destr: 2.0.3

  react-datepicker@7.6.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      '@floating-ui/react': 0.27.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      clsx: 2.1.1
      date-fns: 3.6.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  react-dom@19.0.0(react@19.0.0):
    dependencies:
      react: 19.0.0
      scheduler: 0.25.0

  react-error-boundary@3.1.4(react@19.0.0):
    dependencies:
      '@babel/runtime': 7.26.7
      react: 19.0.0

  react-error-boundary@4.1.2(react@19.0.0):
    dependencies:
      '@babel/runtime': 7.26.7
      react: 19.0.0

  react-hook-form@7.57.0(react@19.0.0):
    dependencies:
      react: 19.0.0

  react-hotkeys-hook@4.6.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  react-image-crop@10.1.8(react@19.0.0):
    dependencies:
      react: 19.0.0

  react-is@16.13.1: {}

  react-promise-suspense@0.3.4:
    dependencies:
      fast-deep-equal: 2.0.1

  react-property@2.0.2: {}

  react-remove-scroll-bar@2.3.8(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      react: 19.0.0
      react-style-singleton: 2.2.3(react@19.0.0)(types-react@19.0.0-rc.1)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  react-remove-scroll@2.6.3(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      react: 19.0.0
      react-remove-scroll-bar: 2.3.8(react@19.0.0)(types-react@19.0.0-rc.1)
      react-style-singleton: 2.2.3(react@19.0.0)(types-react@19.0.0-rc.1)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(react@19.0.0)(types-react@19.0.0-rc.1)
      use-sidecar: 1.1.3(react@19.0.0)(types-react@19.0.0-rc.1)
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  react-select@5.9.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      '@babel/runtime': 7.26.7
      '@emotion/cache': 11.14.0
      '@emotion/react': 11.14.0(react@19.0.0)(types-react@19.0.0-rc.1)
      '@floating-ui/dom': 1.6.13
      '@types/react-transition-group': 4.4.12(types-react@19.0.0-rc.1)
      memoize-one: 6.0.0
      prop-types: 15.8.1
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      react-transition-group: 4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      use-isomorphic-layout-effect: 1.2.0(react@19.0.0)(types-react@19.0.0-rc.1)
    transitivePeerDependencies:
      - '@types/react'
      - supports-color

  react-style-singleton@2.2.3(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      get-nonce: 1.0.1
      react: 19.0.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  react-transition-group@4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      '@babel/runtime': 7.26.7
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  react@19.0.0: {}

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  read-pkg-up@7.0.1:
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  read-pkg@5.2.0:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.1: {}

  real-require@0.2.0: {}

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.2.7
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerator-runtime@0.13.11: {}

  regenerator-runtime@0.14.1: {}

  regexp-tree@0.1.27: {}

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regjsparser@0.10.0:
    dependencies:
      jsesc: 0.5.0

  require-from-string@2.0.2: {}

  resend@4.1.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      '@react-email/render': 1.0.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - react
      - react-dom

  resolve-dir@1.0.1:
    dependencies:
      expand-tilde: 2.0.2
      global-modules: 1.0.0

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  restructure@3.0.2: {}

  reusify@1.0.4: {}

  rfdc@1.4.1: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.2.7
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-regex: 1.2.1

  safe-stable-stringify@2.5.0: {}

  safer-buffer@2.1.2:
    optional: true

  sanitize-filename@1.6.3:
    dependencies:
      truncate-utf8-bytes: 1.0.2

  sass@1.77.4:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.7
      source-map-js: 1.2.1

  satori@0.15.2:
    dependencies:
      '@shuding/opentype.js': 1.4.0-beta.0
      css-background-parser: 0.1.0
      css-box-shadow: 1.0.0-3
      css-gradient-parser: 0.0.16
      css-to-react-native: 3.2.0
      emoji-regex-xs: 2.0.1
      escape-html: 1.0.3
      linebreak: 1.1.0
      parse-css-color: 0.2.1
      postcss-value-parser: 4.2.0
      yoga-wasm-web: 0.3.3

  scheduler@0.25.0: {}

  scheduler@0.25.0-rc-603e6108-20241029: {}

  scmp@2.1.0: {}

  secure-json-parse@2.7.0: {}

  selderee@0.11.0:
    dependencies:
      parseley: 0.12.1

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.0: {}

  semver@7.7.2:
    optional: true

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.7
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  sharp@0.33.5:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.3
      semver: 7.7.0
    optionalDependencies:
      '@img/sharp-darwin-arm64': 0.33.5
      '@img/sharp-darwin-x64': 0.33.5
      '@img/sharp-libvips-darwin-arm64': 1.0.4
      '@img/sharp-libvips-darwin-x64': 1.0.4
      '@img/sharp-libvips-linux-arm': 1.0.5
      '@img/sharp-libvips-linux-arm64': 1.0.4
      '@img/sharp-libvips-linux-s390x': 1.0.4
      '@img/sharp-libvips-linux-x64': 1.0.4
      '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
      '@img/sharp-libvips-linuxmusl-x64': 1.0.4
      '@img/sharp-linux-arm': 0.33.5
      '@img/sharp-linux-arm64': 0.33.5
      '@img/sharp-linux-s390x': 0.33.5
      '@img/sharp-linux-x64': 0.33.5
      '@img/sharp-linuxmusl-arm64': 0.33.5
      '@img/sharp-linuxmusl-x64': 0.33.5
      '@img/sharp-wasm32': 0.33.5
      '@img/sharp-win32-ia32': 0.33.5
      '@img/sharp-win32-x64': 0.33.5

  sharp@0.34.2:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.4
      semver: 7.7.2
    optionalDependencies:
      '@img/sharp-darwin-arm64': 0.34.2
      '@img/sharp-darwin-x64': 0.34.2
      '@img/sharp-libvips-darwin-arm64': 1.1.0
      '@img/sharp-libvips-darwin-x64': 1.1.0
      '@img/sharp-libvips-linux-arm': 1.1.0
      '@img/sharp-libvips-linux-arm64': 1.1.0
      '@img/sharp-libvips-linux-ppc64': 1.1.0
      '@img/sharp-libvips-linux-s390x': 1.1.0
      '@img/sharp-libvips-linux-x64': 1.1.0
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
      '@img/sharp-linux-arm': 0.34.2
      '@img/sharp-linux-arm64': 0.34.2
      '@img/sharp-linux-s390x': 0.34.2
      '@img/sharp-linux-x64': 0.34.2
      '@img/sharp-linuxmusl-arm64': 0.34.2
      '@img/sharp-linuxmusl-x64': 0.34.2
      '@img/sharp-wasm32': 0.34.2
      '@img/sharp-win32-arm64': 0.34.2
      '@img/sharp-win32-ia32': 0.34.2
      '@img/sharp-win32-x64': 0.34.2
    optional: true

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.3

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.3
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  sift@17.1.3: {}

  signal-exit@4.1.0: {}

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  simple-wcswidth@1.0.1: {}

  sisteransi@1.0.5: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  sonic-boom@4.2.0:
    dependencies:
      atomic-sleep: 1.0.0

  sonner@1.7.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)

  source-map-js@1.2.1: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  sparse-bitfield@3.0.3:
    dependencies:
      memory-pager: 1.5.0

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  split2@4.2.0: {}

  stable-hash@0.0.4: {}

  state-local@1.0.7: {}

  stream-browserify@3.0.0:
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2

  streamsearch@1.1.0: {}

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string.prototype.codepointat@0.2.1: {}

  string.prototype.includes@2.0.1:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.2.7
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.9

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-final-newline@3.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  stripe@10.17.0:
    dependencies:
      '@types/node': 22.12.0
      qs: 6.14.0

  stripe@18.3.0(@types/node@22.12.0):
    dependencies:
      qs: 6.14.0
    optionalDependencies:
      '@types/node': 22.12.0

  strnum@1.0.5: {}

  strnum@2.1.1: {}

  strtok3@8.1.0:
    dependencies:
      '@tokenizer/token': 0.3.0
      peek-readable: 5.3.1

  style-to-js@1.1.16:
    dependencies:
      style-to-object: 1.0.8

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  styled-jsx@5.1.6(react@19.0.0):
    dependencies:
      client-only: 0.0.1
      react: 19.0.0

  stylis@4.2.0: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-arc-to-cubic-bezier@3.2.0: {}

  tabbable@6.2.0: {}

  tailwind-merge@2.6.0: {}

  tailwindcss-animate@1.0.7(tailwindcss@3.4.17):
    dependencies:
      tailwindcss: 3.4.17

  tailwindcss@3.4.17:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.1
      postcss-import: 15.1.0(postcss@8.5.1)
      postcss-js: 4.0.1(postcss@8.5.1)
      postcss-load-config: 4.0.2(postcss@8.5.1)
      postcss-nested: 6.2.0(postcss@8.5.1)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.1: {}

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  thread-stream@3.1.0:
    dependencies:
      real-require: 0.2.0

  tiny-cookie@2.5.1: {}

  tiny-inflate@1.0.3: {}

  tinyexec@0.3.2: {}

  tinyglobby@0.2.10:
    dependencies:
      fdir: 6.4.3(picomatch@4.0.2)
      picomatch: 4.0.2

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  token-types@6.0.0:
    dependencies:
      '@tokenizer/token': 0.3.0
      ieee754: 1.2.1

  tr46@0.0.3: {}

  tr46@5.0.0:
    dependencies:
      punycode: 2.3.1

  truncate-utf8-bytes@1.0.2:
    dependencies:
      utf8-byte-length: 1.0.5

  ts-api-utils@2.0.0(typescript@5.6.3):
    dependencies:
      typescript: 5.6.3

  ts-essentials@10.0.3(typescript@5.6.3):
    optionalDependencies:
      typescript: 5.6.3

  ts-interface-checker@0.1.13: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsx@4.19.2:
    dependencies:
      esbuild: 0.23.1
      get-tsconfig: 4.10.0
    optionalDependencies:
      fsevents: 2.3.3

  tsx@4.20.3:
    dependencies:
      esbuild: 0.25.8
      get-tsconfig: 4.10.0
    optionalDependencies:
      fsevents: 2.3.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  type-fest@0.6.0: {}

  type-fest@0.8.1: {}

  type@2.7.3: {}

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.4
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.4
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.4
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.0.0
      reflect.getprototypeof: 1.0.10

  types-react-dom@19.0.0-rc.1:
    dependencies:
      '@types/react': 19.0.8

  types-react@19.0.0-rc.1:
    dependencies:
      csstype: 3.1.3

  typescript@5.6.3: {}

  ufo@1.5.4: {}

  uglify-js@3.19.3:
    optional: true

  uint8array-extras@1.4.0: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.20.0: {}

  undici@7.10.0: {}

  unfetch@4.2.0: {}

  unicode-properties@1.4.1:
    dependencies:
      base64-js: 1.5.1
      unicode-trie: 2.0.0

  unicode-trie@2.0.0:
    dependencies:
      pako: 0.2.9
      tiny-inflate: 1.0.3

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position-from-estree@2.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  update-browserslist-db@1.1.2(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-callback-ref@1.3.3(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      react: 19.0.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  use-context-selector@2.0.0(react@19.0.0)(scheduler@0.25.0):
    dependencies:
      react: 19.0.0
      scheduler: 0.25.0

  use-intl@3.26.3(react@19.0.0):
    dependencies:
      '@formatjs/fast-memoize': 2.2.6
      intl-messageformat: 10.7.14
      react: 19.0.0

  use-isomorphic-layout-effect@1.2.0(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      react: 19.0.0
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  use-sidecar@1.1.3(react@19.0.0)(types-react@19.0.0-rc.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 19.0.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': types-react@19.0.0-rc.1

  utf8-byte-length@1.0.5: {}

  util-deprecate@1.0.2: {}

  uuid@10.0.0: {}

  uuid@9.0.1: {}

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  validator@13.12.0: {}

  vaul@1.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1):
    dependencies:
      '@radix-ui/react-dialog': 1.1.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(types-react-dom@19.0.0-rc.1)(types-react@19.0.0-rc.1)
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'

  vfile-message@4.0.2:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vite-compatible-readable-stream@3.6.1:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  webidl-conversions@3.0.1: {}

  webidl-conversions@7.0.0: {}

  whatwg-url@14.1.0:
    dependencies:
      tr46: 5.0.0
      webidl-conversions: 7.0.0

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.1
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.3
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.0
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.18

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.18:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      for-each: 0.3.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wordwrap@1.0.0: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@8.18.0: {}

  xss@1.0.15:
    dependencies:
      commander: 2.20.3
      cssfilter: 0.0.10

  yallist@4.0.0: {}

  yaml@1.10.2: {}

  yaml@2.7.0: {}

  yjs@13.6.23:
    dependencies:
      lib0: 0.2.114

  yocto-queue@0.1.0: {}

  yoga-layout@3.2.1: {}

  yoga-wasm-web@0.3.3: {}

  zod@3.24.1: {}

  zwitch@2.0.4: {}
