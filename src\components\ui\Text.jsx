import { cva } from 'class-variance-authority'
import { cn } from '@utils'

export const textVariants = cva(
  // Base styles that apply to all variants
  'text-slate-900 dark:text-slate-50',
  {
    variants: {
      variant: {
        xs: 'text-xs -tracking-normal',
        sm: 'text-sm leading-4.5 -tracking-normal',
        base: 'text-base -tracking-normal',
        xl: 'text-xl -tracking-tighter',
        '3xl': 'text-2xl lg:text-3xl -tracking-tightest'
      },
      weight: {
        light: 'font-light',
        medium: 'font-medium',
        normal: 'font-normal',
        semibold: 'font-semibold',
        bold: 'font-bold'
      }
    },
    defaultVariants: {
      variant: 'sm',
      weight: 'normal'
    }
  }
)

export const Text = ({
  as: Component = 'p',
  className,
  variant,
  weight,
  children,
  ...props
}) => {
  return (
    <Component className={cn(textVariants({ variant, weight }), className)} {...props}>
      {children}
    </Component>
  )
}
