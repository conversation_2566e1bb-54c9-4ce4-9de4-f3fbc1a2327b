'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { SquarePen } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { Button, Form, FormControl, FormField, FormItem, Input, Text } from '@components'
import { useResume } from '@hooks'

export const EditorTopbar = ({ children }) => {
  return (
    <div className='bg-white border-b border-slate-200 flex items-center justify-between p-4 lg:p-5'>
      {children}
    </div>
  )
}

export const ResumeTitle = ({ title }) => {
  const formRef = useRef(null)
  const { updateResume } = useResume()
  const [isEditResumeName, setIsEditResumeName] = useState(false)

  const form = useForm({
    defaultValues: {
      name: title
    }
  })

  const { control, setFocus, handleSubmit } = form

  const onSubmit = useCallback(
    (data) => {
      updateResume({ name: data.name })
      setIsEditResumeName(false)
    },
    [updateResume, setIsEditResumeName]
  )

  useEffect(() => {
    if (isEditResumeName) {
      setFocus('name')

      const handleClickOutside = (event) => {
        if (formRef.current && !formRef.current.contains(event.target)) {
          handleSubmit(onSubmit)()
        }
      }

      document.addEventListener('mousedown', handleClickOutside)

      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isEditResumeName, setFocus, handleSubmit, onSubmit])

  return (
    <div className='flex items-center gap-1'>
      {isEditResumeName ? (
        <Form {...form}>
          <form ref={formRef} onSubmit={handleSubmit(onSubmit)}>
            <FormField
              control={control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      autoComplete='off'
                      className='border-none rounded-none shadow-none bg-transparent p-0 text-base font-semibold text-slate-700 disabled:bg-transparent disabled:cursor-default placeholder:text-slate-700 disabled:text-slate-700'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
      ) : (
        <Text
          as='span'
          variant='base'
          weight='semibold'
          className='max-w-40 w-full text-slate-700 truncate'>
          {title}
        </Text>
      )}
      {!isEditResumeName && (
        <Button
          type='button'
          onClick={() => setIsEditResumeName(true)}
          variant='icon'
          className='border-none shadow-none p-1'>
          <SquarePen size={20} className='text-slate-500' />
        </Button>
      )}
    </div>
  )
}
