'use client'

import { useCallback, useState } from 'react'
import { Download, Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button } from '@components'
import { useAuth, useResume, useSubscription } from '@hooks'
import { downloadResume, isSubscriptionActive } from '@utils'

export const DownloadReactResume = () => {
  const tc = useTranslations('Common')
  const { currentUser } = useAuth()
  const { updateSubscriptionState } = useSubscription()
  const { resume } = useResume()
  const { design } = resume
  const { font } = design
  const [isLoading, setIsLoading] = useState(false)

  const handleDownload = useCallback(async () => {
    if (!currentUser) return
    if (!isSubscriptionActive(currentUser.subscription)) {
      updateSubscriptionState({
        type: 'updateState',
        payload: { pricingModalOpen: true }
      })
      return
    }
    if (currentUser?.role === 'anon') {
      updateSubscriptionState({
        type: 'updateState',
        payload: { onboarding: true }
      })
      return
    }
    try {
      setIsLoading(true)
      await downloadResume(font)
    } catch (error) {
      console.error('Error downloading resume:', error)
    } finally {
      setIsLoading(false)
    }
  }, [font, currentUser, updateSubscriptionState])

  return (
    <Button size='md' onClick={handleDownload} isLoading={isLoading}>
      {isLoading ? (
        <Loader2 size={16} className='animate-spin' />
      ) : (
        <Download size={16} />
      )}
      <span>{tc('download')}</span>
    </Button>
  )
}
