'use client'

import { createContext, useCallback, useMemo, useReducer } from 'react'
import { useResume } from '@hooks'

export const EditorContext = createContext()

function reducer(state, action) {
  const { type, payload } = action

  switch (type) {
    case 'updateState': {
      return { ...state, ...payload }
    }
    case 'updateSectionsVisibility': {
      return { ...state, sectionsVisibility: payload }
    }
    case 'setActiveTab': {
      return { ...state, activeTab: payload }
    }
    default: {
      return state
    }
  }
}

function createSectionsVisibility(sections, isSheetActive) {
  return sections.length > 0
    ? sections.reduce((accumulator, section, index) => {
        accumulator[isSheetActive ? index : section.id] = section.isVisible
        return accumulator
      }, {})
    : {}
}

function createInitialState() {
  const initialState = {
    activePanelId: null,
    isSheetActive: false,
    visiblityControls: false,
    sectionsVisibility: {},
    activeTab: 'editor'
  }

  return initialState
}

export const EditorProvider = ({ children }) => {
  const { resume, updateResume } = useResume()
  const [state, dispatch] = useReducer(reducer, createInitialState(resume))

  const { isSheetActive, activePanelId, sectionsVisibility, visiblityControls } = state
  const { design } = resume
  const { sections } = design

  const handlePanelClick = useCallback(
    (id) => {
      if (visiblityControls) {
        return
      }

      dispatch({
        type: 'updateState',
        payload: {
          activePanelId: id,
          isSheetActive: true,
          visiblityControls: false,
          sectionsVisibility: createSectionsVisibility(resume.design.sections)
        }
      })
    },
    [dispatch, resume.design.sections, visiblityControls]
  )

  const handleBack = useCallback(() => {
    dispatch({
      type: 'updateState',
      payload: {
        isSheetActive: false,
        sectionVisibility: false,
        visiblityControls: false
      }
    })
  }, [dispatch])

  const updateSectionsVisibility = useCallback(
    (section, index, action = 'update') => {
      const key = isSheetActive ? index : section.id

      let newSectionsVisibility = {}
      newSectionsVisibility =
        action === 'remove'
          ? Object.fromEntries(
              Object.entries(sectionsVisibility)
                .map(([k, v]) => [
                  Number(k) < index ? k : Number(k) > index ? Number(k) - 1 : null,
                  v
                ])
                .filter(([k]) => k !== null)
            )
          : {
              ...state.sectionsVisibility,
              [key]: !sectionsVisibility?.[key] || false
            }

      dispatch({
        type: 'updateSectionsVisibility',
        payload: newSectionsVisibility
      })
    },
    [dispatch, state.sectionsVisibility, isSheetActive, sectionsVisibility]
  )

  const showVisibilityControls = useCallback(() => {
    const activeInnerSection = resume[activePanelId]

    const activeSections = isSheetActive ? activeInnerSection : sections
    const sectionsVisibility = createSectionsVisibility(activeSections, isSheetActive)

    dispatch({
      type: 'updateState',
      payload: { visiblityControls: true, sectionsVisibility: sectionsVisibility }
    })
  }, [dispatch, activePanelId, resume, isSheetActive, sections])

  const saveItemsVisibility = useCallback(() => {
    let payload = {}
    let hasChanges = false
    if (isSheetActive) {
      const activeInnerSection = resume[activePanelId]

      const updatedInnerSections = activeInnerSection.map((section, index) => {
        const newVisibility = sectionsVisibility[index]

        if (section.isVisible !== newVisibility) {
          hasChanges = true
        }

        return {
          ...section,
          isVisible: newVisibility
        }
      })

      payload = {
        [activePanelId]: updatedInnerSections
      }
    } else {
      const updatedSections = resume.design.sections.map((section) => {
        const newVisibility = sectionsVisibility[section.id]

        if (section.isVisible !== newVisibility) {
          hasChanges = true
        }

        return {
          ...section,
          isVisible: newVisibility
        }
      })

      payload = {
        design: {
          ...design,
          sections: updatedSections
        }
      }
    }
    if (hasChanges) {
      updateResume(payload)
    }
    dispatch({
      type: 'updateState',
      payload: { visiblityControls: false }
    })
  }, [
    dispatch,
    isSheetActive,
    updateResume,
    resume,
    activePanelId,
    sectionsVisibility,
    design
  ])

  const setActiveTab = useCallback(
    (tab) => {
      dispatch({
        type: 'setActiveTab',
        payload: tab
      })
    },
    [dispatch]
  )

  const value = useMemo(
    () => ({
      ...state,
      editorHandler: dispatch,
      handlePanelClick,
      handleBack,
      updateSectionsVisibility,
      showVisibilityControls,
      saveItemsVisibility,
      setActiveTab
    }),
    [
      state,
      dispatch,
      handlePanelClick,
      handleBack,
      updateSectionsVisibility,
      showVisibilityControls,
      saveItemsVisibility,
      setActiveTab
    ]
  )

  return <EditorContext.Provider value={value}>{children}</EditorContext.Provider>
}
