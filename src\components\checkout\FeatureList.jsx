import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Files, Sparkles } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Text } from '@components/ui'

export const FeatureList = () => {
  const t = useTranslations('CheckoutPage')
  const features = [
    {
      icon: Files,
      title: t('downloadUnlimitedResumes')
    },
    {
      icon: BadgeCheck,
      title: t('access300ProvenTemplates')
    },
    {
      icon: Sparkles,
      title: t('infiniteAIVariations')
    }
  ]

  return (
    <div className='space-y-6 lg:block hidden'>
      {features.map((feature, index) => (
        <div key={index} className='flex items-center gap-2'>
          <feature.icon size={20} className='text-slate-600' />
          <Text as='h3' variant='base' weight='medium' className='text-slate-700'>
            {feature.title}
          </Text>
        </div>
      ))}
    </div>
  )
}
