'use client'

import { ChevronLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button, Text } from '@components'
import { useSubscription } from '@hooks'
import { VerifyOtpForm } from './VerifyOtpForm'

export const VerifyOtp = ({ setCurrentStep }) => {
  const { subscriptionState } = useSubscription()
  const email = subscriptionState.userEmail
  const t = useTranslations('VerifyOtp')
  return (
    <>
      <div className='flex items-center gap-2 mt-12'>
        <Button
          variant='social'
          size='sm'
          onClick={() => setCurrentStep('need-to-verify')}
          className='rounded-md [&_svg]:size-4 p-1.5'>
          <ChevronLeft className='w-4 h-4' />
        </Button>
        <Text as='h2' weight='semibold' className='text-2xl text-neutral-900'>
          {t('title')}
        </Text>
      </div>
      <Text className='text-neutral-500 mt-2'>
        {t('description', { email: '<EMAIL>' })}
      </Text>
      <VerifyOtpForm email={email} setCurrentStep={setCurrentStep} />
    </>
  )
}
