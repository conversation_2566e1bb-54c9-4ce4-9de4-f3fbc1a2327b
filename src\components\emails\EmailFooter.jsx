import * as React from 'react'
import { Column, Img, Link, Row, Section, Text } from '@react-email/components'

export const EmailFooter = () => {
  return (
    <>
      {/* <Hr className='border border-solid border-secondary my-[26px] mx-0 w-full' /> */}
      {/* <Text className='text-sm text-secondary'>
        This email was sent to {email}.{' '}
        {message || "If you didn't request this email, you can safely ignore it."}
      </Text>
      <Text className='text-sm text-secondary text-center'>
        Copyright ©{new Date().getFullYear()} Swift Resume
      </Text> */}
      <Section className='border-t border-solid border-gray-200 text-gray-600 text-sm font-normal leading-5 font-inter'>
        <Section className='mt-6'>
          <Row className='w-full'>
            <Column className='w-2/3'>
              <Text className='text-gray-600 font-inter text-sm font-normal leading-5 m-0'>
                Copyright ©{new Date().getFullYear()} Swift Resume
              </Text>
            </Column>
            <Column>
              <Section>
                <Row>
                  <Column>
                    <Link
                      // href={`${process.env.BINGO_FACEBOOK_SOCIAL_URL}`}
                      href='#'
                      className='ml-6'>
                      <Img
                        src='https://api.bingocardcreator.com/media/facebook-mail-template-icon.png'
                        width='24'
                        height='24'
                        alt='facebook'
                        className='inline object-cover'
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link
                      // href={`${process.env.BINGO_YOUTUBE_SOCIAL_URL}`}
                      href='#'
                      className='ml-6'>
                      <Img
                        src='https://api.bingocardcreator.com/media/youtube-mail-template-icon.png'
                        width='24'
                        height='24'
                        alt='youtube'
                        className='inline object-cover'
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link
                      // href={`${process.env.BINGO_PINTRATES_SOCIAL_URL}`}
                      href='#'
                      className='ml-6'>
                      <Img
                        src='https://api.bingocardcreator.com/media/pinterest-mail-template-icon.png'
                        width='24'
                        height='24'
                        alt='pinterest'
                        className='inline object-cover'
                      />
                    </Link>
                  </Column>
                </Row>
              </Section>
            </Column>
          </Row>
        </Section>
      </Section>
    </>
  )
}
