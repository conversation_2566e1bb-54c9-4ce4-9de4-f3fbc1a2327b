// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, type Options } from '@hey-api/client-fetch';
import type { FindUsersError, FindUsersResponse, CreateUserData, CreateUserError, CreateUserResponse, UpdateUsersData, UpdateUsersError, UpdateUsersResponse, DeleteUsersError, DeleteUsersResponse, FindUserByIdData, FindUserByIdError, FindUserByIdResponse, UpdateUserByIdData, UpdateUserByIdError, UpdateUserByIdResponse, DeleteUserByIdData, DeleteUserByIdError, DeleteUserByIdResponse, CountUsersError, CountUsersResponse, LoginError, LoginResponse, LogoutError, LogoutResponse, UnlockData, UnlockError, UnlockResponse, RefreshTokenError, RefreshTokenResponse, CurrentUserError, CurrentUserResponse, ForgotPasswordData, ForgotPasswordError, ForgotPasswordResponse, ResetPasswordData, ResetPasswordError, ResetPasswordResponse, VerifyTokenError, VerifyTokenResponse, FindCategoriesError, FindCategoriesResponse, CreateCategoryData, CreateCategoryError, CreateCategoryResponse, UpdateCategoriesData, UpdateCategoriesError, UpdateCategoriesResponse, DeleteCategoriesError, DeleteCategoriesResponse, FindCategoryByIdData, FindCategoryByIdError, FindCategoryByIdResponse, UpdateCategoryByIdData, UpdateCategoryByIdError, UpdateCategoryByIdResponse, DeleteCategoryByIdData, DeleteCategoryByIdError, DeleteCategoryByIdResponse, CountCategoriesError, CountCategoriesResponse, FindPagesError, FindPagesResponse, CreatePageData, CreatePageError, CreatePageResponse, UpdatePagesData, UpdatePagesError, UpdatePagesResponse, DeletePagesError, DeletePagesResponse, FindPageByIdData, FindPageByIdError, FindPageByIdResponse, UpdatePageByIdData, UpdatePageByIdError, UpdatePageByIdResponse, DeletePageByIdData, DeletePageByIdError, DeletePageByIdResponse, CountPagesError, CountPagesResponse, FindPostsError, FindPostsResponse, CreatePostData, CreatePostError, CreatePostResponse, UpdatePostsData, UpdatePostsError, UpdatePostsResponse, DeletePostsError, DeletePostsResponse, FindPostByIdData, FindPostByIdError, FindPostByIdResponse, UpdatePostByIdData, UpdatePostByIdError, UpdatePostByIdResponse, DeletePostByIdData, DeletePostByIdError, DeletePostByIdResponse, CountPostsError, CountPostsResponse, FindMediaError, FindMediaResponse, CreateMediaData, CreateMediaError, CreateMediaResponse, UpdateMediaData, UpdateMediaError, UpdateMediaResponse, DeleteMediaError, DeleteMediaResponse, FindMediaByIdData, FindMediaByIdError, FindMediaByIdResponse, UpdateMediaByIdData, UpdateMediaByIdError, UpdateMediaByIdResponse, DeleteMediaByIdData, DeleteMediaByIdError, DeleteMediaByIdResponse, CountMediaError, CountMediaResponse, FindTemplatesError, FindTemplatesResponse, CreateTemplateData, CreateTemplateError, CreateTemplateResponse, UpdateTemplatesData, UpdateTemplatesError, UpdateTemplatesResponse, DeleteTemplatesError, DeleteTemplatesResponse, FindTemplateByIdData, FindTemplateByIdError, FindTemplateByIdResponse, UpdateTemplateByIdData, UpdateTemplateByIdError, UpdateTemplateByIdResponse, DeleteTemplateByIdData, DeleteTemplateByIdError, DeleteTemplateByIdResponse, CountTemplatesError, CountTemplatesResponse, FindResumesError, FindResumesResponse, CreateResumeData, CreateResumeError, CreateResumeResponse, UpdateResumesData, UpdateResumesError, UpdateResumesResponse, DeleteResumesError, DeleteResumesResponse, FindResumeByIdData, FindResumeByIdError, FindResumeByIdResponse, UpdateResumeByIdData, UpdateResumeByIdError, UpdateResumeByIdResponse, DeleteResumeByIdData, DeleteResumeByIdError, DeleteResumeByIdResponse, CountResumesError, CountResumesResponse, FindResumeExamplesError, FindResumeExamplesResponse, CreateResumeExampleData, CreateResumeExampleError, CreateResumeExampleResponse, UpdateResumeExamplesData, UpdateResumeExamplesError, UpdateResumeExamplesResponse, DeleteResumeExamplesError, DeleteResumeExamplesResponse, FindResumeExampleByIdData, FindResumeExampleByIdError, FindResumeExampleByIdResponse, UpdateResumeExampleByIdData, UpdateResumeExampleByIdError, UpdateResumeExampleByIdResponse, DeleteResumeExampleByIdData, DeleteResumeExampleByIdError, DeleteResumeExampleByIdResponse, CountResumeExamplesError, CountResumeExamplesResponse, FindProductsError, FindProductsResponse, CreateProductData, CreateProductError, CreateProductResponse, UpdateProductsData, UpdateProductsError, UpdateProductsResponse, DeleteProductsError, DeleteProductsResponse, FindProductByIdData, FindProductByIdError, FindProductByIdResponse, UpdateProductByIdData, UpdateProductByIdError, UpdateProductByIdResponse, DeleteProductByIdData, DeleteProductByIdError, DeleteProductByIdResponse, CountProductsError, CountProductsResponse, FindSubscriptionsError, FindSubscriptionsResponse, CreateSubscriptionData, CreateSubscriptionError, CreateSubscriptionResponse, UpdateSubscriptionsData, UpdateSubscriptionsError, UpdateSubscriptionsResponse, DeleteSubscriptionsError, DeleteSubscriptionsResponse, FindSubscriptionByIdData, FindSubscriptionByIdError, FindSubscriptionByIdResponse, UpdateSubscriptionByIdData, UpdateSubscriptionByIdError, UpdateSubscriptionByIdResponse, DeleteSubscriptionByIdData, DeleteSubscriptionByIdError, DeleteSubscriptionByIdResponse, CountSubscriptionsError, CountSubscriptionsResponse, FindRedirectsError, FindRedirectsResponse, CreateRedirectData, CreateRedirectError, CreateRedirectResponse, UpdateRedirectsData, UpdateRedirectsError, UpdateRedirectsResponse, DeleteRedirectsError, DeleteRedirectsResponse, FindRedirectByIdData, FindRedirectByIdError, FindRedirectByIdResponse, UpdateRedirectByIdData, UpdateRedirectByIdError, UpdateRedirectByIdResponse, DeleteRedirectByIdData, DeleteRedirectByIdError, DeleteRedirectByIdResponse, CountRedirectsError, CountRedirectsResponse, FindFormsError, FindFormsResponse, CreateFormData, CreateFormError, CreateFormResponse, UpdateFormsData, UpdateFormsError, UpdateFormsResponse, DeleteFormsError, DeleteFormsResponse, FindFormByIdData, FindFormByIdError, FindFormByIdResponse, UpdateFormByIdData, UpdateFormByIdError, UpdateFormByIdResponse, DeleteFormByIdData, DeleteFormByIdError, DeleteFormByIdResponse, CountFormsError, CountFormsResponse, FindFormSubmissionsError, FindFormSubmissionsResponse, CreateFormSubmissionData, CreateFormSubmissionError, CreateFormSubmissionResponse, UpdateFormSubmissionsData, UpdateFormSubmissionsError, UpdateFormSubmissionsResponse, DeleteFormSubmissionsError, DeleteFormSubmissionsResponse, FindFormSubmissionByIdData, FindFormSubmissionByIdError, FindFormSubmissionByIdResponse, UpdateFormSubmissionByIdData, UpdateFormSubmissionByIdError, UpdateFormSubmissionByIdResponse, DeleteFormSubmissionByIdData, DeleteFormSubmissionByIdError, DeleteFormSubmissionByIdResponse, CountFormSubmissionsError, CountFormSubmissionsResponse, FindSearchResultsError, FindSearchResultsResponse, CreateSearchResultData, CreateSearchResultError, CreateSearchResultResponse, UpdateSearchResultsData, UpdateSearchResultsError, UpdateSearchResultsResponse, DeleteSearchResultsError, DeleteSearchResultsResponse, FindSearchResultByIdData, FindSearchResultByIdError, FindSearchResultByIdResponse, UpdateSearchResultByIdData, UpdateSearchResultByIdError, UpdateSearchResultByIdResponse, DeleteSearchResultByIdData, DeleteSearchResultByIdError, DeleteSearchResultByIdResponse, CountSearchResultsError, CountSearchResultsResponse, FindPayloadJobsError, FindPayloadJobsResponse, CreatePayloadJobData, CreatePayloadJobError, CreatePayloadJobResponse, UpdatePayloadJobsData, UpdatePayloadJobsError, UpdatePayloadJobsResponse, DeletePayloadJobsError, DeletePayloadJobsResponse, FindPayloadJobByIdData, FindPayloadJobByIdError, FindPayloadJobByIdResponse, UpdatePayloadJobByIdData, UpdatePayloadJobByIdError, UpdatePayloadJobByIdResponse, DeletePayloadJobByIdData, DeletePayloadJobByIdError, DeletePayloadJobByIdResponse, CountPayloadJobsError, CountPayloadJobsResponse, RequestOtpData, RequestOtpError, RequestOtpResponse, VerifyOtpData, VerifyOtpError, VerifyOtpResponse, GetCountryBasedProductsListData, GetCountryBasedProductsListError, GetCountryBasedProductsListResponse } from './types.gen';

export const client = createClient(createConfig());

/**
 * List all users
 */
export const findUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindUsersResponse, FindUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Create a new user
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateUserResponse, CreateUserError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Update users
 */
export const updateUsers = <ThrowOnError extends boolean = false>(options?: Options<UpdateUsersData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateUsersResponse, UpdateUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Delete users
 */
export const deleteUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteUsersResponse, DeleteUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Retrieve a user by ID
 */
export const findUserById = <ThrowOnError extends boolean = false>(options: Options<FindUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindUserByIdResponse, FindUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Update a user by ID
 */
export const updateUserById = <ThrowOnError extends boolean = false>(options: Options<UpdateUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateUserByIdResponse, UpdateUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Delete a user by ID
 */
export const deleteUserById = <ThrowOnError extends boolean = false>(options: Options<DeleteUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteUserByIdResponse, DeleteUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Count of users
 */
export const countUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountUsersResponse, CountUsersError, ThrowOnError>({
        ...options,
        url: '/api/users/count'
    });
};

/**
 * Login
 */
export const login = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<LoginResponse, LoginError, ThrowOnError>({
        ...options,
        url: '/api/users/login'
    });
};

/**
 * Logout
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<LogoutResponse, LogoutError, ThrowOnError>({
        ...options,
        url: '/api/users/logout'
    });
};

/**
 * Unlock
 */
export const unlock = <ThrowOnError extends boolean = false>(options?: Options<UnlockData, ThrowOnError>) => {
    return (options?.client ?? client).post<UnlockResponse, UnlockError, ThrowOnError>({
        ...options,
        url: '/api/users/unlock'
    });
};

/**
 * Refresh token
 */
export const refreshToken = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<RefreshTokenResponse, RefreshTokenError, ThrowOnError>({
        ...options,
        url: '/api/users/refresh-token'
    });
};

/**
 * Current user
 */
export const currentUser = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CurrentUserResponse, CurrentUserError, ThrowOnError>({
        ...options,
        url: '/api/users/me'
    });
};

/**
 * Forgot password
 */
export const forgotPassword = <ThrowOnError extends boolean = false>(options?: Options<ForgotPasswordData, ThrowOnError>) => {
    return (options?.client ?? client).post<ForgotPasswordResponse, ForgotPasswordError, ThrowOnError>({
        ...options,
        url: '/api/users/forgot-password'
    });
};

/**
 * Reset password
 */
export const resetPassword = <ThrowOnError extends boolean = false>(options?: Options<ResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? client).post<ResetPasswordResponse, ResetPasswordError, ThrowOnError>({
        ...options,
        url: '/api/users/reset-password'
    });
};

/**
 * Verify token
 */
export const verifyToken = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<VerifyTokenResponse, VerifyTokenError, ThrowOnError>({
        ...options,
        url: '/api/users/verify/{token}'
    });
};

/**
 * List all categories
 */
export const findCategories = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindCategoriesResponse, FindCategoriesError, ThrowOnError>({
        ...options,
        url: '/api/categories'
    });
};

/**
 * Create a new category
 */
export const createCategory = <ThrowOnError extends boolean = false>(options?: Options<CreateCategoryData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateCategoryResponse, CreateCategoryError, ThrowOnError>({
        ...options,
        url: '/api/categories'
    });
};

/**
 * Update categories
 */
export const updateCategories = <ThrowOnError extends boolean = false>(options?: Options<UpdateCategoriesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateCategoriesResponse, UpdateCategoriesError, ThrowOnError>({
        ...options,
        url: '/api/categories'
    });
};

/**
 * Delete categories
 */
export const deleteCategories = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteCategoriesResponse, DeleteCategoriesError, ThrowOnError>({
        ...options,
        url: '/api/categories'
    });
};

/**
 * Retrieve a category by ID
 */
export const findCategoryById = <ThrowOnError extends boolean = false>(options: Options<FindCategoryByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindCategoryByIdResponse, FindCategoryByIdError, ThrowOnError>({
        ...options,
        url: '/api/categories/{id}'
    });
};

/**
 * Update a category by ID
 */
export const updateCategoryById = <ThrowOnError extends boolean = false>(options: Options<UpdateCategoryByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateCategoryByIdResponse, UpdateCategoryByIdError, ThrowOnError>({
        ...options,
        url: '/api/categories/{id}'
    });
};

/**
 * Delete a category by ID
 */
export const deleteCategoryById = <ThrowOnError extends boolean = false>(options: Options<DeleteCategoryByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteCategoryByIdResponse, DeleteCategoryByIdError, ThrowOnError>({
        ...options,
        url: '/api/categories/{id}'
    });
};

/**
 * Count of categories
 */
export const countCategories = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountCategoriesResponse, CountCategoriesError, ThrowOnError>({
        ...options,
        url: '/api/categories/count'
    });
};

/**
 * List all pages
 */
export const findPages = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPagesResponse, FindPagesError, ThrowOnError>({
        ...options,
        url: '/api/pages'
    });
};

/**
 * Create a new page
 */
export const createPage = <ThrowOnError extends boolean = false>(options?: Options<CreatePageData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreatePageResponse, CreatePageError, ThrowOnError>({
        ...options,
        url: '/api/pages'
    });
};

/**
 * Update pages
 */
export const updatePages = <ThrowOnError extends boolean = false>(options?: Options<UpdatePagesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePagesResponse, UpdatePagesError, ThrowOnError>({
        ...options,
        url: '/api/pages'
    });
};

/**
 * Delete pages
 */
export const deletePages = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePagesResponse, DeletePagesError, ThrowOnError>({
        ...options,
        url: '/api/pages'
    });
};

/**
 * Retrieve a page by ID
 */
export const findPageById = <ThrowOnError extends boolean = false>(options: Options<FindPageByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPageByIdResponse, FindPageByIdError, ThrowOnError>({
        ...options,
        url: '/api/pages/{id}'
    });
};

/**
 * Update a page by ID
 */
export const updatePageById = <ThrowOnError extends boolean = false>(options: Options<UpdatePageByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePageByIdResponse, UpdatePageByIdError, ThrowOnError>({
        ...options,
        url: '/api/pages/{id}'
    });
};

/**
 * Delete a page by ID
 */
export const deletePageById = <ThrowOnError extends boolean = false>(options: Options<DeletePageByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePageByIdResponse, DeletePageByIdError, ThrowOnError>({
        ...options,
        url: '/api/pages/{id}'
    });
};

/**
 * Count of pages
 */
export const countPages = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountPagesResponse, CountPagesError, ThrowOnError>({
        ...options,
        url: '/api/pages/count'
    });
};

/**
 * List all posts
 */
export const findPosts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPostsResponse, FindPostsError, ThrowOnError>({
        ...options,
        url: '/api/posts'
    });
};

/**
 * Create a new post
 */
export const createPost = <ThrowOnError extends boolean = false>(options?: Options<CreatePostData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreatePostResponse, CreatePostError, ThrowOnError>({
        ...options,
        url: '/api/posts'
    });
};

/**
 * Update posts
 */
export const updatePosts = <ThrowOnError extends boolean = false>(options?: Options<UpdatePostsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePostsResponse, UpdatePostsError, ThrowOnError>({
        ...options,
        url: '/api/posts'
    });
};

/**
 * Delete posts
 */
export const deletePosts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePostsResponse, DeletePostsError, ThrowOnError>({
        ...options,
        url: '/api/posts'
    });
};

/**
 * Retrieve a post by ID
 */
export const findPostById = <ThrowOnError extends boolean = false>(options: Options<FindPostByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPostByIdResponse, FindPostByIdError, ThrowOnError>({
        ...options,
        url: '/api/posts/{id}'
    });
};

/**
 * Update a post by ID
 */
export const updatePostById = <ThrowOnError extends boolean = false>(options: Options<UpdatePostByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePostByIdResponse, UpdatePostByIdError, ThrowOnError>({
        ...options,
        url: '/api/posts/{id}'
    });
};

/**
 * Delete a post by ID
 */
export const deletePostById = <ThrowOnError extends boolean = false>(options: Options<DeletePostByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePostByIdResponse, DeletePostByIdError, ThrowOnError>({
        ...options,
        url: '/api/posts/{id}'
    });
};

/**
 * Count of posts
 */
export const countPosts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountPostsResponse, CountPostsError, ThrowOnError>({
        ...options,
        url: '/api/posts/count'
    });
};

/**
 * List all media
 */
export const findMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindMediaResponse, FindMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Create a new media
 */
export const createMedia = <ThrowOnError extends boolean = false>(options?: Options<CreateMediaData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateMediaResponse, CreateMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Update media
 */
export const updateMedia = <ThrowOnError extends boolean = false>(options?: Options<UpdateMediaData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateMediaResponse, UpdateMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Delete media
 */
export const deleteMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteMediaResponse, DeleteMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Retrieve a media by ID
 */
export const findMediaById = <ThrowOnError extends boolean = false>(options: Options<FindMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindMediaByIdResponse, FindMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Update a media by ID
 */
export const updateMediaById = <ThrowOnError extends boolean = false>(options: Options<UpdateMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateMediaByIdResponse, UpdateMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Delete a media by ID
 */
export const deleteMediaById = <ThrowOnError extends boolean = false>(options: Options<DeleteMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteMediaByIdResponse, DeleteMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Count of media
 */
export const countMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountMediaResponse, CountMediaError, ThrowOnError>({
        ...options,
        url: '/api/media/count'
    });
};

/**
 * List all templates
 */
export const findTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindTemplatesResponse, FindTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Create a new template
 */
export const createTemplate = <ThrowOnError extends boolean = false>(options?: Options<CreateTemplateData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateTemplateResponse, CreateTemplateError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Update templates
 */
export const updateTemplates = <ThrowOnError extends boolean = false>(options?: Options<UpdateTemplatesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateTemplatesResponse, UpdateTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Delete templates
 */
export const deleteTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteTemplatesResponse, DeleteTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Retrieve a template by ID
 */
export const findTemplateById = <ThrowOnError extends boolean = false>(options: Options<FindTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindTemplateByIdResponse, FindTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Update a template by ID
 */
export const updateTemplateById = <ThrowOnError extends boolean = false>(options: Options<UpdateTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateTemplateByIdResponse, UpdateTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Delete a template by ID
 */
export const deleteTemplateById = <ThrowOnError extends boolean = false>(options: Options<DeleteTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteTemplateByIdResponse, DeleteTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Count of templates
 */
export const countTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountTemplatesResponse, CountTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates/count'
    });
};

/**
 * List all resumes
 */
export const findResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumesResponse, FindResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Create a new resume
 */
export const createResume = <ThrowOnError extends boolean = false>(options?: Options<CreateResumeData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateResumeResponse, CreateResumeError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Update resumes
 */
export const updateResumes = <ThrowOnError extends boolean = false>(options?: Options<UpdateResumesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumesResponse, UpdateResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Delete resumes
 */
export const deleteResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumesResponse, DeleteResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Retrieve a resume by ID
 */
export const findResumeById = <ThrowOnError extends boolean = false>(options: Options<FindResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeByIdResponse, FindResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Update a resume by ID
 */
export const updateResumeById = <ThrowOnError extends boolean = false>(options: Options<UpdateResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeByIdResponse, UpdateResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Delete a resume by ID
 */
export const deleteResumeById = <ThrowOnError extends boolean = false>(options: Options<DeleteResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeByIdResponse, DeleteResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Count of resumes
 */
export const countResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountResumesResponse, CountResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes/count'
    });
};

/**
 * List all resume examples
 */
export const findResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeExamplesResponse, FindResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Create a new resume example
 */
export const createResumeExample = <ThrowOnError extends boolean = false>(options?: Options<CreateResumeExampleData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateResumeExampleResponse, CreateResumeExampleError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Update resume examples
 */
export const updateResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<UpdateResumeExamplesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeExamplesResponse, UpdateResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Delete resume examples
 */
export const deleteResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeExamplesResponse, DeleteResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Retrieve a resume example by ID
 */
export const findResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<FindResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeExampleByIdResponse, FindResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Update a resume example by ID
 */
export const updateResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<UpdateResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeExampleByIdResponse, UpdateResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Delete a resume example by ID
 */
export const deleteResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<DeleteResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeExampleByIdResponse, DeleteResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Count of resume examples
 */
export const countResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountResumeExamplesResponse, CountResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/count'
    });
};

/**
 * List all products
 */
export const findProducts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindProductsResponse, FindProductsError, ThrowOnError>({
        ...options,
        url: '/api/products'
    });
};

/**
 * Create a new product
 */
export const createProduct = <ThrowOnError extends boolean = false>(options?: Options<CreateProductData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateProductResponse, CreateProductError, ThrowOnError>({
        ...options,
        url: '/api/products'
    });
};

/**
 * Update products
 */
export const updateProducts = <ThrowOnError extends boolean = false>(options?: Options<UpdateProductsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateProductsResponse, UpdateProductsError, ThrowOnError>({
        ...options,
        url: '/api/products'
    });
};

/**
 * Delete products
 */
export const deleteProducts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteProductsResponse, DeleteProductsError, ThrowOnError>({
        ...options,
        url: '/api/products'
    });
};

/**
 * Retrieve a product by ID
 */
export const findProductById = <ThrowOnError extends boolean = false>(options: Options<FindProductByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindProductByIdResponse, FindProductByIdError, ThrowOnError>({
        ...options,
        url: '/api/products/{id}'
    });
};

/**
 * Update a product by ID
 */
export const updateProductById = <ThrowOnError extends boolean = false>(options: Options<UpdateProductByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateProductByIdResponse, UpdateProductByIdError, ThrowOnError>({
        ...options,
        url: '/api/products/{id}'
    });
};

/**
 * Delete a product by ID
 */
export const deleteProductById = <ThrowOnError extends boolean = false>(options: Options<DeleteProductByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteProductByIdResponse, DeleteProductByIdError, ThrowOnError>({
        ...options,
        url: '/api/products/{id}'
    });
};

/**
 * Count of products
 */
export const countProducts = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountProductsResponse, CountProductsError, ThrowOnError>({
        ...options,
        url: '/api/products/count'
    });
};

/**
 * List all subscriptions
 */
export const findSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindSubscriptionsResponse, FindSubscriptionsError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions'
    });
};

/**
 * Create a new subscription
 */
export const createSubscription = <ThrowOnError extends boolean = false>(options?: Options<CreateSubscriptionData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateSubscriptionResponse, CreateSubscriptionError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions'
    });
};

/**
 * Update subscriptions
 */
export const updateSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<UpdateSubscriptionsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateSubscriptionsResponse, UpdateSubscriptionsError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions'
    });
};

/**
 * Delete subscriptions
 */
export const deleteSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteSubscriptionsResponse, DeleteSubscriptionsError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions'
    });
};

/**
 * Retrieve a subscription by ID
 */
export const findSubscriptionById = <ThrowOnError extends boolean = false>(options: Options<FindSubscriptionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindSubscriptionByIdResponse, FindSubscriptionByIdError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions/{id}'
    });
};

/**
 * Update a subscription by ID
 */
export const updateSubscriptionById = <ThrowOnError extends boolean = false>(options: Options<UpdateSubscriptionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateSubscriptionByIdResponse, UpdateSubscriptionByIdError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions/{id}'
    });
};

/**
 * Delete a subscription by ID
 */
export const deleteSubscriptionById = <ThrowOnError extends boolean = false>(options: Options<DeleteSubscriptionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteSubscriptionByIdResponse, DeleteSubscriptionByIdError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions/{id}'
    });
};

/**
 * Count of subscriptions
 */
export const countSubscriptions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountSubscriptionsResponse, CountSubscriptionsError, ThrowOnError>({
        ...options,
        url: '/api/subscriptions/count'
    });
};

/**
 * List all redirects
 */
export const findRedirects = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindRedirectsResponse, FindRedirectsError, ThrowOnError>({
        ...options,
        url: '/api/redirects'
    });
};

/**
 * Create a new redirect
 */
export const createRedirect = <ThrowOnError extends boolean = false>(options?: Options<CreateRedirectData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateRedirectResponse, CreateRedirectError, ThrowOnError>({
        ...options,
        url: '/api/redirects'
    });
};

/**
 * Update redirects
 */
export const updateRedirects = <ThrowOnError extends boolean = false>(options?: Options<UpdateRedirectsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateRedirectsResponse, UpdateRedirectsError, ThrowOnError>({
        ...options,
        url: '/api/redirects'
    });
};

/**
 * Delete redirects
 */
export const deleteRedirects = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteRedirectsResponse, DeleteRedirectsError, ThrowOnError>({
        ...options,
        url: '/api/redirects'
    });
};

/**
 * Retrieve a redirect by ID
 */
export const findRedirectById = <ThrowOnError extends boolean = false>(options: Options<FindRedirectByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindRedirectByIdResponse, FindRedirectByIdError, ThrowOnError>({
        ...options,
        url: '/api/redirects/{id}'
    });
};

/**
 * Update a redirect by ID
 */
export const updateRedirectById = <ThrowOnError extends boolean = false>(options: Options<UpdateRedirectByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateRedirectByIdResponse, UpdateRedirectByIdError, ThrowOnError>({
        ...options,
        url: '/api/redirects/{id}'
    });
};

/**
 * Delete a redirect by ID
 */
export const deleteRedirectById = <ThrowOnError extends boolean = false>(options: Options<DeleteRedirectByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteRedirectByIdResponse, DeleteRedirectByIdError, ThrowOnError>({
        ...options,
        url: '/api/redirects/{id}'
    });
};

/**
 * Count of redirects
 */
export const countRedirects = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountRedirectsResponse, CountRedirectsError, ThrowOnError>({
        ...options,
        url: '/api/redirects/count'
    });
};

/**
 * List all forms
 */
export const findForms = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindFormsResponse, FindFormsError, ThrowOnError>({
        ...options,
        url: '/api/forms'
    });
};

/**
 * Create a new form
 */
export const createForm = <ThrowOnError extends boolean = false>(options?: Options<CreateFormData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateFormResponse, CreateFormError, ThrowOnError>({
        ...options,
        url: '/api/forms'
    });
};

/**
 * Update forms
 */
export const updateForms = <ThrowOnError extends boolean = false>(options?: Options<UpdateFormsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateFormsResponse, UpdateFormsError, ThrowOnError>({
        ...options,
        url: '/api/forms'
    });
};

/**
 * Delete forms
 */
export const deleteForms = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteFormsResponse, DeleteFormsError, ThrowOnError>({
        ...options,
        url: '/api/forms'
    });
};

/**
 * Retrieve a form by ID
 */
export const findFormById = <ThrowOnError extends boolean = false>(options: Options<FindFormByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindFormByIdResponse, FindFormByIdError, ThrowOnError>({
        ...options,
        url: '/api/forms/{id}'
    });
};

/**
 * Update a form by ID
 */
export const updateFormById = <ThrowOnError extends boolean = false>(options: Options<UpdateFormByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateFormByIdResponse, UpdateFormByIdError, ThrowOnError>({
        ...options,
        url: '/api/forms/{id}'
    });
};

/**
 * Delete a form by ID
 */
export const deleteFormById = <ThrowOnError extends boolean = false>(options: Options<DeleteFormByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteFormByIdResponse, DeleteFormByIdError, ThrowOnError>({
        ...options,
        url: '/api/forms/{id}'
    });
};

/**
 * Count of forms
 */
export const countForms = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountFormsResponse, CountFormsError, ThrowOnError>({
        ...options,
        url: '/api/forms/count'
    });
};

/**
 * List all form submissions
 */
export const findFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindFormSubmissionsResponse, FindFormSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions'
    });
};

/**
 * Create a new form submission
 */
export const createFormSubmission = <ThrowOnError extends boolean = false>(options?: Options<CreateFormSubmissionData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateFormSubmissionResponse, CreateFormSubmissionError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions'
    });
};

/**
 * Update form submissions
 */
export const updateFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<UpdateFormSubmissionsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateFormSubmissionsResponse, UpdateFormSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions'
    });
};

/**
 * Delete form submissions
 */
export const deleteFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteFormSubmissionsResponse, DeleteFormSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions'
    });
};

/**
 * Retrieve a form submission by ID
 */
export const findFormSubmissionById = <ThrowOnError extends boolean = false>(options: Options<FindFormSubmissionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindFormSubmissionByIdResponse, FindFormSubmissionByIdError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions/{id}'
    });
};

/**
 * Update a form submission by ID
 */
export const updateFormSubmissionById = <ThrowOnError extends boolean = false>(options: Options<UpdateFormSubmissionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateFormSubmissionByIdResponse, UpdateFormSubmissionByIdError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions/{id}'
    });
};

/**
 * Delete a form submission by ID
 */
export const deleteFormSubmissionById = <ThrowOnError extends boolean = false>(options: Options<DeleteFormSubmissionByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteFormSubmissionByIdResponse, DeleteFormSubmissionByIdError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions/{id}'
    });
};

/**
 * Count of form submissions
 */
export const countFormSubmissions = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountFormSubmissionsResponse, CountFormSubmissionsError, ThrowOnError>({
        ...options,
        url: '/api/form-submissions/count'
    });
};

/**
 * List all search results
 */
export const findSearchResults = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindSearchResultsResponse, FindSearchResultsError, ThrowOnError>({
        ...options,
        url: '/api/search'
    });
};

/**
 * Create a new search result
 */
export const createSearchResult = <ThrowOnError extends boolean = false>(options?: Options<CreateSearchResultData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateSearchResultResponse, CreateSearchResultError, ThrowOnError>({
        ...options,
        url: '/api/search'
    });
};

/**
 * Update search results
 */
export const updateSearchResults = <ThrowOnError extends boolean = false>(options?: Options<UpdateSearchResultsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateSearchResultsResponse, UpdateSearchResultsError, ThrowOnError>({
        ...options,
        url: '/api/search'
    });
};

/**
 * Delete search results
 */
export const deleteSearchResults = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteSearchResultsResponse, DeleteSearchResultsError, ThrowOnError>({
        ...options,
        url: '/api/search'
    });
};

/**
 * Retrieve a search result by ID
 */
export const findSearchResultById = <ThrowOnError extends boolean = false>(options: Options<FindSearchResultByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindSearchResultByIdResponse, FindSearchResultByIdError, ThrowOnError>({
        ...options,
        url: '/api/search/{id}'
    });
};

/**
 * Update a search result by ID
 */
export const updateSearchResultById = <ThrowOnError extends boolean = false>(options: Options<UpdateSearchResultByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateSearchResultByIdResponse, UpdateSearchResultByIdError, ThrowOnError>({
        ...options,
        url: '/api/search/{id}'
    });
};

/**
 * Delete a search result by ID
 */
export const deleteSearchResultById = <ThrowOnError extends boolean = false>(options: Options<DeleteSearchResultByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteSearchResultByIdResponse, DeleteSearchResultByIdError, ThrowOnError>({
        ...options,
        url: '/api/search/{id}'
    });
};

/**
 * Count of search results
 */
export const countSearchResults = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountSearchResultsResponse, CountSearchResultsError, ThrowOnError>({
        ...options,
        url: '/api/search/count'
    });
};

/**
 * List all payload jobs
 */
export const findPayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPayloadJobsResponse, FindPayloadJobsError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs'
    });
};

/**
 * Create a new payload job
 */
export const createPayloadJob = <ThrowOnError extends boolean = false>(options?: Options<CreatePayloadJobData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreatePayloadJobResponse, CreatePayloadJobError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs'
    });
};

/**
 * Update payload jobs
 */
export const updatePayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<UpdatePayloadJobsData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePayloadJobsResponse, UpdatePayloadJobsError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs'
    });
};

/**
 * Delete payload jobs
 */
export const deletePayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePayloadJobsResponse, DeletePayloadJobsError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs'
    });
};

/**
 * Retrieve a payload job by ID
 */
export const findPayloadJobById = <ThrowOnError extends boolean = false>(options: Options<FindPayloadJobByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindPayloadJobByIdResponse, FindPayloadJobByIdError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs/{id}'
    });
};

/**
 * Update a payload job by ID
 */
export const updatePayloadJobById = <ThrowOnError extends boolean = false>(options: Options<UpdatePayloadJobByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdatePayloadJobByIdResponse, UpdatePayloadJobByIdError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs/{id}'
    });
};

/**
 * Delete a payload job by ID
 */
export const deletePayloadJobById = <ThrowOnError extends boolean = false>(options: Options<DeletePayloadJobByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeletePayloadJobByIdResponse, DeletePayloadJobByIdError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs/{id}'
    });
};

/**
 * Count of payload jobs
 */
export const countPayloadJobs = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountPayloadJobsResponse, CountPayloadJobsError, ThrowOnError>({
        ...options,
        url: '/api/payload-jobs/count'
    });
};

/**
 * Request OTP
 */
export const requestOtp = <ThrowOnError extends boolean = false>(options: Options<RequestOtpData, ThrowOnError>) => {
    return (options?.client ?? client).post<RequestOtpResponse, RequestOtpError, ThrowOnError>({
        ...options,
        url: '/api/otp/request-otp'
    });
};

/**
 * Verify OTP
 */
export const verifyOtp = <ThrowOnError extends boolean = false>(options: Options<VerifyOtpData, ThrowOnError>) => {
    return (options?.client ?? client).post<VerifyOtpResponse, VerifyOtpError, ThrowOnError>({
        ...options,
        url: '/api/otp/verify-otp'
    });
};

/**
 * Get country-based products list
 */
export const getCountryBasedProductsList = <ThrowOnError extends boolean = false>(options?: Options<GetCountryBasedProductsListData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCountryBasedProductsListResponse, GetCountryBasedProductsListError, ThrowOnError>({
        ...options,
        url: '/api/products/country-based-products-list'
    });
};