'use client'

import * as React from 'react'
import { cva } from 'class-variance-authority'
import * as SeparatorPrimitive from '@radix-ui/react-separator'
import { Text } from '@components'
import { cn } from '@utils'

const Separator = React.forwardRef(
  ({ className, orientation = 'horizontal', decorative = true, ...props }, ref) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'shrink-0 bg-slate-200 dark:bg-slate-800',
        orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-[1px]',
        className
      )}
      {...props}
    />
  )
)
Separator.displayName = SeparatorPrimitive.Root.displayName

const SeparatorWithText = ({
  text,
  className,
  textVariant = 'transperent',
  size = 'default'
}) => {
  const textVariants = cva('text-slate-400 dark:text-slate-50', {
    variants: {
      variant: {
        transperent: 'text-xs -tracking-normal bg-white',
        card: 'text-sm leading-none -tracking-normal font-medium font-uppercase border border-base-200 dark:border-slate-800 rounded bg-white dark:bg-slate-900 text-[#262626] shadow-sm'
      },
      size: {
        default: 'px-1.5',
        sm: 'px-2 py-1'
      }
    },
    defaultVariants: {
      variant: 'transperent',
      size: 'default'
    }
  })

  return (
    <div className={cn('relative my-2', className)}>
      <Separator orientation='horizontal' />
      <div className='absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 flex justify-center'>
        <Text as='span' className={cn(textVariants({ variant: textVariant, size }))}>
          {text}
        </Text>
      </div>
    </div>
  )
}

export { Separator, SeparatorWithText }
