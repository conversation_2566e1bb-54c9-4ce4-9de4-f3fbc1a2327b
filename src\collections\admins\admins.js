import { isAdmin, isAdminOrSelf } from '@collections/access'
import { isValidEmail, isValidPassword } from '@collections/validations'
import { collections } from '@constants'

export const admins = {
  slug: collections.admins.slug,
  admin: {
    useAsTitle: 'email'
  },
  access: {
    create: isAdmin,
    read: isAdminOrSelf,
    update: isAdminOrSelf,
    delete: isAdmin
  },
  auth: {
    depth: 0,
    tokenExpiration: 86_400, // 1 day
    maxLoginAttempts: 3,
    lockTime: 600_000 // 10 mins
  },
  timestamps: true,
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      validate: isValidEmail
    },
    {
      name: 'password',
      type: 'password',
      validate: isValidPassword
    },
    {
      name: 'firstName',
      type: 'text',
      minLength: collections.shared.minLength,
      maxLength: collections.shared.maxLength
    },
    {
      name: 'lastName',
      type: 'text',
      minLength: collections.shared.minLength,
      maxLength: collections.shared.maxLength
    }
  ]
}
