'use client'

import { useCallback, useEffect, useState } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Drawer,
  DrawerContent,
  DrawerTitle,
  Separator,
  SwiftLogo,
  Text
} from '@components'
import { useAuth, useSubscription } from '@hooks'
import { useMediaQuery } from '@raddix/use-media-query'
import { getPriceString } from '@utils/getPriceString'
import { getStripe } from '@utils/getStripejs'
import { CheckoutForm } from './CheckoutForm'
import { FeatureList } from './FeatureList'
import { PlanSelector } from './PlanSelector'

export const CheckoutModal = () => {
  const t = useTranslations('CheckoutPage')
  const { updateSubscriptions } = useAuth()
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  const [loading, setLoading] = useState(false)
  const { subscriptionState, updateSubscriptionState } = useSubscription()
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const payment_intent = searchParams.get('payment_intent')
  const payment_intent_client_secret = searchParams.get('payment_intent_client_secret')
  const subscriptionId = searchParams.get('subscriptionId')

  const { pricingModalOpen, products, currency, currencySymbol } = subscriptionState

  const productsWithTrial = products?.find((product) => product.trialpriceJSON)

  const finalPrice = productsWithTrial?.priceJSON

  const priceString = getPriceString(finalPrice, currency, currencySymbol)

  const stripe = useCallback(async () => {
    if (payment_intent && payment_intent_client_secret) {
      setLoading(true)
      return await getStripe()
    }
  }, [payment_intent, payment_intent_client_secret])

  useEffect(() => {
    if (payment_intent && payment_intent_client_secret) {
      stripe().then((stripeInstance) => {
        setLoading(true)
        stripeInstance
          .retrievePaymentIntent(payment_intent_client_secret)
          .then(async ({ paymentIntent }) => {
            let message = ''
            switch (paymentIntent.status) {
              case 'succeeded': {
                message = t('success')
                break
              }

              case 'processing': {
                message = t('paymentProcessing')
                break
              }

              case 'requires_payment_method': {
                message = t('paymentFailed')
                break
              }

              default: {
                message = t('somethingWentWrong')
                break
              }
            }
            if (paymentIntent.status === 'succeeded') {
              toast.success(message)
              await updateSubscriptions(subscriptionId, {
                status: 'active'
              })
              updateSubscriptionState({
                type: 'updateState',
                payload: {
                  pricingModalOpen: false,
                  onboarding: true
                }
              })
            } else {
              updateSubscriptionState({
                type: 'updateState',
                payload: {
                  paymentMessage: message
                }
              })
            }
            const newSearchParams = new URLSearchParams(searchParams.toString())
            let keysToRemove = [
              'payment_intent_client_secret',
              'payment_intent',
              'redirect_status',
              'setup_intent',
              'setup_intent_client_secret',
              'subscriptionId'
            ]
            for (const key of keysToRemove) {
              newSearchParams.delete(key)
            }

            const finalStringSearch = newSearchParams.toString()

            router.replace(
              finalStringSearch ? `${pathname}?${finalStringSearch}` : pathname
            )

            setLoading(false)
          })
      })
    }
  }, [
    payment_intent,
    payment_intent_client_secret,
    stripe,
    updateSubscriptionState,
    searchParams,
    router,
    pathname,
    updateSubscriptions,
    subscriptionId
  ])

  const modalContent = (
    <div className='flex flex-col lg:flex-row h-full space-y-6 lg:gap-6'>
      <div className='flex-1'>
        <div className='lg:p-5'>
          <SwiftLogo className='lg:block hidden' />
          <Text as='h2' variant='3xl' weight='bold' className='text-foreground lg:mt-12'>
            {t('title')}
          </Text>
          <Text variant='xs' weight='medium' className='text-slate-500 mt-2 mb-6'>
            {t('description')}
          </Text>
          <PlanSelector />
          <FeatureList />
          <Text variant='xs' className='text-slate-400 text-center mt-6 hidden lg:block'>
            {t('checkOutMsg', { priceString })}
          </Text>
        </div>
      </div>
      <Separator className='block lg:hidden' />
      <div className='flex-1'>
        <div className='lg:bg-slate-50 lg:border lg:p-6 lg:border-slate-100 lg:rounded-xl h-full w-full'>
          <CheckoutForm />
          <Text variant='xs' className='text-slate-400 text-center mt-6 block lg:hidden'>
            {t('checkOutMsg', { priceString })}
          </Text>
        </div>
      </div>
    </div>
  )

  if (isDesktop) {
    return (
      <Dialog
        open={pricingModalOpen}
        onOpenChange={(open) => {
          updateSubscriptionState({
            type: 'updateState',
            payload: {
              pricingModalOpen: open
            }
          })
        }}>
        <DialogContent
          className='max-w-[850px] p-4'
          closeButtonClassName='bg-white rounded-full size-5 flex justify-center items-center top-3 right-3'>
          <DialogHeader className='sr-only'>
            <DialogTitle>Checkout</DialogTitle>
          </DialogHeader>
          {modalContent}
          {loading && <PaymentConfirmationLoader />}
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer
      open={pricingModalOpen}
      onOpenChange={(open) => {
        updateSubscriptionState({
          type: 'updateState',
          payload: {
            pricingModalOpen: open
          }
        })
      }}>
      <DrawerContent className='max-h-[90vh]'>
        <DrawerTitle className='sr-only'>Checkout</DrawerTitle>
        <div className='h-full overflow-y-auto p-4'>
          {modalContent}
          {loading && <PaymentConfirmationLoader />}
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export const PaymentConfirmationLoader = () => {
  const t = useTranslations('CheckoutPage')
  return (
    <div className='absolute inset-0 bg-white/80 backdrop-blur-sm z-10 overflow-hidden rounded-md'>
      <div className='h-full flex flex-col items-center justify-center py-12'>
        <div className='flex flex-col items-center space-y-6'>
          {/* Main Loading Animation */}
          <div className='relative'>
            <div className='w-20 h-20 border-4 border-blue-200 rounded-full animate-pulse'></div>
            <div className='w-16 h-16 border-4 border-transparent border-t-blue-600 rounded-full animate-spin absolute top-2 left-2'></div>
            <Loader2 className='w-8 h-8 text-blue-600 animate-spin absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2' />
          </div>

          {/* Bouncing Dots */}
          <div className='flex space-x-2'>
            <div className='w-3 h-3 bg-blue-600 rounded-full animate-bounce'></div>
            <div
              className='w-3 h-3 bg-blue-600 rounded-full animate-bounce'
              style={{ animationDelay: '0.1s' }}></div>
            <div
              className='w-3 h-3 bg-blue-600 rounded-full animate-bounce'
              style={{ animationDelay: '0.2s' }}></div>
          </div>

          {/* Loading Text */}
          <div className='text-center space-y-2'>
            <p className='text-lg font-medium text-gray-700'>{t('processingPayment')}</p>
            <p className='text-sm text-gray-500'>{t('thisMayTakeAFewMoments')}</p>
          </div>

          {/* Progress Bar */}
          <div className='w-full max-w-xs'>
            <div className='w-full bg-gray-200 rounded-full h-2'>
              <div
                className='bg-blue-600 h-2 rounded-full animate-pulse'
                style={{ width: '60%' }}></div>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        .animate-spin-slow {
          animation: spin-slow 3s linear infinite;
        }
      `}</style>
    </div>
  )
}
