import { isAdmin, isAdminOrSelf } from '@collections/access'
import { collections } from '@constants'

export const subscriptions = {
  slug: collections.subscriptions.slug,
  access: {
    create: isAdmin,
    read: isAdminOrSelf,
    update: isAdminOrSelf,
    delete: isAdmin
  },
  timestamps: true,
  admin: {
    defaultColumns: ['subscriptionId', 'status'],
    useAsTitle: 'subscriptionId'
  },
  fields: [
    {
      name: 'subscriptionId',
      label: 'Subscription ID',
      type: 'text',
      unique: true,
      index: true,
      required: true
    },
    {
      name: 'status',
      label: 'Status',
      type: 'text',
      required: false
    },
    {
      name: 'user',
      label: 'User',
      type: 'relationship',
      relationTo: collections.users.slug,
      hasMany: false
    },
    {
      name: 'stripeCustomerId',
      label: 'Stripe Customer ID',
      type: 'text',
      required: false
    }
  ]
}
