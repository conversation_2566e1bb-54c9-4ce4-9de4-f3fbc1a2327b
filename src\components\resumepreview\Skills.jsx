import { resumeHasSidebar, resumeOptionValue } from '@utils'
import { SectionContainer, SectionItems, SectionTitle, SkillName, SkillsList } from './ui'

export const Skills = ({ resumeData, tc, location }) => {
  const { skills, design } = resumeData
  const { layout, palette, headingUnderlineStyle, sections } = design

  const hasSidebar = resumeHasSidebar(layout)
  const sectionData =
    sections.find((section) => section.sectionKey === resumeOptionValue?.skills) || {}

  const sectionTitle =
    resumeOptionValue?.skills === sectionData?.sectionName
      ? tc(resumeOptionValue?.skills)
      : sectionData?.sectionName

  const visibleSkills = skills.filter((skill) => skill.isVisible)

  if (!visibleSkills?.length) return null

  return (
    <SectionContainer layout={layout}>
      <SectionTitle
        title={sectionTitle}
        location={location}
        layout={layout}
        palette={palette}
        headingUnderlineStyle={headingUnderlineStyle}
      />
      <SectionItems layout={layout}>
        <SkillsList location={location}>
          {visibleSkills.map((skill, index) => (
            <SkillName
              key={`skill-${index}`}
              palette={palette}
              location={location}
              hasSidebar={hasSidebar}>
              <span>{skill.skill}</span>
            </SkillName>
          ))}
        </SkillsList>
      </SectionItems>
    </SectionContainer>
  )
}
