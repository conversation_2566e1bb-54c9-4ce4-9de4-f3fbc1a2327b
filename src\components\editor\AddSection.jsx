'use client'

import { useEffect, useRef, useState } from 'react'
import { Eye, EyeOff, GripVertical, Plus, Trash2 } from 'lucide-react'
import { AnimatePresence, Reorder, motion } from 'motion/react'
import { useTranslations } from 'next-intl'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
  Text
} from '@components'
import { useEditor } from '@hooks'
import { cn } from '@utils'

export const AccordionWrapper = ({ children, fields, ...props }) => {
  const [activeAccordion, setActiveAccordion] = useState([])
  const lastLength = useRef(fields.length)

  useEffect(() => {
    if (fields.length > 0 && fields.length > lastLength.current) {
      setActiveAccordion([fields.at(-1).id])
    }
    lastLength.current = fields.length
  }, [fields])

  return (
    <Accordion
      type='multiple'
      value={activeAccordion}
      {...props}
      onValueChange={(value) => {
        setActiveAccordion(value)
      }}>
      {children}
    </Accordion>
  )
}

export const ReorderGroup = ({
  as = 'div',
  axis = 'y',
  className,
  values,
  onReorder,
  children
}) => {
  return (
    <Reorder.Group
      as={as}
      axis={axis}
      className={cn('space-y-4', className)}
      values={values}
      onReorder={onReorder}>
      <AnimatePresence>{children}</AnimatePresence>
    </Reorder.Group>
  )
}

export const ReorderItem = ({
  id,
  value,
  dragListener,
  dragControls,
  className,
  children
}) => {
  return (
    <motion.div
      key={id}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ scale: 0.8, opacity: 0 }}
      transition={{ duration: 0.3 }}>
      <Reorder.Item
        as='div'
        id={id}
        value={value}
        dragListener={dragListener}
        dragControls={dragControls}
        className={className}>
        {children}
      </Reorder.Item>
    </motion.div>
  )
}

export const AddSection = ({ text, onClick }) => {
  const tc = useTranslations('Common')
  const { visiblityControls } = useEditor()

  if (visiblityControls) {
    return null
  }

  return (
    <Button
      type='button'
      variant='subtle'
      size='link'
      onClick={onClick}
      className='w-full justify-start p-4 border border-dashed border-slate-300 rounded-lg transition-colors hover:text-slate-700 hover:border-slate-400 mb-5'>
      <Plus />
      {tc(text)}
    </Button>
  )
}

export const SectionItem = ({
  id,
  value,
  dragListener,
  dragControls,
  children,
  className
}) => {
  return (
    <ReorderItem
      id={id}
      value={value}
      dragListener={dragListener}
      dragControls={dragControls}>
      <AccordionItem
        value={id}
        className={cn(
          'relative border border-slate-200 rounded-lg bg-slate-50 select-none',
          className
        )}>
        {children}
      </AccordionItem>
    </ReorderItem>
  )
}

export const SectionReorder = ({ dragHandler, className, size = 20 }) => {
  return (
    <GripVertical
      size={size}
      className={cn('text-slate-400 cursor-grab flex-shrink-0', className)}
      onPointerDown={dragHandler}
    />
  )
}

export const SectionVisibilityHandler = ({ isVisible, className, onClick }) => {
  return (
    <Button
      type='button'
      variant='subtle'
      size='link'
      className={cn('[&_svg]:size-5', className)}
      onClick={onClick}>
      {isVisible ? <Eye /> : <EyeOff />}
    </Button>
  )
}

export const SectionTrigger = ({ label, className, isVisible }) => {
  return (
    <AccordionTrigger
      className={cn(
        'py-4 select-none hover:no-underline truncate gap-2',
        isVisible ? 'opacity-100' : 'opacity-35',
        className
      )}>
      <Text
        as='span'
        variant='sm'
        weight='medium'
        className='block text-slate-700 truncate'>
        {label}
      </Text>
    </AccordionTrigger>
  )
}

export const SectionRemove = ({ onClick, className }) => {
  return (
    <Button
      type='button'
      variant='subtle'
      size='link'
      className={cn('text-slate-500', className)}
      onClick={onClick}>
      <Trash2 />
    </Button>
  )
}

export const SectionContent = ({ className, children }) => {
  return (
    <AccordionContent
      className={cn(
        'pt-2 px-4 pb-4 space-y-6 divide-y divide-slate-200 [&>*:not(:first-child)]:pt-6',
        className
      )}>
      {children}
    </AccordionContent>
  )
}
