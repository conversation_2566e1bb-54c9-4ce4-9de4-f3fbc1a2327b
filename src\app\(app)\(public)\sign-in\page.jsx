import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { SeparatorWithText, SocialLoginForm, Text } from '@components'
import { auth, routes } from '@constants'
import { SignInForm } from './SignInForm'

export async function generateMetadata() {
  const t = await getTranslations('SignInPage')
  return {
    title: t('metaTitle'),
    description: t('metaDescription')
  }
}

export default async function Page() {
  const t = await getTranslations('SignInPage')
  const tc = await getTranslations('Common')
  return (
    <>
      <div>
        <Text as='h1' variant='3xl' className='text-slate-700 mb-2.5' weight='semibold'>
          {t('title')}
        </Text>
        <Text variant='sm' weight='medium' className='text-slate-700'>
          {t('notMember')}{' '}
          <Link className='text-primary' href={routes.signUp}>
            {tc('signUp')}
          </Link>
        </Text>
      </div>
      <div className='relative'>
        <SocialLoginForm
          provider={auth.social.providers.LINKEDIN}
          label={tc('signInWithLinkedIn')}
        />
        <SeparatorWithText className='my-6' text={tc('orUseEmail')} />
        <SignInForm />
      </div>
    </>
  )
}
