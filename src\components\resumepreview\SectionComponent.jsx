import { resumeOptionValue } from '@utils'
import { AdditionalProficiencies } from './AdditionalProficiencies'
import { CustomSections } from './CustomSections'
import { Education } from './Education'
import { Experience } from './Experience'
import { Skills } from './Skills'

export const SectionComponent = ({ sectionKey, ...props }) => {
  switch (sectionKey) {
    case resumeOptionValue.skills: {
      return <Skills {...props} />
    }
    case resumeOptionValue.education: {
      return <Education {...props} />
    }
    case resumeOptionValue.experience: {
      return <Experience {...props} />
    }
    case resumeOptionValue.customSections: {
      return <CustomSections {...props} />
    }
    case resumeOptionValue.proficiencies: {
      return <AdditionalProficiencies {...props} />
    }
    default: {
      return null
    }
  }
}
