import React from 'react'

export const MediaBlock = ({ media, disableInnerContainer = false }) => {
  if (!media) return null

  const content = (
    <div className='my-8'>
      {media.url && (
        <img
          src={media.url}
          alt={media.alt || 'Media content'}
          className='w-full h-auto rounded-lg'
        />
      )}
    </div>
  )

  if (disableInnerContainer) {
    return content
  }

  return <div className='container my-16'>{content}</div>
}
