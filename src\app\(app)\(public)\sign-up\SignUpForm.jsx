'use client'

import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  Button,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Text
} from '@components'
import { auth, routes, validationSchemas } from '@constants'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'
import { withPasswordValidation } from '@utils'

const registerSchema = withPasswordValidation(
  z.object({
    email: validationSchemas.email,
    password: validationSchemas.password,
    confirmPassword: validationSchemas.confirmPassword,
    firstName: validationSchemas.firstName,
    lastName: validationSchemas.lastName
  })
)

export const SignUpForm = () => {
  const t = useTranslations('SignUpPage')
  const tc = useTranslations('Common')
  const {
    createUser,
    isCreatingUser,
    createUserError,
    authenticate,
    isAuthenticating,
    authenticateError
  } = useAuth()
  const searchParameters = useSearchParams()
  const authError = searchParameters.get('error')
  const swiftError = auth.errors[authError] || authError

  const AlreadyExists = ({ email }) => (
    <div className='text-center'>
      {t('alreadyExists')}{' '}
      <Link className='text-primary' href={{ pathname: routes.signIn, query: { email } }}>
        {tc('signInLink')}
      </Link>
    </div>
  )

  const Errors = ({ error, email }) => {
    if (!error) {
      return null
    }

    if (error.digest === auth.errors.userAlreadyExists.digest) {
      return <ErrorDisplay error={error} component={<AlreadyExists email={email} />} />
    }

    return <ErrorDisplay error={error} />
  }

  const form = useForm({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: ''
    }
  })

  const handleSubmit = (data) => {
    createUser(data, {
      onSuccess: async () => {
        await authenticate({ credentials: data })
      }
    })
  }

  const error = createUserError || authenticateError
  const isLoading = isCreatingUser || isAuthenticating

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-8'>
        <div className='space-y-4'>
          <div className='flex flex-col lg:flex-row gap-2.5'>
            <FormField
              control={form.control}
              name='firstName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tc('firstName')}</FormLabel>
                  <FormControl>
                    <Input type='text' placeholder={tc('firstName')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='lastName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tc('lastName')}</FormLabel>
                  <FormControl>
                    <Input type='text' placeholder={tc('lastName')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('email')}</FormLabel>
                <FormControl>
                  <Input type='email' placeholder={tc('emailPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('password')}</FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder={tc('passwordPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='confirmPassword'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tc('confirmPassword')}</FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder={tc('confirmPasswordPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <Button type='submit' className='w-full' isLoading={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className='animate-spin' /> {tc('signingUp')}...
            </>
          ) : (
            tc('signUp')
          )}
        </Button>
        <Text variant='xs' className='text-center'>
          {t('termsText')}{' '}
          <Link className='text-primary' href={routes.termsOfService}>
            {tc('termsLink')}
          </Link>{' '}
          {tc('and')}{' '}
          <Link className='text-primary' href={routes.privacyPolicy}>
            {tc('privacyLink')}
          </Link>
        </Text>
        <Errors error={error || swiftError} email={form.getValues('email')} />
      </form>
    </Form>
  )
}
