// Node ESM loader to ignore CSS imports when running Node-based CLIs (e.g., payload generate:types)
// Returns an empty module for .css/.scss files so Node doesn't error on unknown extension

export async function load(url, context, defaultLoad) {
  try {
    const parsed = new URL(url)
    const pathname = parsed.pathname || url
    if (pathname.endsWith('.css') || pathname.endsWith('.scss')) {
      return {
        format: 'module',
        shortCircuit: true,
        source: 'export default {}'
      }
    }
  } catch {
    // Fallback for non-URL strings
    if (url.endsWith('.css') || url.endsWith('.scss')) {
      return {
        format: 'module',
        shortCircuit: true,
        source: 'export default {}'
      }
    }
  }

  return defaultLoad(url, context, defaultLoad)
}
