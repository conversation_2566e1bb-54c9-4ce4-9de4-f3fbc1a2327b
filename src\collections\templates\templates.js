import { isAdmin } from '@collections/access'
import { collections } from '@constants'
import { templateFields } from '@utils'

export const templates = {
  slug: collections.templates.slug,
  access: {
    create: isAdmin,
    read: isAdmin,
    update: isAdmin,
    delete: isAdmin
  },
  timestamps: true,
  admin: {
    defaultColumns: ['templateName', 'user'],
    useAsTitle: 'templateName'
  },
  fields: [
    {
      name: 'templateName',
      type: 'text',
      required: true
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: collections.users.slug,
      hasMany: false
    },
    ...templateFields
  ]
}
