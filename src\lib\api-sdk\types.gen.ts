// This file is auto-generated by @hey-api/openapi-ts

export type FindUsersResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindUsersError = unknown;

export type CreateUserData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateUserResponse = (unknown);

export type CreateUserError = unknown;

export type UpdateUsersData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateUsersResponse = (unknown);

export type UpdateUsersError = unknown;

export type DeleteUsersResponse = (void);

export type DeleteUsersError = unknown;

export type FindUserByIdData = {
    path: {
        id: string;
    };
};

export type FindUserByIdResponse = (unknown);

export type FindUserByIdError = unknown;

export type UpdateUserByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateUserByIdResponse = (unknown);

export type UpdateUserByIdError = unknown;

export type DeleteUserByIdData = {
    path: {
        id: string;
    };
};

export type DeleteUserByIdResponse = (void);

export type DeleteUserByIdError = unknown;

export type CountUsersResponse = ({
    totalDocs?: number;
});

export type CountUsersError = unknown;

export type LoginResponse = ({
    [key: string]: unknown;
});

export type LoginError = unknown;

export type LogoutResponse = ({
    [key: string]: unknown;
});

export type LogoutError = unknown;

export type UnlockData = {
    body?: {
        email?: unknown;
    };
};

export type UnlockResponse = ({
    [key: string]: unknown;
});

export type UnlockError = unknown;

export type RefreshTokenResponse = ({
    [key: string]: unknown;
});

export type RefreshTokenError = unknown;

export type CurrentUserResponse = ({
    [key: string]: unknown;
});

export type CurrentUserError = unknown;

export type ForgotPasswordData = {
    body?: {
        email?: unknown;
    };
};

export type ForgotPasswordResponse = ({
    [key: string]: unknown;
});

export type ForgotPasswordError = unknown;

export type ResetPasswordData = {
    body?: {
        token?: unknown;
        password?: unknown;
    };
};

export type ResetPasswordResponse = ({
    [key: string]: unknown;
});

export type ResetPasswordError = unknown;

export type VerifyTokenResponse = ({
    [key: string]: unknown;
});

export type VerifyTokenError = unknown;

export type FindCategoriesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindCategoriesError = unknown;

export type CreateCategoryData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateCategoryResponse = (unknown);

export type CreateCategoryError = unknown;

export type UpdateCategoriesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateCategoriesResponse = (unknown);

export type UpdateCategoriesError = unknown;

export type DeleteCategoriesResponse = (void);

export type DeleteCategoriesError = unknown;

export type FindCategoryByIdData = {
    path: {
        id: string;
    };
};

export type FindCategoryByIdResponse = (unknown);

export type FindCategoryByIdError = unknown;

export type UpdateCategoryByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateCategoryByIdResponse = (unknown);

export type UpdateCategoryByIdError = unknown;

export type DeleteCategoryByIdData = {
    path: {
        id: string;
    };
};

export type DeleteCategoryByIdResponse = (void);

export type DeleteCategoryByIdError = unknown;

export type CountCategoriesResponse = ({
    totalDocs?: number;
});

export type CountCategoriesError = unknown;

export type FindPagesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindPagesError = unknown;

export type CreatePageData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreatePageResponse = (unknown);

export type CreatePageError = unknown;

export type UpdatePagesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdatePagesResponse = (unknown);

export type UpdatePagesError = unknown;

export type DeletePagesResponse = (void);

export type DeletePagesError = unknown;

export type FindPageByIdData = {
    path: {
        id: string;
    };
};

export type FindPageByIdResponse = (unknown);

export type FindPageByIdError = unknown;

export type UpdatePageByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdatePageByIdResponse = (unknown);

export type UpdatePageByIdError = unknown;

export type DeletePageByIdData = {
    path: {
        id: string;
    };
};

export type DeletePageByIdResponse = (void);

export type DeletePageByIdError = unknown;

export type CountPagesResponse = ({
    totalDocs?: number;
});

export type CountPagesError = unknown;

export type FindPostsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindPostsError = unknown;

export type CreatePostData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreatePostResponse = (unknown);

export type CreatePostError = unknown;

export type UpdatePostsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdatePostsResponse = (unknown);

export type UpdatePostsError = unknown;

export type DeletePostsResponse = (void);

export type DeletePostsError = unknown;

export type FindPostByIdData = {
    path: {
        id: string;
    };
};

export type FindPostByIdResponse = (unknown);

export type FindPostByIdError = unknown;

export type UpdatePostByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdatePostByIdResponse = (unknown);

export type UpdatePostByIdError = unknown;

export type DeletePostByIdData = {
    path: {
        id: string;
    };
};

export type DeletePostByIdResponse = (void);

export type DeletePostByIdError = unknown;

export type CountPostsResponse = ({
    totalDocs?: number;
});

export type CountPostsError = unknown;

export type FindMediaResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindMediaError = unknown;

export type CreateMediaData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateMediaResponse = (unknown);

export type CreateMediaError = unknown;

export type UpdateMediaData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateMediaResponse = (unknown);

export type UpdateMediaError = unknown;

export type DeleteMediaResponse = (void);

export type DeleteMediaError = unknown;

export type FindMediaByIdData = {
    path: {
        id: string;
    };
};

export type FindMediaByIdResponse = (unknown);

export type FindMediaByIdError = unknown;

export type UpdateMediaByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateMediaByIdResponse = (unknown);

export type UpdateMediaByIdError = unknown;

export type DeleteMediaByIdData = {
    path: {
        id: string;
    };
};

export type DeleteMediaByIdResponse = (void);

export type DeleteMediaByIdError = unknown;

export type CountMediaResponse = ({
    totalDocs?: number;
});

export type CountMediaError = unknown;

export type FindTemplatesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindTemplatesError = unknown;

export type CreateTemplateData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateTemplateResponse = (unknown);

export type CreateTemplateError = unknown;

export type UpdateTemplatesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateTemplatesResponse = (unknown);

export type UpdateTemplatesError = unknown;

export type DeleteTemplatesResponse = (void);

export type DeleteTemplatesError = unknown;

export type FindTemplateByIdData = {
    path: {
        id: string;
    };
};

export type FindTemplateByIdResponse = (unknown);

export type FindTemplateByIdError = unknown;

export type UpdateTemplateByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateTemplateByIdResponse = (unknown);

export type UpdateTemplateByIdError = unknown;

export type DeleteTemplateByIdData = {
    path: {
        id: string;
    };
};

export type DeleteTemplateByIdResponse = (void);

export type DeleteTemplateByIdError = unknown;

export type CountTemplatesResponse = ({
    totalDocs?: number;
});

export type CountTemplatesError = unknown;

export type FindResumesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindResumesError = unknown;

export type CreateResumeData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateResumeResponse = (unknown);

export type CreateResumeError = unknown;

export type UpdateResumesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateResumesResponse = (unknown);

export type UpdateResumesError = unknown;

export type DeleteResumesResponse = (void);

export type DeleteResumesError = unknown;

export type FindResumeByIdData = {
    path: {
        id: string;
    };
};

export type FindResumeByIdResponse = (unknown);

export type FindResumeByIdError = unknown;

export type UpdateResumeByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateResumeByIdResponse = (unknown);

export type UpdateResumeByIdError = unknown;

export type DeleteResumeByIdData = {
    path: {
        id: string;
    };
};

export type DeleteResumeByIdResponse = (void);

export type DeleteResumeByIdError = unknown;

export type CountResumesResponse = ({
    totalDocs?: number;
});

export type CountResumesError = unknown;

export type FindResumeExamplesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindResumeExamplesError = unknown;

export type CreateResumeExampleData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateResumeExampleResponse = (unknown);

export type CreateResumeExampleError = unknown;

export type UpdateResumeExamplesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateResumeExamplesResponse = (unknown);

export type UpdateResumeExamplesError = unknown;

export type DeleteResumeExamplesResponse = (void);

export type DeleteResumeExamplesError = unknown;

export type FindResumeExampleByIdData = {
    path: {
        id: string;
    };
};

export type FindResumeExampleByIdResponse = (unknown);

export type FindResumeExampleByIdError = unknown;

export type UpdateResumeExampleByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateResumeExampleByIdResponse = (unknown);

export type UpdateResumeExampleByIdError = unknown;

export type DeleteResumeExampleByIdData = {
    path: {
        id: string;
    };
};

export type DeleteResumeExampleByIdResponse = (void);

export type DeleteResumeExampleByIdError = unknown;

export type CountResumeExamplesResponse = ({
    totalDocs?: number;
});

export type CountResumeExamplesError = unknown;

export type FindProductsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindProductsError = unknown;

export type CreateProductData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateProductResponse = (unknown);

export type CreateProductError = unknown;

export type UpdateProductsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateProductsResponse = (unknown);

export type UpdateProductsError = unknown;

export type DeleteProductsResponse = (void);

export type DeleteProductsError = unknown;

export type FindProductByIdData = {
    path: {
        id: string;
    };
};

export type FindProductByIdResponse = (unknown);

export type FindProductByIdError = unknown;

export type UpdateProductByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateProductByIdResponse = (unknown);

export type UpdateProductByIdError = unknown;

export type DeleteProductByIdData = {
    path: {
        id: string;
    };
};

export type DeleteProductByIdResponse = (void);

export type DeleteProductByIdError = unknown;

export type CountProductsResponse = ({
    totalDocs?: number;
});

export type CountProductsError = unknown;

export type FindSubscriptionsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindSubscriptionsError = unknown;

export type CreateSubscriptionData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateSubscriptionResponse = (unknown);

export type CreateSubscriptionError = unknown;

export type UpdateSubscriptionsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateSubscriptionsResponse = (unknown);

export type UpdateSubscriptionsError = unknown;

export type DeleteSubscriptionsResponse = (void);

export type DeleteSubscriptionsError = unknown;

export type FindSubscriptionByIdData = {
    path: {
        id: string;
    };
};

export type FindSubscriptionByIdResponse = (unknown);

export type FindSubscriptionByIdError = unknown;

export type UpdateSubscriptionByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateSubscriptionByIdResponse = (unknown);

export type UpdateSubscriptionByIdError = unknown;

export type DeleteSubscriptionByIdData = {
    path: {
        id: string;
    };
};

export type DeleteSubscriptionByIdResponse = (void);

export type DeleteSubscriptionByIdError = unknown;

export type CountSubscriptionsResponse = ({
    totalDocs?: number;
});

export type CountSubscriptionsError = unknown;

export type FindRedirectsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindRedirectsError = unknown;

export type CreateRedirectData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateRedirectResponse = (unknown);

export type CreateRedirectError = unknown;

export type UpdateRedirectsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateRedirectsResponse = (unknown);

export type UpdateRedirectsError = unknown;

export type DeleteRedirectsResponse = (void);

export type DeleteRedirectsError = unknown;

export type FindRedirectByIdData = {
    path: {
        id: string;
    };
};

export type FindRedirectByIdResponse = (unknown);

export type FindRedirectByIdError = unknown;

export type UpdateRedirectByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateRedirectByIdResponse = (unknown);

export type UpdateRedirectByIdError = unknown;

export type DeleteRedirectByIdData = {
    path: {
        id: string;
    };
};

export type DeleteRedirectByIdResponse = (void);

export type DeleteRedirectByIdError = unknown;

export type CountRedirectsResponse = ({
    totalDocs?: number;
});

export type CountRedirectsError = unknown;

export type FindFormsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindFormsError = unknown;

export type CreateFormData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateFormResponse = (unknown);

export type CreateFormError = unknown;

export type UpdateFormsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateFormsResponse = (unknown);

export type UpdateFormsError = unknown;

export type DeleteFormsResponse = (void);

export type DeleteFormsError = unknown;

export type FindFormByIdData = {
    path: {
        id: string;
    };
};

export type FindFormByIdResponse = (unknown);

export type FindFormByIdError = unknown;

export type UpdateFormByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateFormByIdResponse = (unknown);

export type UpdateFormByIdError = unknown;

export type DeleteFormByIdData = {
    path: {
        id: string;
    };
};

export type DeleteFormByIdResponse = (void);

export type DeleteFormByIdError = unknown;

export type CountFormsResponse = ({
    totalDocs?: number;
});

export type CountFormsError = unknown;

export type FindFormSubmissionsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindFormSubmissionsError = unknown;

export type CreateFormSubmissionData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateFormSubmissionResponse = (unknown);

export type CreateFormSubmissionError = unknown;

export type UpdateFormSubmissionsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateFormSubmissionsResponse = (unknown);

export type UpdateFormSubmissionsError = unknown;

export type DeleteFormSubmissionsResponse = (void);

export type DeleteFormSubmissionsError = unknown;

export type FindFormSubmissionByIdData = {
    path: {
        id: string;
    };
};

export type FindFormSubmissionByIdResponse = (unknown);

export type FindFormSubmissionByIdError = unknown;

export type UpdateFormSubmissionByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateFormSubmissionByIdResponse = (unknown);

export type UpdateFormSubmissionByIdError = unknown;

export type DeleteFormSubmissionByIdData = {
    path: {
        id: string;
    };
};

export type DeleteFormSubmissionByIdResponse = (void);

export type DeleteFormSubmissionByIdError = unknown;

export type CountFormSubmissionsResponse = ({
    totalDocs?: number;
});

export type CountFormSubmissionsError = unknown;

export type FindSearchResultsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindSearchResultsError = unknown;

export type CreateSearchResultData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateSearchResultResponse = (unknown);

export type CreateSearchResultError = unknown;

export type UpdateSearchResultsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateSearchResultsResponse = (unknown);

export type UpdateSearchResultsError = unknown;

export type DeleteSearchResultsResponse = (void);

export type DeleteSearchResultsError = unknown;

export type FindSearchResultByIdData = {
    path: {
        id: string;
    };
};

export type FindSearchResultByIdResponse = (unknown);

export type FindSearchResultByIdError = unknown;

export type UpdateSearchResultByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateSearchResultByIdResponse = (unknown);

export type UpdateSearchResultByIdError = unknown;

export type DeleteSearchResultByIdData = {
    path: {
        id: string;
    };
};

export type DeleteSearchResultByIdResponse = (void);

export type DeleteSearchResultByIdError = unknown;

export type CountSearchResultsResponse = ({
    totalDocs?: number;
});

export type CountSearchResultsError = unknown;

export type FindPayloadJobsResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindPayloadJobsError = unknown;

export type CreatePayloadJobData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreatePayloadJobResponse = (unknown);

export type CreatePayloadJobError = unknown;

export type UpdatePayloadJobsData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdatePayloadJobsResponse = (unknown);

export type UpdatePayloadJobsError = unknown;

export type DeletePayloadJobsResponse = (void);

export type DeletePayloadJobsError = unknown;

export type FindPayloadJobByIdData = {
    path: {
        id: string;
    };
};

export type FindPayloadJobByIdResponse = (unknown);

export type FindPayloadJobByIdError = unknown;

export type UpdatePayloadJobByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdatePayloadJobByIdResponse = (unknown);

export type UpdatePayloadJobByIdError = unknown;

export type DeletePayloadJobByIdData = {
    path: {
        id: string;
    };
};

export type DeletePayloadJobByIdResponse = (void);

export type DeletePayloadJobByIdError = unknown;

export type CountPayloadJobsResponse = ({
    totalDocs?: number;
});

export type CountPayloadJobsError = unknown;

export type RequestOtpData = {
    body: {
        /**
         * Email address to send OTP to
         */
        email: string;
    };
};

export type RequestOtpResponse = ({
    message?: string;
});

export type RequestOtpError = ({
    /**
     * Error details
     */
    error?: {
        [key: string]: unknown;
    };
});

export type VerifyOtpData = {
    body: {
        /**
         * Email address associated with the OTP
         */
        email: string;
        /**
         * 6-digit OTP code
         */
        otp: string;
    };
};

export type VerifyOtpResponse = ({
    message?: string;
});

export type VerifyOtpError = ({
    error?: 'Invalid OTP' | 'User not found';
});

export type GetCountryBasedProductsListData = {
    query?: {
        /**
         * Country code (e.g., US, GB, DE)
         */
        country?: string;
        /**
         * Currency code (e.g., usd, eur, gbp)
         */
        currency?: string;
    };
};

export type GetCountryBasedProductsListResponse = ({
    country?: string;
    currency?: string;
    symbol?: string;
    products?: Array<{
        id?: string;
        title?: string;
        description?: string;
        priceJSON?: {
            [key: string]: unknown;
        };
        trialpriceJSON?: {
            [key: string]: unknown;
        };
        stripeProductID?: string;
        trialStripeProductID?: string;
        isPopular?: boolean;
        trialPeriod?: number;
        createdAt?: string;
        updatedAt?: string;
    }>;
    total?: number;
});

export type GetCountryBasedProductsListError = ({
    error?: string;
});