# Port. Don't change unless you know what you're doing. Referenced in nginx
PORT=4000

# Database connection string
DATABASE_URI=mongodb://localhost:27017

# Used to encrypt JWT tokens (should be a 24 character random string)
PAYLOAD_SECRET=fafb2de7cd6f4676a6f2f0e0

# URLs
NEXT_PUBLIC_URL=https://swift.local

# Allow robots to index the site (optional), set to true in prod
NEXT_PUBLIC_IS_LIVE=0

# Don't sent data to Vercel
VERCEL_TELEMETRY_DISABLED=1
NEXT_TELEMETRY_DISABLED=1

# Resend
RESEND_API_KEY=resend_api_key
RESEND_FROM_EMAIL=from_email
RESEND_FROM_NAME=Swift Resume

# LinkedIn
LINKEDIN_CLIENT_ID="client_id"
LINKEDIN_CLIENT_SECRET="client_secret"

# Digital Ocean space storage
DO_S3_SECRET_KEY="secrate"
DO_S3_ACCESS_KEY="access"
DO_S3_ENDPOINT="https://nyc3.digitaloceanspaces.com"
DO_S3_BUCKET="swiftresume"
DO_S3_REGION="nyc3"
DO_CDN_ENDPOINT="https://swiftresume.nyc3.cdn.digitaloceanspaces.com"

#stripe
STRIPE_SECRET_KEY="secret_key"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="publishable_key"
STRIPE_WEBHOOK_SECRET="webhook_secret"