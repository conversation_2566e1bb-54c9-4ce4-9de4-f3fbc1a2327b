import { addDataAndFileToRequest } from 'payload'
import { encrypt } from '../../../utils/encrypt.js'

export const verifyOtp = {
  path: '/verify-otp',
  method: 'post',
  summary: 'Verify OTP',
  handler: async (request) => {
    await addDataAndFileToRequest(request)
    const { email, otp } = request.data
    const { payload } = request // Encrypt the user-provided OTP for comparison
    const encryptedOtp = encrypt({ payload, value: otp })

    const otpDocument = await payload.find({
      collection: 'otp',
      where: {
        email: {
          equals: email
        },
        otp: {
          equals: encryptedOtp
        },
        expiresAt: {
          greaterThan: new Date().toISOString()
        }
      }
    })
    if (!otpDocument.docs?.length) {
      return Response.json({ error: 'Invalid OTP' }, { status: 400 })
    }

    await payload.delete({
      collection: 'otp',
      id: otpDocument.docs[0].id
    })

    const user = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email
        }
      }
    })

    if (!user.docs?.length) {
      return Response.json({ error: 'User not found' }, { status: 400 })
    }

    await payload.update({
      collection: 'users',
      id: user.docs[0].id,
      data: {
        role: 'member'
      }
    })

    return Response.json({ message: 'OTP verified' })
  }
}
