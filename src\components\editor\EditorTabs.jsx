'use client'

import { useMemo, useState } from 'react'
import { Brush, SquarePen } from 'lucide-react'
import { useTranslations } from 'next-intl'
import {
  DesignPanel,
  Drawer,
  DrawerContent,
  DrawerDescription,
  Drawer<PERSON><PERSON>le,
  Drawer<PERSON>eader,
  DrawerNestedRoot,
  DrawerTitle,
  EditorContent,
  EditorNavigation,
  EditorPanel,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@components'
import { useEditor } from '@hooks'
import { useMediaQuery } from '@raddix/use-media-query'
import { cn } from '@utils'

const tabs = [
  {
    label: 'editor',
    icon: SquarePen,
    component: () => <EditorPanel />
  },
  {
    label: 'design',
    icon: Brush,
    component: () => <DesignPanel />
  }
]

const TabList = ({ baseClassName }) => {
  const tc = useTranslations('Common')
  return (
    <TabsList
      variant='rounded'
      className='flex-shrink-0 px-4 lg:px-4.5 flex gap-4 lg:border-b border-slate-300 justify-end'
      baseClassName={baseClassName}>
      <div className='flex bg-slate-100 border border-slate-200 rounded-lg my-3 p-0.5'>
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.label}
            value={tab.label}
            className='px-3 py-1 h-7 gap-1 w-1/2 lg:w-auto relative text-slate-500 data-[state=active]:text-slate-700 leading-5'>
            <tab.icon size={16} />
            {tc(tab.label)}
          </TabsTrigger>
        ))}
      </div>
    </TabsList>
  )
}

const TabContent = ({ activeTab }) =>
  tabs.map((tab) => {
    const TabComponent = tab.component
    return (
      <TabsContent
        forceMount={true}
        key={`${tab.label}-panel`}
        value={tab.label}
        className={cn('mt-0 overflow-y-auto thin-scrollbar h-full', {
          block: tab.label === activeTab,
          hidden: tab.label !== activeTab
        })}>
        <TabComponent />
      </TabsContent>
    )
  })

const TabContainer = ({ activeTab, setActiveTab, className, children }) => (
  <Tabs
    defaultValue='editor'
    className={cn(className, 'test')}
    value={activeTab}
    onValueChange={(value) => setActiveTab(value)}>
    {children}
  </Tabs>
)

const snapPoints = ['280px', '490px', 1]

export const EditorTabs = () => {
  const [snap, setSnap] = useState(snapPoints[0])
  const { activeTab, setActiveTab, isSheetActive, activePanelId, handleBack } =
    useEditor()

  const desktopTabList = useMemo(
    () => <TabList activeTab={activeTab} setActiveTab={setActiveTab} />,
    [activeTab, setActiveTab]
  )

  const mobileTabList = useMemo(
    () => (
      <TabList
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        baseClassName='absolute top-[14px] right-0'
      />
    ),
    [activeTab, setActiveTab]
  )

  const tabContent = useMemo(() => <TabContent activeTab={activeTab} />, [activeTab])

  const isDesktop = useMediaQuery('(min-width: 1024px)')
  return isDesktop ? (
    <>
      <TabContainer
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        className='h-full hidden flex-col lg:flex'>
        {desktopTabList}
        {tabContent}
      </TabContainer>
    </>
  ) : (
    <>
      <Drawer
        open={true}
        snapPoints={snapPoints}
        activeSnapPoint={snap}
        setActiveSnapPoint={setSnap}
        handleOnly
        modal={false}>
        <DrawerContent
          className='h-full max-h-[85%] outline-none'
          contentClassName='hidden'>
          <DrawerHandle>
            <div className='absolute bg-zinc-100 w-16 h-1.5 mt-2 top-0 left-1/2 -translate-x-1/2 rounded-xl' />
          </DrawerHandle>
          <DrawerHeader className='sr-only'>
            <DrawerTitle>Editor</DrawerTitle>
            <DrawerDescription>Editor</DrawerDescription>
          </DrawerHeader>
          <div className='overflow-y-auto thin-scrollbar h-full'>
            <TabContainer
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              className='h-full flex flex-col'>
              {mobileTabList}
              {tabContent}
            </TabContainer>
            <DrawerNestedRoot
              open={isSheetActive}
              onOpenChange={(open) => {
                if (!open) {
                  handleBack()
                }
              }}>
              <DrawerContent
                className='h-full max-h-[80%] outline-none bg-white border-t border-gray-200 shadow-lg'
                contentClassName='relative bg-slate-200'>
                <div className='absolute bg-slate-50 w-full h-3.5 rounded-xl -z-10' />
                <DrawerHeader className='p-0'>
                  <EditorNavigation />
                  <DrawerTitle className='sr-only'>Editor Content</DrawerTitle>
                  <DrawerDescription className='sr-only'>
                    Editor Sheet Content
                  </DrawerDescription>
                </DrawerHeader>
                <div className='flex-1 overflow-y-auto thin-scrollbar p-4'>
                  <EditorContent activePanelId={activePanelId} />
                </div>
              </DrawerContent>
            </DrawerNestedRoot>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}
