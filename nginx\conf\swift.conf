server {
  listen  80;
  server_name swift.local;

  return 301 https://$host$request_uri;
}

map $http_upgrade $connection_upgrade {
  default upgrade;
  ''  close;
}

server {
  listen                443 ssl;
  http2                 on;
  server_name           swift.local;
  ssl_certificate       /etc/nginx/ssl/swift.local.cert.pem;
  ssl_certificate_key   /etc/nginx/ssl/swift.local.key.pem;

  client_max_body_size 5M;

  location / {
    proxy_pass                    https://host.docker.internal:4000;
    proxy_set_header              X-Forwarded-For $remote_addr;
    proxy_set_header              Host $http_host;
    proxy_set_header Upgrade      $http_upgrade;
    proxy_set_header Connection   $connection_upgrade;
  }
}