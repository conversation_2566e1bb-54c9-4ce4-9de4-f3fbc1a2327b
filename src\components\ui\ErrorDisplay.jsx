'use client'

import { useTranslations } from 'next-intl'
import { cn } from '@utils'
import { textVariants } from './Text'

export const ErrorDisplay = ({ error, component }) => {
  const t = useTranslations('Errors')

  console.log('error', error)

  if (!error) {
    return null
  }

  let message = t('unexpectedError') // Default fallback

  // Handle different error types
  if (typeof error === 'string') {
    // If error is just a string, try to use it as a key
    try {
      message = t(error) || error
    } catch {
      message = error
    }
  } else if (error?.digest) {
    // If error has a digest property, use it as translation key
    try {
      message = t(error.digest) || t('unexpectedError')
    } catch {
      message = t('unexpectedError')
    }
  } else if (error?.message) {
    // If error has a message property, use it directly
    message = error.message
  }

  return (
    <div
      className={cn(textVariants({ variant: 'xs', weight: 'medium' }), 'text-red-500')}>
      {component || <>{message}</>}
    </div>
  )
}
