import { parseDocument } from 'htmlparser2'

export const parseHtmlToObjectArray = (htmlString) => {
  try {
    const document = parseDocument(htmlString)
    return parseNode(document)
  } catch (error) {
    console.error('Error parsing HTML:', error)
    return []
  }
}

const parseNode = (node) => {
  if (!node) return null

  if (node.type === 'text') {
    const text = node.data?.trim()
    return text ? { type: 'text', content: text } : null
  }

  if (node.type === 'tag') {
    const element = {
      type: 'element',
      tagName: node.name,
      attributes: node.attribs || {},
      styles: parseInlineStyles(node.attribs?.style || ''),
      children: []
    }

    if (node.children && node.children.length > 0) {
      element.children = node.children
        .map((child) => parseNode(child))
        .filter((child) => child !== null)
    }

    return element
  }

  if (node.type === 'root' && node.children) {
    return node.children
      .map((child) => parseNode(child))
      .filter((child) => child !== null)
  }

  return null
}

const parseInlineStyles = (styleString) => {
  if (!styleString) return {}

  const styles = {}

  try {
    for (const rule of styleString.split(';')) {
      const [property, value] = rule.split(':').map((s) => s.trim())
      if (property && value) {
        const camelProperty = property.replaceAll(/-([a-z])/g, (_, letter) =>
          letter.toUpperCase()
        )
        styles[camelProperty] = value
      }
    }
  } catch (error) {
    console.warn('Error parsing styles:', styleString, error)
  }

  return styles
}

export const isTextElement = (tagName) => {
  return ['span', 'p', 'li', 'strong', 'em', 'u', 's', 'a'].includes(tagName)
}

export const extractPagesContent = (pageElements) => {
  const pages = []

  for (const [index, pageElement] of pageElements.entries()) {
    try {
      const htmlContent = pageElement.outerHTML
      const parsedContent = parseHtmlToObjectArray(htmlContent)

      pages.push({
        pageIndex: index,
        content: parsedContent,
        rawHtml: htmlContent
      })
    } catch (error) {
      console.error(`Error parsing page ${index}:`, error)
    }
  }

  return pages
}
