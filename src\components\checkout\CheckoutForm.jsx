'use client'

import { useMemo, useState } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import { isAnon } from '@access'
import { Button, Input, Label, SeparatorWithText } from '@components'
import { StripeElementsProvider } from '@components/stripe'
import { countryToLocaleMap } from '@constants'
import { useAuth, useSubscription } from '@hooks'
import {
  ExpressCheckoutElement,
  PaymentElement,
  useElements,
  useStripe
} from '@stripe/react-stripe-js'
import { appendSearchParams, cn, delay } from '@utils'

export const PaymentForm = () => {
  const t = useTranslations('CheckoutPage')
  const tc = useTranslations('Common')
  const te = useTranslations('Errors')
  const { currentUser, refetchCurrentUser } = useAuth()
  const pathname = usePathname()
  const { subscriptionState, updateSubscriptionState } = useSubscription()
  const { selectedPlan, currency, paymentMessage } = subscriptionState
  const stripe = useStripe()
  const elements = useElements()
  const [errorMessage, setErrorMessage] = useState()
  const [loading, setLoading] = useState(false)
  const email = isAnon(currentUser?.role) ? '' : currentUser?.email
  const [emailValue, setEmailValue] = useState(email)

  const { priceData, price } = useMemo(() => {
    if (!selectedPlan) return null
    const priceData = selectedPlan.priceJSON['data'][0]
    const currencyOptions = priceData.currency_options
    const price = currencyOptions[currency]
    return { priceData, price }
  }, [selectedPlan, currency])

  const trialPlan = useMemo(() => {
    if (
      !selectedPlan ||
      !selectedPlan.trialpriceJSON ||
      selectedPlan.trialpriceJSON === ''
    )
      return null
    const trialPriceData = selectedPlan.trialpriceJSON['data'][0]
    const currencyOptions = trialPriceData.currency_options
    const trialPrice = currencyOptions[currency]
    return { trialPriceData, trialPrice }
  }, [selectedPlan, currency])

  const trialPriceData = trialPlan?.trialPriceData
  const trialPrice = trialPlan?.trialPrice

  const paymentOptions = useMemo(() => {
    if (!priceData) return {}
    return {
      mode: 'subscription',
      amount: price.unit_amount,
      currency: currency,
      subscription_data: {
        items: [{ price: priceData.id }]
      },
      trialPlan: trialPriceData?.id
    }
  }, [priceData, price, currency, trialPriceData])

  const handleError = (error) => {
    setLoading(false)
    setErrorMessage(error)
  }

  const handleSubmit = async (event) => {
    if (event?.elementType !== 'expressCheckout') {
      event.preventDefault()
    }

    const isExpressCheckout = event?.elementType === 'expressCheckout'

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      // disable form submission until Stripe.js has loaded.
      return
    }

    if (errorMessage !== '') {
      setErrorMessage('')
    }

    if (paymentMessage !== '') {
      updateSubscriptionState({
        type: 'updateState',
        payload: {
          paymentMessage: ''
        }
      })
    }

    setLoading(true)

    // Trigger form validation and wallet collection
    const { error: submitError } = await elements.submit()
    if (submitError) {
      handleError(submitError.message)
      return
    }

    const finalEmail = isExpressCheckout ? event?.billingDetails?.email : emailValue

    let payload = {
      ...paymentOptions,
      email: finalEmail,
      userId: currentUser?.id,
      stripeCustomerId: currentUser?.stripeCustomerId
    }

    if (payload.email === '' || !payload.email) {
      handleError(te('noEmail'))
      return
    }

    // Create the subscription
    const response = await fetch('/api/create-subscription', {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const {
      error: subscriptionError,
      type,
      clientSecret,
      subscriptionId
    } = await response.json()

    await delay(1000)

    refetchCurrentUser()

    if (!clientSecret) {
      handleError(subscriptionError)
      return
    }
    const confirmIntent = type === 'setup' ? stripe.confirmSetup : stripe.confirmPayment

    let redirectUrl = localStorage?.getItem('redirect') || pathname
    let returnBaseUrl = `${process.env.NEXT_PUBLIC_URL}${redirectUrl}`

    let returnUrl = appendSearchParams(returnBaseUrl, 'subscriptionId', subscriptionId)

    // Confirm the Intent using the details collected by the Payment Element
    const { error } = await confirmIntent({
      elements,
      clientSecret,
      confirmParams: {
        return_url: returnUrl
      }
    })

    if (error) {
      handleError(error.message)
      setLoading(false)
      return
    }

    setLoading(false)
  }

  const paymentDefaultValues = {
    billingDetails: {
      email: emailValue
    }
  }

  // const clearErrorAndRetry = () => {
  //     setPaymentError(null);
  // };

  // const clearSubscriptionAndRetry = () => {
  //     router.refresh();
  //     setAlreadySubscribed(null);
  // };

  const expressCheckoutOptions = {
    wallets: {
      applePay: 'always',
      googlePay: 'always'
    },
    buttonType: {
      googlePay: 'plain',
      applePay: 'plain'
    },
    buttonHeight: 50,
    emailRequired: true
  }

  const finalPrice = selectedPlan?.trialpriceJSON ? trialPrice : price

  const onClick = ({ resolve }) => {
    const options = {
      lineItems: [
        {
          name: `${selectedPlan.title}`,
          amount: finalPrice.unit_amount
        }
      ],
      applePay: {
        recurringPaymentRequest: {
          paymentDescription: selectedPlan?.description || '',
          managementURL: process.env.NEXT_PUBLIC_URL + '/dashboard/billing',
          regularBilling: {
            amount: price.unit_amount,
            label: selectedPlan.title,
            recurringPaymentIntervalUnit: priceData.recurring.interval,
            recurringPaymentIntervalCount: priceData.recurring.interval_count
          }
        }
      }
    }

    resolve(options)
  }

  return (
    <div className={cn('space-y-5')}>
      <ExpressCheckoutElement
        options={expressCheckoutOptions}
        onClick={onClick}
        onConfirm={handleSubmit}
      />
      <SeparatorWithText className='py-3' text={t('OR')} textVariant='card' size='sm' />
      <form className='space-y-4' onSubmit={handleSubmit}>
        <div className='space-y-3'>
          <div className='space-y-1.5'>
            <Label htmlFor='email'>{tc('email')}</Label>
            <Input
              placeholder='<EMAIL>'
              type='email'
              name='email'
              value={emailValue}
              disabled={!!email}
              onChange={(event) => setEmailValue(event.target.value)}
            />
          </div>
          <PaymentElement
            className='mb-6'
            options={{
              defaultValues: paymentDefaultValues,
              terms: {
                card: 'never',
                googlePay: 'never',
                applePay: 'never',
                paypal: 'never'
              },
              layout: 'tabs',
              wallets: 'never'
            }}
          />
        </div>
        <div>
          <Button
            disabled={loading || !stripe || !elements}
            type='submit'
            size='xl'
            className='w-full'>
            {loading ? (
              <Loader2 className='w-4 h-4 text-white animate-spin' />
            ) : (
              t('startNow')
            )}
          </Button>
          {errorMessage && (
            <div
              className='text-red-600 text-sm text-center font-medium my-3'
              id='payment-message'>
              {errorMessage || paymentMessage}
            </div>
          )}
        </div>
      </form>
      {loading && <div className='absolute inset-0 z-10 overflow-hidden rounded-md' />}
    </div>
  )
}

export const CheckoutForm = () => {
  const { subscriptionState } = useSubscription()
  const { country } = subscriptionState
  const subscriptionOptions = {
    payment_method_configuration: 'pmc_1RnaEMQZXKaa7MQrhOPa2Tav'
  }

  const stripeLocale = countryToLocaleMap[country] || 'en'

  return (
    <StripeElementsProvider
      subscriptionOptions={subscriptionOptions}
      locale={stripeLocale}>
      <PaymentForm />
    </StripeElementsProvider>
  )
}
