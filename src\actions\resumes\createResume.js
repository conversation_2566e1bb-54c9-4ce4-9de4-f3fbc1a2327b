'use server'

import { revalidatePath } from 'next/cache'
import { api } from '@api'
import { resumes, routes } from '@constants'
import { getCurrentUser } from '@context'
import { SwiftError } from '@error'
import { excludeKeys } from '@utils'

const excludedKeys = ['updatedAt', 'createdAt']

export const createResume = async (payload) => {
  const user = await getCurrentUser()

  if (!user) {
    console.error('No user when trying creating resume')
    throw new SwiftError(resumes.errors.createError)
  }

  const cleanedData = excludeKeys(payload, excludedKeys)

  const { data, error } = await api.createResume({
    body: {
      user: user.id,
      ...cleanedData
    }
  })

  if (error) {
    console.error('Error creating resume', error)
    throw new SwiftError(resumes.errors.createError)
  }

  revalidatePath(routes.dashboard)

  return data.doc
}
