'use client'

import { useEffect, useState } from 'react'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  Text
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'

// import { randomId } from '@utils'

const formSchema = z.object({
  otp: z.string().min(6, { message: 'OTP must be 6 digits' }),
  email: z.string().nonempty('Email is required').email('Invalid email')
})

export function VerifyOtpForm({ email, setCurrentStep }) {
  const { verifyOtp, isVerifyingOtp, refetchCurrentUser } = useAuth()

  // const sixDigitRandomTestNumber = randomId().slice(-6).padStart(6, '0')
  const t = useTranslations('VerifyOtp')
  const tc = useTranslations('Common')
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: '',
      email: email ?? ''
    }
  })

  const onSubmit = (data) => {
    verifyOtp(
      { email: data.email, otp: data.otp },
      {
        onSuccess: () => {
          refetchCurrentUser()
          setCurrentStep('download')
        },
        onError: (error) => {
          toast.error(error.message)
        }
      }
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=''>
        <FormField
          control={form.control}
          name='otp'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='mt-6 text-neutral-900'>{t('code')}</FormLabel>
              <FormControl className='mt-1.5'>
                <InputOTP maxLength={6} containerClassName='justify-start' {...field}>
                  <InputOTPGroup>
                    <InputOTPSlot
                      index={0}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                    <InputOTPSlot
                      index={1}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                    <InputOTPSlot
                      index={2}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                    <InputOTPSlot
                      index={3}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                    <InputOTPSlot
                      index={4}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                    <InputOTPSlot
                      index={5}
                      className='border-zinc-200 size-10 text-lg shadow-none'
                    />
                  </InputOTPGroup>
                </InputOTP>
              </FormControl>
              <FormMessage className='text-white text-center' />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => <Input type='hidden' {...field} />}
        />
        <Text className='text-gray-500 mt-1.5'>
          {t('didntGetCode')} <ResendCodeButton email={email} />
        </Text>
        <div className='mt-5'>
          <Button
            size='lg'
            type='submit'
            className='w-full h-10 rounded-xl'
            disabled={isVerifyingOtp}>
            {isVerifyingOtp ? <Loader2 className='animate-spin' /> : tc('continue')}
          </Button>
        </div>
      </form>
    </Form>
  )
}

export function ResendCodeButton({ email }) {
  const t = useTranslations('VerifyOtp')
  const [cooldownTime, setCooldownTime] = useState(26)
  const { requestOtp, isRequestingOtp, currentUser } = useAuth()

  const handleResend = () => {
    if (cooldownTime > 0) return
    requestOtp(
      { email: email, firstName: currentUser?.firstName },
      {
        onSuccess: () => {
          toast.success('Code sent successfully')
          setCooldownTime(26)
        },
        onError: (error) => {
          toast.error(error.message)
        }
      }
    )
  }

  useEffect(() => {
    let timer
    if (cooldownTime > 0) {
      timer = setInterval(() => {
        setCooldownTime((previousTime) => previousTime - 1)
      }, 1000)
    } else {
      clearInterval(timer)
    }
    return () => clearInterval(timer)
  }, [cooldownTime])

  return (
    <Button
      variant={cooldownTime > 0 ? 'subtleprimary' : 'subtle'}
      size='link'
      type='button'
      onClick={handleResend}
      disabled={isRequestingOtp}>
      {cooldownTime > 0 ? t('resendAgain', { cooldownTime }) : t('resend')}
    </Button>
  )
}
